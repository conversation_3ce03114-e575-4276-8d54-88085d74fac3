.PHONY: init
init:
	go install github.com/google/wire/cmd/wire@latest
	go install github.com/golang/mock/mockgen@latest
	go install github.com/swaggo/swag/cmd/swag@latest
	go get github.com/swaggo/swag@v1.16.3

.PHONY: swag-cms
swag-cms:
	swag init --dir cmd/cms,internal/handler/cms -o ./docs --instanceName cms --parseDependency

.PHONY: swag-authcenter
swag-authcenter:
	swag init --dir cmd/authcenter,internal/handler/authcenter -o ./docs --instanceName authcenter --parseDependency

.PHONY: swag-calendar
swag-calendar:
	swag init --dir cmd/calendar,internal/handler/calendar -o ./docs --instanceName calendar --parseDependency

.PHONY: swag-fortune
swag-fortune:
	swag init --dir cmd/fortune,internal/handler/fortune -o ./docs --instanceName fortune --parseDependency

.PHONY: swag-wealth
swag-wealth:
	swag init --dir cmd/wealth,internal/handler/wealth -o ./docs --instanceName wealth --parseDependency

.PHONY: swag-master
swag-master:
	swag init --dir cmd/master,internal/handler/master -o ./docs --instanceName master --parseDependency

.PHONY: swag-adflow
swag-adflow:
	swag init --dir cmd/adflow,internal/handler/adflow -o ./docs --instanceName adflow --parseDependency

.PHONY: swag
swag: swag-cms swag-calendar swag-fortune swag-wealth swag-master swag-adflow swag-authcenter

.PHONY: wire-cms
wire-cms:
	wire gen ./cmd/cms/wire

.PHONY: wire-authcenter
wire-authcenter:
	wire gen ./cmd/authcenter/wire

.PHONY: wire-calendar
wire-calendar:
	wire gen ./cmd/calendar/wire

.PHONY: wire-fortune
wire-fortune:
	wire gen ./cmd/fortune/wire

.PHONY: wire-wealth
wire-wealth:
	wire gen ./cmd/wealth/wire

.PHONY: wire-master
wire-master:
	wire gen ./cmd/master/wire

.PHONY: wire-adflow
wire-adflow:
	wire gen ./cmd/adflow/wire

.PHONY: wire
wire: wire-cms wire-calendar wire-fortune wire-wealth wire-master wire-adflow wire-authcenter
