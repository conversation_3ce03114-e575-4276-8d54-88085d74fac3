services:
  mysql:
    image: mysql:8.0.31-debian
    hostname: imp-mysql
    container_name: imp-mysql
    restart: always
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=dangerous
      - MYSQL_ROOT_HOST=%
      - MYSQL_DATABASE=imp
    volumes:
       - ./data/mysql/user:/var/lib/mysql
       - ./conf/mysql/conf.d:/etc/mysql/conf.d
  redis:
    image: redis:6-alpine
    hostname: imp-redis
    container_name: imp-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
     - ./data/redis/:/data
     - ./conf/redis/redis.conf:/etc/redis/redis.conf
    command: ["redis-server","/etc/redis/redis.conf"]
