{"swagger": "2.0", "info": {"description": "This is a http server template.", "title": "CMS API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "url": "http://www.swagger.io/support", "email": "<EMAIL>"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}, "version": "1.0.0"}, "paths": {"/app/channel/create": {"post": {"description": "创建渠道", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["渠道"], "summary": "创建渠道", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.CreateAppChannelRequest"}}], "responses": {"200": {"description": "创建渠道", "schema": {"$ref": "#/definitions/v1.CreateAppChannelResponse"}}}}}, "/app/channel/pageList": {"post": {"security": [{"BearerAuth": []}], "description": "渠道列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["渠道"], "summary": "渠道列表", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.PageListAppChannelRequest"}}], "responses": {"200": {"description": "渠道列表", "schema": {"$ref": "#/definitions/v1.PageListAppChannelResponse"}}}}}, "/app/channel/update": {"post": {"security": [{"BearerAuth": []}], "description": "更新渠道", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["渠道"], "summary": "更新渠道", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.UpdateAppChannelRequest"}}], "responses": {"200": {"description": "更新渠道", "schema": {"$ref": "#/definitions/v1.UpdateAppChannelResponse"}}}}}, "/app/version/create": {"post": {"security": [{"BearerAuth": []}], "description": "创建版本", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["应用版本"], "summary": "创建版本", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.CreateAppVersionRequest"}}], "responses": {"200": {"description": "创建版本", "schema": {"$ref": "#/definitions/v1.CreateAppVersionResponse"}}}}}, "/app/version/pageList": {"post": {"security": [{"BearerAuth": []}], "description": "版本列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["应用版本"], "summary": "版本列表", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.PageListAppVersionRequest"}}], "responses": {"200": {"description": "版本列表", "schema": {"$ref": "#/definitions/v1.PageListAppVersionResponse"}}}}}, "/app/version/publish": {"post": {"security": [{"BearerAuth": []}], "description": "发布版本", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["应用版本"], "summary": "发布版本", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.PublishAppVersionRequest"}}], "responses": {"200": {"description": "发布版本", "schema": {"$ref": "#/definitions/v1.PublishAppVersionResponse"}}}}}, "/app/version/recall": {"post": {"security": [{"BearerAuth": []}], "description": "撤回版本", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["应用版本"], "summary": "撤回版本", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.RecallAppVersionRequest"}}], "responses": {"200": {"description": "撤回版本", "schema": {"$ref": "#/definitions/v1.RecallAppVersionResponse"}}}}}, "/app/version/update": {"post": {"security": [{"BearerAuth": []}], "description": "更新版本", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["应用版本"], "summary": "更新版本", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.UpdateAppVersionRequest"}}], "responses": {"200": {"description": "更新版本", "schema": {"$ref": "#/definitions/v1.UpdateAppVersionResponse"}}}}}, "/appUser/pageList": {"post": {"description": "分页查询用户列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["用户"], "summary": "分页查询用户列表", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.PageListAppUserRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.PageListAppUserResponse"}}}}}, "/appUser/pageListVip": {"post": {"description": "分页查询VIP用户列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["用户"], "summary": "分页查询VIP用户列表", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.PageListAppUserVipRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.PageListAppUserVipResponse"}}}}}, "/atlasProduct/create": {"post": {"description": "创建产品", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["产品"], "summary": "创建产品", "parameters": [{"description": "创建产品请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.AtlasCreateProductReq"}}], "responses": {"200": {"description": "创建产品成功", "schema": {"allOf": [{"$ref": "#/definitions/v1.Response"}, {"type": "object", "properties": {"data": {"type": "string"}}}]}}}}}, "/atlasProduct/delete": {"post": {"description": "删除产品", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["产品"], "summary": "删除产品", "parameters": [{"description": "删除产品请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.AtlasDeleteProductReq"}}], "responses": {"200": {"description": "删除产品成功", "schema": {"allOf": [{"$ref": "#/definitions/v1.Response"}, {"type": "object", "properties": {"data": {"type": "string"}}}]}}}}}, "/atlasProduct/list": {"post": {"description": "获取产品列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["产品"], "summary": "获取产品列表", "parameters": [{"description": "获取产品列表请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.AtlasListProductRequest"}}], "responses": {"200": {"description": "获取产品列表成功", "schema": {"allOf": [{"$ref": "#/definitions/v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/v1.AtlasListProductResponse"}}}]}}}}}, "/atlasProduct/update": {"post": {"description": "更新产品", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["产品"], "summary": "更新产品", "parameters": [{"description": "更新产品请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.AtlasUpdateProductReq"}}], "responses": {"200": {"description": "更新产品成功", "schema": {"allOf": [{"$ref": "#/definitions/v1.Response"}, {"type": "object", "properties": {"data": {"type": "string"}}}]}}}}}, "/bal/create": {"post": {"description": "创建八字分析链接", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["八字分析链接"], "summary": "创建八字分析链接", "parameters": [{"description": "创建八字分析链接请求", "name": "param", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.CreateBaziAnalysisLinkRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.CreateBaziAnalysisLinkResponse"}}}}}, "/enums/app": {"post": {"security": [{"BearerAuth": []}], "description": "App", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["枚举"], "summary": "App", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.EnumsAppRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.EnumsAppResponse"}}}}}, "/enums/dizhi": {"post": {"security": [{"BearerAuth": []}], "description": "地支", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["枚举"], "summary": "地支", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.EnumsDizhiRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.EnumsDizhiResponse"}}}}}, "/enums/ganzhi": {"post": {"security": [{"BearerAuth": []}], "description": "干支", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["枚举"], "summary": "干支", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.EnumsGanzhiRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.EnumsGanzhiResponse"}}}}}, "/enums/lunar": {"post": {"description": "获取农历列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["枚举"], "summary": "获取农历列表", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.EnumsLunarRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.EnumsLunarResponse"}}}}}, "/enums/module": {"post": {"security": [{"BearerAuth": []}], "description": "模块", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["枚举"], "summary": "模块", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.EnumsModuleRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.EnumsModuleResponse"}}}}}, "/enums/nayin": {"post": {"security": [{"BearerAuth": []}], "description": "纳音", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["枚举"], "summary": "纳音", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.EnumsNayinRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.EnumsNayinResponse"}}}}}, "/enums/shensha": {"post": {"security": [{"BearerAuth": []}], "description": "神煞", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["枚举"], "summary": "神煞", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.EnumsShenshaRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.EnumsShenshaResponse"}}}}}, "/enums/shierchangsheng": {"post": {"security": [{"BearerAuth": []}], "description": "十二长生", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["枚举"], "summary": "十二长生", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.EnumsShierchangshengRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.EnumsShierchangshengResponse"}}}}}, "/enums/shishen": {"post": {"security": [{"BearerAuth": []}], "description": "十神", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["枚举"], "summary": "十神", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.EnumsShishenRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.EnumsShishenResponse"}}}}}, "/enums/tiangan": {"post": {"security": [{"BearerAuth": []}], "description": "天干", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["枚举"], "summary": "天干", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.EnumsTianganRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.EnumsTianganResponse"}}}}}, "/enums/wuxing": {"post": {"security": [{"BearerAuth": []}], "description": "五行", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["枚举"], "summary": "五行", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.EnumsWuxingRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.EnumsWuxingResponse"}}}}}, "/enums/xiji": {"post": {"security": [{"BearerAuth": []}], "description": "喜忌", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["枚举"], "summary": "喜忌", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.EnumsXijiRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.EnumsXijiResponse"}}}}}, "/enums/zuodui": {"post": {"security": [{"BearerAuth": []}], "description": "坐-对", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["枚举"], "summary": "坐-对", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.EnumsZuoduiRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.EnumsZuoduiResponse"}}}}}, "/enums/zuoduiPlus": {"post": {"description": "坐-对+", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["枚举"], "summary": "坐-对+", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.EnumsZuoduiPlusRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.EnumsZuoduiPlusResponse"}}}}}, "/feedback/pageList": {"post": {"security": [{"BearerAuth": []}], "description": "分页查询反馈列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["反馈"], "summary": "分页查询反馈列表", "parameters": [{"description": "分页查询反馈列表请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.PageListFeedbackRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.PageListFeedbackResponse"}}}}}, "/feedback/reply": {"post": {"security": [{"BearerAuth": []}], "description": "回复反馈", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["反馈"], "summary": "回复反馈", "parameters": [{"description": "回复反馈请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.ReplyFeedbackRequest"}}], "responses": {}}}, "/mingliRule/create": {"post": {"security": [{"BearerAuth": []}], "description": "创建命理规则", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["命理规则"], "summary": "创建命理规则", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.CreateMingliRuleRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.CreateMingliRuleResponse"}}}}}, "/mingliRule/delete": {"post": {"security": [{"BearerAuth": []}], "description": "删除命理规则", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["命理规则"], "summary": "删除命理规则", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.DeleteMingliRuleRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.DeleteMingliRuleResponse"}}}}}, "/mingliRule/match": {"post": {"description": "匹配命理规则", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["命理规则"], "summary": "匹配命理规则", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.MatchMingliRuleRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.MatchMingliRulesResponse"}}}}}, "/mingliRule/pageList": {"post": {"security": [{"BearerAuth": []}], "description": "分页查询命理规则", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["命理规则"], "summary": "分页查询命理规则", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.PageListMingliRuleRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.PageListMingliRuleResponse"}}}}}, "/mingliRule/update": {"post": {"security": [{"BearerAuth": []}], "description": "更新命理规则", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["命理规则"], "summary": "更新命理规则", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.UpdateMingliRuleRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.UpdateMingliRuleResponse"}}}}}, "/mingliRuleCondition/create": {"post": {"security": [{"BearerAuth": []}], "description": "创建命理规则条件", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["命理规则条件"], "summary": "创建命理规则条件", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.CreateMingliRuleConditionRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.CreateMingliRuleConditionResponse"}}}}}, "/mingliRuleCondition/delete": {"post": {"security": [{"BearerAuth": []}], "description": "删除命理规则条件", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["命理规则条件"], "summary": "删除命理规则条件", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.DeleteMingliRuleConditionRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.DeleteMingliRuleConditionResponse"}}}}}, "/mingliRuleCondition/detail": {"post": {"security": [{"BearerAuth": []}], "description": "获取命理规则条件", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["命理规则条件"], "summary": "获取命理规则条件", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.GetMingliRuleConditionDetailRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.GetMingliRuleConditionDetailResponse"}}}}}, "/mingliRuleCondition/pageList": {"post": {"security": [{"BearerAuth": []}], "description": "分页查询命理规则条件", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["命理规则条件"], "summary": "分页查询命理规则条件", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.PageListMingliRuleConditionRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.PageListMingliRuleConditionResponse"}}}}}, "/mingliRuleCondition/setXiji": {"post": {"security": [{"BearerAuth": []}], "description": "设置喜忌", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["命理规则条件"], "summary": "设置喜忌", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.SetMingliRuleConditionXijiRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.SetMingliRuleConditionXijiResponse"}}}}}, "/mingliRuleCondition/setZuodui": {"post": {"security": [{"BearerAuth": []}], "description": "设置坐对", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["命理规则条件"], "summary": "设置坐对", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.SetMingliRuleConditionZuoduiRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.SetMingliRuleConditionZuoduiResponse"}}}}}, "/mingliRuleCondition/update": {"post": {"security": [{"BearerAuth": []}], "description": "更新命理规则条件", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["命理规则条件"], "summary": "更新命理规则条件", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.UpdateMingliRuleConditionRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.UpdateMingliRuleConditionResponse"}}}}}, "/mp/qrcode/create": {"post": {"security": [{"BearerAuth": []}], "description": "小程序生成二维码", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["小程序"], "summary": "小程序生成二维码", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.MpQrCodeRequest"}}], "responses": {"200": {"description": "OK", "schema": {"type": "file"}}}}}, "/mp/qrcode/pageList": {"post": {"security": [{"BearerAuth": []}], "description": "小程序二维码列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["小程序"], "summary": "小程序二维码列表", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.MpQrCodePageListRequest"}}], "responses": {"200": {"description": "二维码列表", "schema": {"$ref": "#/definitions/v1.MpQrCodePageListResponse"}}}}}, "/oa/material/add": {"post": {"security": [{"BearerAuth": []}], "description": "公众号上传永久素材", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["微信公众号"], "summary": "公众号上传永久素材", "parameters": [{"type": "string", "description": "素材类型", "name": "type", "in": "formData", "required": true}, {"type": "string", "description": "素材备注", "name": "remark", "in": "formData", "required": true}, {"type": "file", "description": "素材文件", "name": "file", "in": "formData", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.OaAppMaterialResponse"}}}}}, "/oa/menu/create": {"post": {"security": [{"BearerAuth": []}], "description": "创建公众号菜单", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["微信公众号"], "summary": "创建公众号菜单", "parameters": [{"description": "body", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.OaCreateMenuRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.OaCreateMenuResponse"}}}}}, "/oa/menu/detail": {"post": {"security": [{"BearerAuth": []}], "description": "获取公众号菜单", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["微信公众号"], "summary": "获取公众号菜单", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.OaGetMenuDetailResponse"}}}}}, "/oa/qrcode/create": {"post": {"security": [{"BearerAuth": []}], "description": "创建公众号自定义参数二维码", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["微信公众号"], "summary": "创建公众号自定义参数二维码", "parameters": [{"description": "body", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.OaCreateQrcodeRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.OaCreateQrcodeResponse"}}}}}, "/order/detail": {"post": {"description": "获取订单详情", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["订单"], "summary": "获取订单详情", "parameters": [{"description": "获取订单详情请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.OrderDetailRequest"}}], "responses": {"200": {"description": "获取订单详情成功", "schema": {"$ref": "#/definitions/v1.OrderDetailResponse"}}}}}, "/order/pageList": {"post": {"security": [{"BearerAuth": []}], "description": "分页查询用户订单", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["用户订单"], "summary": "分页查询用户订单", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.PageListUserOrderRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.PageListUserOrderResponse"}}}}}, "/paipanRecord/pageList": {"post": {"security": [{"BearerAuth": []}], "description": "分页查询用户排盘记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["用户排盘记录"], "summary": "分页查询用户排盘记录", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.PageListPaipanRecordRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.PageListPaipanRecordResponse"}}}}}, "/portal-manage/article/create": {"post": {"security": [{"BearerAuth": []}], "description": "创建门户文章", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["门户"], "summary": "创建门户文章", "parameters": [{"description": "创建门户文章请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.CreatePortalArticleRequest"}}], "responses": {"200": {"description": "创建门户文章成功", "schema": {"$ref": "#/definitions/v1.CreatePortalArticleResponse"}}}}}, "/portal-manage/article/delete": {"post": {"security": [{"BearerAuth": []}], "description": "删除门户文章", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["门户"], "summary": "删除门户文章", "parameters": [{"description": "删除门户文章请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.DeletePortalArticleRequest"}}], "responses": {"200": {"description": "删除门户文章成功", "schema": {"$ref": "#/definitions/v1.Response"}}}}}, "/portal-manage/article/pageList": {"post": {"description": "分页查询门户文章", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["门户"], "summary": "分页查询门户文章", "parameters": [{"description": "分页查询门户文章请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.PageListPortalArticleRequest"}}], "responses": {"200": {"description": "分页查询门户文章成功", "schema": {"$ref": "#/definitions/v1.PageListPortalArticleResponse"}}}}}, "/portal-manage/article/update": {"post": {"security": [{"BearerAuth": []}], "description": "更新门户文章", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["门户"], "summary": "更新门户文章", "parameters": [{"description": "更新门户文章请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.UpdatePortalArticleRequest"}}], "responses": {"200": {"description": "更新门户文章成功", "schema": {"$ref": "#/definitions/v1.Response"}}}}}, "/portal-manage/section/create": {"post": {"security": [{"BearerAuth": []}], "description": "创建门户栏目", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["门户"], "summary": "创建门户栏目", "parameters": [{"description": "创建门户栏目请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.CreatePortalSectionRequest"}}], "responses": {"200": {"description": "创建门户栏目成功", "schema": {"$ref": "#/definitions/v1.CreatePortalSectionResponse"}}}}}, "/portal-manage/section/delete": {"post": {"security": [{"BearerAuth": []}], "description": "删除门户栏目", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["门户"], "summary": "删除门户栏目", "parameters": [{"description": "删除门户栏目请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.DeletePortalSectionRequest"}}], "responses": {"200": {"description": "删除门户栏目成功", "schema": {"$ref": "#/definitions/v1.Response"}}}}}, "/portal-manage/section/list": {"post": {"description": "分页查询门户栏目", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["门户"], "summary": "分页查询门户栏目", "parameters": [{"description": "分页查询门户栏目请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.ListPortalSectionRequest"}}], "responses": {"200": {"description": "分页查询门户栏目成功", "schema": {"$ref": "#/definitions/v1.ListPortalSectionResponse"}}}}}, "/portal-manage/section/update": {"post": {"security": [{"BearerAuth": []}], "description": "更新门户栏目", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["门户"], "summary": "更新门户栏目", "parameters": [{"description": "更新门户栏目请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.UpdatePortalSectionRequest"}}], "responses": {"200": {"description": "更新门户栏目成功", "schema": {"$ref": "#/definitions/v1.Response"}}}}}, "/portal-manage/tag/delete": {"post": {"security": [{"BearerAuth": []}], "description": "删除文章标签", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["门户"], "summary": "删除文章标签（下拉列表中删除）", "parameters": [{"description": "删除文章标签请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.DeletePortalTagRequest"}}], "responses": {"200": {"description": "删除文章标签成功", "schema": {"$ref": "#/definitions/v1.Response"}}}}}, "/portal-manage/tag/list": {"post": {"security": [{"BearerAuth": []}], "description": "获取文章标签列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["门户"], "summary": "获取文章标签列表（文章标签输入栏调用）", "parameters": [{"description": "获取文章标签请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.ListTagRequest"}}], "responses": {"200": {"description": "获取文章标签成功", "schema": {"$ref": "#/definitions/v1.ListTagResponse"}}}}}, "/promotion-manage/user/create": {"post": {"security": [{"BearerAuth": []}], "description": "创建推广用户（渠道）", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["推广管理"], "summary": "创建推广用户（渠道）", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.CreateAppUserPromotionRequest"}}], "responses": {"200": {"description": "创建推广用户（渠道）", "schema": {"$ref": "#/definitions/v1.CreateAppUserPromotionResponse"}}}}}, "/promotion-manage/user/pageList": {"post": {"security": [{"BearerAuth": []}], "description": "分页查询推广用户（渠道）", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["推广管理"], "summary": "分页查询推广用户（渠道）", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.PageListAppUserPromotionRequest"}}], "responses": {"200": {"description": "分页查询推广用户（渠道）", "schema": {"$ref": "#/definitions/v1.PageListAppUserPromotionResponse"}}}}}, "/promotion-manage/user/update": {"post": {"security": [{"BearerAuth": []}], "description": "更新推广用户（渠道）", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["推广管理"], "summary": "更新推广用户（渠道）", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.UpdateAppUserPromotionRequest"}}], "responses": {"200": {"description": "更新推广用户（渠道）", "schema": {"$ref": "#/definitions/v1.UpdateAppUserPromotionResponse"}}}}}, "/qw/acquisitionLink/create": {"post": {"security": [{"BearerAuth": []}], "description": "创建获客链接", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["企业微信"], "summary": "创建获客链接", "parameters": [{"description": "请求参数", "name": "createAcquisitionLink", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.QwCreateAcquisitionLinkRequest"}}], "responses": {"200": {"description": "成功返回数据", "schema": {"$ref": "#/definitions/v1.QwCreateAcquisitionLinkResponse"}}}}}, "/qw/bill/pageList": {"post": {"security": [{"BearerAuth": []}], "description": "分页查询企微收款记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["企业微信"], "summary": "分页查询企微收款记录", "parameters": [{"description": "请求参数", "name": "pageListBill", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.QwPageListBillRequest"}}], "responses": {"200": {"description": "成功返回数据", "schema": {"$ref": "#/definitions/v1.QwPageListBillResponse"}}}}}, "/qw/contact/follow/pageList": {"post": {"security": [{"BearerAuth": []}], "description": "分页查询客户跟进记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["企业微信"], "summary": "分页查询客户跟进记录", "parameters": [{"description": "请求参数", "name": "pageListContactFollow", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.QwPageListContactFollowRequest"}}], "responses": {"200": {"description": "成功返回数据", "schema": {"$ref": "#/definitions/v1.QwPageListContactFollowResponse"}}}}}, "/qw/contact/follow/users": {"post": {"security": [{"BearerAuth": []}], "description": "获取客户跟进人员列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["企业微信"], "summary": "获取客户跟进人员列表", "parameters": [{"description": "请求参数", "name": "contactFollowUsers", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.QwContactFollowUsersRequest"}}], "responses": {"200": {"description": "成功返回数据", "schema": {"$ref": "#/definitions/v1.QwContactFollowUsersResponse"}}}}}, "/qw/contact/update": {"post": {"security": [{"BearerAuth": []}], "description": "更新客户信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["企业微信"], "summary": "更新客户信息", "parameters": [{"description": "请求参数", "name": "updateContact", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.QwUpdateContactRequest"}}], "responses": {"200": {"description": "成功返回数据", "schema": {"$ref": "#/definitions/v1.QwUpdateContactResponse"}}}}}, "/qw/contactWay/create": {"post": {"security": [{"BearerAuth": []}], "description": "配置客户联系「联系我」方式", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["企业微信"], "summary": "配置客户联系「联系我」方式", "parameters": [{"description": "请求参数", "name": "addContactWay", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.QwCreateContactWayRequest"}}], "responses": {"200": {"description": "成功返回数据", "schema": {"$ref": "#/definitions/v1.QwCreateContactWayResponse"}}}}}, "/qw/contactWay/delete": {"post": {"security": [{"BearerAuth": []}], "description": "删除客户联系「联系我」方式", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["企业微信"], "summary": "删除客户联系「联系我」方式", "parameters": [{"description": "请求参数", "name": "deleteContactWay", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.QwDeleteContactWayRequest"}}], "responses": {"200": {"description": "成功返回数据", "schema": {"$ref": "#/definitions/v1.QwDeleteContactWayResponse"}}}}}, "/qw/contactWay/pageList": {"post": {"security": [{"BearerAuth": []}], "description": "分页查询客户联系「联系我」方式", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["企业微信"], "summary": "分页查询客户联系「联系我」方式", "parameters": [{"description": "请求参数", "name": "pageListContactWay", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.QwPageListContactWayRequest"}}], "responses": {"200": {"description": "成功返回数据", "schema": {"$ref": "#/definitions/v1.QwPageListContactWayResponse"}}}}}, "/qw/contactWay/update": {"post": {"security": [{"BearerAuth": []}], "description": "更新客户联系「联系我」方式", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["企业微信"], "summary": "更新客户联系「联系我」方式", "parameters": [{"description": "请求参数", "name": "updateContactWay", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.QwUpdateContactWayRequest"}}], "responses": {"200": {"description": "成功返回数据", "schema": {"$ref": "#/definitions/v1.QwUpdateContactWayResponse"}}}}}, "/qw/departments": {"post": {"security": [{"BearerAuth": []}], "description": "获取企业微信部门列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["企业微信"], "summary": "获取企业微信部门列表", "parameters": [{"description": "请求参数", "name": "departments", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.QwDepartmentsRequest"}}], "responses": {"200": {"description": "成功返回数据", "schema": {"$ref": "#/definitions/v1.QwDepartmentsResponse"}}}}}, "/term/query": {"post": {"description": "查询术语", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["术语"], "summary": "查询术语", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.QueryTermRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.QueryTermResponse"}}}}}, "/userMingli/pageList": {"post": {"security": [{"BearerAuth": []}], "description": "分页查询用户命例", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["用户命例"], "summary": "分页查询用户命例", "parameters": [{"description": "params", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1.PageListUserMingliRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/v1.PageListUserMingliResponse"}}}}}}, "definitions": {"model.MingliRuleConditionValue": {"type": "object", "properties": {"type": {"description": "类型：1-支、2-干、3-干支、4-十神、5-五行", "type": "integer", "example": 1}, "value": {"description": "值", "type": "integer", "example": 1}}}, "model.MpQrcode": {"type": "object", "properties": {"appID": {"type": "integer"}, "createdAt": {"description": "创建时间", "type": "string"}, "deletedAt": {"type": "string"}, "id": {"type": "integer"}, "imageData": {"type": "array", "items": {"type": "integer"}}, "imageType": {"type": "string"}, "page": {"type": "string"}, "remark": {"type": "string"}, "sceneStr": {"type": "string"}, "updatedAt": {"description": "更新时间", "type": "string"}}}, "model.Product": {"type": "object", "properties": {"application": {"description": "所属应用", "type": "string"}, "createdAt": {"description": "创建时间", "type": "string"}, "enable": {"description": "是否启用：0-禁用，1-启用", "type": "integer"}, "id": {"type": "integer"}, "originalPrice": {"description": "划线价", "type": "integer"}, "remark": {"description": "备注", "type": "string"}, "salePrice": {"description": "出售价", "type": "integer"}, "sku_code": {"description": "SKU编码", "type": "string"}, "sku_name": {"description": "SKU名称", "type": "string"}, "specification": {"description": "规格", "type": "string"}, "stock": {"description": "库存量", "type": "integer"}, "type": {"description": "商品类型：0-其他，1-虚拟会员商品，2-咨询服务", "type": "integer"}, "updatedAt": {"description": "更新时间", "type": "string"}, "validityDays": {"description": "会员有效期天数", "type": "integer"}}}, "model.QwContactFollowEvent": {"type": "object", "properties": {"add_state": {"description": "添加参数", "type": "string"}, "add_way": {"description": "添加方式", "type": "integer"}, "del_flag": {"description": "删除标记：0-未删除 1-被删除 2-主动删除 3-自动删除（接替）", "type": "integer"}, "del_source": {"description": "删除来源", "type": "string"}, "time": {"description": "事件时间", "type": "integer"}, "type": {"description": "事件类型：1-添加 2-删除", "type": "integer"}}}, "model.UserMingliDayun": {"type": "object", "properties": {"endYear": {"type": "integer"}, "startYear": {"type": "integer"}, "values": {"type": "array", "items": {"type": "string"}}}}, "model.UserMingliXiaoyun": {"type": "object", "properties": {"endYear": {"type": "integer"}, "startYear": {"type": "integer"}, "subYear": {"type": "integer"}, "values": {"type": "array", "items": {"type": "string"}}}}, "request.Button": {"type": "object", "properties": {"appid": {"type": "string"}, "key": {"type": "string"}, "media_id": {"type": "string"}, "name": {"type": "string"}, "pagepath": {"type": "string"}, "sub_button": {"type": "array", "items": {"$ref": "#/definitions/request.SubButton"}}, "type": {"type": "string"}, "url": {"type": "string"}}}, "request.SubButton": {"type": "object", "properties": {"appid": {"type": "string"}, "key": {"type": "string"}, "media_id": {"type": "string"}, "name": {"type": "string"}, "pagepath": {"type": "string"}, "sub_button": {"type": "array", "items": {"$ref": "#/definitions/request.SubButton"}}, "type": {"type": "string"}, "url": {"type": "string"}}}, "response.Button": {"type": "object", "properties": {"appid": {"type": "string"}, "key": {"type": "string"}, "name": {"type": "string"}, "pagepath": {"type": "string"}, "sub_button": {"type": "array", "items": {"$ref": "#/definitions/response.Button"}}, "type": {"type": "string"}, "url": {"type": "string"}}}, "response.ConditionalMenu": {"type": "object", "properties": {"button": {"type": "array", "items": {"$ref": "#/definitions/response.Button"}}, "matchrule": {"$ref": "#/definitions/response.MatchRule"}, "menuid": {"type": "integer"}}}, "response.MatchRule": {"type": "object", "properties": {"city": {"type": "string"}, "client_platform_type": {"type": "integer"}, "country": {"type": "string"}, "group_id": {"type": "integer"}, "province": {"type": "string"}, "sex": {"type": "integer"}}}, "response.Menu": {"type": "object", "properties": {"button": {"type": "array", "items": {"$ref": "#/definitions/response.Button"}}, "menuid": {"type": "integer"}}}, "v1.AppUserMemberDuration": {"type": "object", "properties": {"appsName": {"type": "array", "items": {"type": "string"}}, "expireTime": {"description": "到期时间", "type": "string"}, "isExpired": {"description": "是否过期", "type": "boolean"}, "lastGainTime": {"description": "最后一次获取时间", "type": "string"}, "lastGainType": {"description": "最后一次获取类型：0 购买，1 管理员赠送，2 注册赠送，3 app登录赠送", "type": "integer"}, "roleID": {"type": "integer"}, "roleName": {"type": "string"}, "startTime": {"description": "开始时间", "type": "string"}}}, "v1.AtlasCreateProductReq": {"type": "object", "properties": {"application": {"description": "所属应用", "type": "string"}, "enable": {"description": "是否启用：0-禁用，1-启用", "type": "integer"}, "originalPrice": {"description": "划线价", "type": "integer"}, "remark": {"description": "备注", "type": "string"}, "salePrice": {"description": "出售价", "type": "integer"}, "sku_name": {"description": "SKU名称", "type": "string"}, "specification": {"description": "规格", "type": "string"}, "stock": {"description": "库存量", "type": "integer"}, "type": {"description": "商品类型：0-其他，1-虚拟会员商品，2-咨询服务", "type": "integer"}, "validityDays": {"description": "会员有效期天数", "type": "integer"}}}, "v1.AtlasDeleteProductReq": {"type": "object", "properties": {"id": {"type": "integer"}}}, "v1.AtlasListProductReq": {"type": "object", "properties": {"application": {"description": "应用", "type": "string"}, "name": {"description": "商品名称", "type": "string"}, "sort": {"description": "排序 1-创建日期升序，2-创建日期降序，3-价格升序，4-价格降序", "type": "integer"}}}, "v1.AtlasListProductRequest": {"type": "object", "required": ["pageNum", "pageSize"], "properties": {"pageNum": {"type": "integer", "minimum": 1, "example": 1}, "pageSize": {"type": "integer", "minimum": 1, "example": 10}, "param": {"$ref": "#/definitions/v1.AtlasListProductReq"}}}, "v1.AtlasListProductResponse": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/model.Product"}}, "total": {"type": "integer"}}}, "v1.AtlasUpdateProductReq": {"type": "object", "properties": {"application": {"description": "所属应用", "type": "string"}, "enable": {"description": "是否启用：0-禁用，1-启用", "type": "integer"}, "id": {"type": "integer"}, "originalPrice": {"description": "划线价", "type": "integer"}, "remark": {"description": "备注", "type": "string"}, "salePrice": {"description": "出售价", "type": "integer"}, "sku_name": {"description": "SKU名称", "type": "string"}, "specification": {"description": "规格", "type": "string"}, "stock": {"description": "库存量", "type": "integer"}, "type": {"description": "商品类型：0-其他，1-虚拟会员商品，2-咨询服务", "type": "integer"}, "validityDays": {"description": "会员有效期天数", "type": "integer"}}}, "v1.ChannelAppStatistics": {"type": "object", "properties": {"appID": {"type": "integer"}, "appName": {"type": "string"}, "enableWxMiniProgram": {"type": "boolean"}, "signupNum": {"type": "integer"}, "wxMiniProgramQrCode": {"$ref": "#/definitions/model.MpQrcode"}}}, "v1.ChannelStatistics": {"type": "object", "properties": {"apps": {"type": "array", "items": {"$ref": "#/definitions/v1.ChannelAppStatistics"}}, "signupNum": {"type": "integer"}}}, "v1.CreateAppChannelRequest": {"type": "object", "required": ["name", "remark"], "properties": {"name": {"type": "string"}, "remark": {"type": "string"}}}, "v1.CreateAppChannelResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.CreateAppChannelResponseData"}, "message": {"type": "string"}}}, "v1.CreateAppChannelResponseData": {"type": "object", "properties": {"id": {"type": "integer"}}}, "v1.CreateAppUserPromotionRequest": {"type": "object", "required": ["level1Ratio", "level2Ratio", "name", "phone"], "properties": {"level1Ratio": {"description": "一级返利比例", "type": "integer"}, "level2Ratio": {"description": "二级返利比例", "type": "integer"}, "name": {"description": "渠道名称", "type": "string"}, "phone": {"description": "渠道手机号", "type": "string"}, "remark": {"description": "备注", "type": "string"}}}, "v1.CreateAppUserPromotionResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.CreateAppUserPromotionResponseData"}, "message": {"type": "string"}}}, "v1.CreateAppUserPromotionResponseData": {"type": "object", "properties": {"id": {"type": "integer"}}}, "v1.CreateAppVersionRequest": {"type": "object", "required": ["appID", "osType", "updateNote", "url", "versionName"], "properties": {"appID": {"description": "应用ID", "type": "integer"}, "isForceUpdate": {"description": "是否强制更新", "type": "boolean"}, "isHotUpdate": {"description": "是否热更新", "type": "boolean"}, "osType": {"description": "1:android, 2:ios", "type": "integer"}, "remark": {"description": "备注", "type": "string"}, "updateNote": {"description": "更新说明", "type": "string"}, "url": {"description": "下载地址", "type": "string"}, "versionName": {"description": "版本名称：1.2.0", "type": "string"}}}, "v1.CreateAppVersionResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.CreateAppVersionResponseData"}, "message": {"type": "string"}}}, "v1.CreateAppVersionResponseData": {"type": "object", "properties": {"id": {"type": "integer"}}}, "v1.CreateBaziAnalysisLinkRequest": {"type": "object", "required": ["birthplace", "birthtime", "gender", "name", "orderID", "phone"], "properties": {"birthplace": {"description": "出生地点：省、市、区", "type": "array", "items": {"type": "string"}}, "birthtime": {"description": "出生时间：2006-01-02 15:04:05", "type": "string"}, "gender": {"description": "性别：1-男、2-女", "type": "integer"}, "ip": {"description": "IP", "type": "string"}, "name": {"description": "姓名", "type": "string"}, "orderID": {"description": "订单ID", "type": "integer"}, "phone": {"description": "手机号", "type": "string"}, "ua": {"description": "Auth-Agent", "type": "string"}}}, "v1.CreateBaziAnalysisLinkResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "string"}, "message": {"type": "string"}}}, "v1.CreateMingliRuleConditionRequest": {"type": "object", "required": ["mingliRuleId", "name", "no"], "properties": {"category": {"description": "类别：2-坐对、3-喜忌、1-全选", "type": "integer", "example": 1}, "criterion": {"description": "判断依据说明", "type": "string", "example": "判断依据说明"}, "gender": {"description": "日主性别（2-男、3-女、1-无关性别）", "type": "integer", "example": 1}, "mingliRuleId": {"description": "命理规则ID", "type": "integer", "example": 1}, "name": {"description": "条件名称", "type": "string", "example": "条件名称"}, "no": {"description": "条件编号", "type": "string", "example": "条件编号"}, "type": {"description": "条件类型（坐-对）", "type": "integer", "example": 1}, "weizhiDui": {"description": "位置对（1-年、2-月、3-日、4-时、5-大运、6-流年、7-流月、8-流日、9-流时）", "type": "array", "items": {"type": "integer"}, "example": [1, 2, 3]}, "weizhiZuo": {"description": "位置坐（1-年、2-月、3-日、4-时、5-大运、6-流年、7-流月、8-流日、9-流时）", "type": "array", "items": {"type": "integer"}, "example": [1, 2, 3]}}}, "v1.CreateMingliRuleConditionResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.CreateMingliRuleConditionResponseData"}, "message": {"type": "string"}}}, "v1.CreateMingliRuleConditionResponseData": {"type": "object", "properties": {"id": {"description": "命理规则条件ID", "type": "integer", "example": 1}}}, "v1.CreateMingliRuleRequest": {"type": "object", "required": ["description", "isEnabled", "module", "name", "no", "result"], "properties": {"description": {"description": "规则说明", "type": "string", "example": "规则说明"}, "isEnabled": {"description": "是否启用", "type": "boolean", "example": true}, "module": {"description": "应用模块", "type": "integer", "example": 1}, "name": {"description": "规则名称", "type": "string", "example": "规则名称"}, "no": {"description": "规则编号", "type": "string", "maxLength": 32, "example": "NO1001"}, "result": {"description": "判断结果", "type": "string", "example": "判断结果"}}}, "v1.CreateMingliRuleResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.CreateMingliRuleResponseData"}, "message": {"type": "string"}}}, "v1.CreateMingliRuleResponseData": {"type": "object", "properties": {"id": {"description": "规则ID", "type": "integer", "example": 1}}}, "v1.CreatePortalArticleRequest": {"type": "object", "required": ["author", "content", "sectionId", "title"], "properties": {"author": {"description": "作者", "type": "string"}, "content": {"description": "文章内容", "type": "string"}, "isPublish": {"description": "是否发布", "type": "boolean"}, "isTop": {"description": "是否置顶", "type": "boolean"}, "remark": {"description": "备注", "type": "string"}, "sectionId": {"description": "栏目ID", "type": "integer"}, "tags": {"description": "标签", "type": "array", "items": {"type": "string"}}, "title": {"description": "文章标题", "type": "string"}}}, "v1.CreatePortalArticleResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.CreatePortalArticleResponseData"}, "message": {"type": "string"}}}, "v1.CreatePortalArticleResponseData": {"type": "object", "properties": {"id": {"type": "integer"}}}, "v1.CreatePortalSectionRequest": {"type": "object", "required": ["name"], "properties": {"name": {"description": "栏目（文章分类）名称", "type": "string"}, "remark": {"description": "栏目备注", "type": "string"}}}, "v1.CreatePortalSectionResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.CreatePortalSectionResponseData"}, "message": {"type": "string"}}}, "v1.CreatePortalSectionResponseData": {"type": "object", "properties": {"id": {"description": "栏目ID", "type": "integer"}}}, "v1.DeleteMingliRuleConditionRequest": {"type": "object", "required": ["id"], "properties": {"id": {"description": "命理规则条件ID", "type": "integer", "example": 1}}}, "v1.DeleteMingliRuleConditionResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "message": {"type": "string"}}}, "v1.DeleteMingliRuleRequest": {"type": "object", "required": ["id"], "properties": {"id": {"type": "integer", "example": 1}}}, "v1.DeleteMingliRuleResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "message": {"type": "string"}}}, "v1.DeletePortalArticleRequest": {"type": "object", "required": ["id"], "properties": {"id": {"type": "integer"}}}, "v1.DeletePortalSectionRequest": {"type": "object", "required": ["id"], "properties": {"id": {"description": "栏目ID", "type": "integer"}}}, "v1.DeletePortalTagRequest": {"type": "object", "required": ["names"], "properties": {"names": {"description": "标签名称", "type": "array", "items": {"type": "string"}}}}, "v1.EnumsAppRequest": {"type": "object", "properties": {"onlySelf": {"type": "boolean", "example": true}}}, "v1.EnumsAppResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/v1.EnumsAppResponseDataItem"}}, "message": {"type": "string"}}}, "v1.EnumsAppResponseDataItem": {"type": "object", "properties": {"app": {"type": "string"}, "appID": {"type": "string"}, "clientID": {"type": "string"}, "code": {"type": "string", "example": "app_code"}, "id": {"type": "integer", "example": 1}, "isSelf": {"type": "boolean", "example": true}, "name": {"type": "string", "example": "应用名称"}, "org": {"type": "string"}, "role": {"type": "integer"}, "wxMpAppID": {"type": "string"}}}, "v1.EnumsDizhiRequest": {"type": "object"}, "v1.EnumsDizhiResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/v1.EnumsDizhiResponseItem"}}, "message": {"type": "string"}}}, "v1.EnumsDizhiResponseItem": {"type": "object", "properties": {"dizhi": {"description": "地支", "type": "string"}, "id": {"type": "integer", "example": 1}, "jieqi": {"description": "节气", "type": "string"}, "name": {"type": "string", "example": "子"}, "shichen": {"description": "时辰", "type": "string"}, "shuxiang": {"description": "属相", "type": "string"}, "wuxing": {"description": "五行", "type": "string"}, "yinyang": {"description": "阴阳", "type": "string"}, "yuefen": {"description": "月份", "type": "string"}, "zhongqi": {"description": "中气", "type": "string"}}}, "v1.EnumsGanzhiRequest": {"type": "object"}, "v1.EnumsGanzhiResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/v1.EnumsGanzhiResponseItem"}}, "message": {"type": "string"}}}, "v1.EnumsGanzhiResponseItem": {"type": "object", "properties": {"dizhiID": {"type": "integer", "example": 1}, "id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "甲子"}, "tianganID": {"type": "integer", "example": 1}}}, "v1.EnumsLunarRequest": {"type": "object", "required": ["year"], "properties": {"year": {"description": "年份", "type": "string", "example": "2020"}}}, "v1.EnumsLunarResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/v1.EnumsLunarResponseItem"}}, "message": {"type": "string"}}}, "v1.EnumsLunarResponseItem": {"type": "object", "properties": {"days": {"description": "日期", "type": "array", "items": {"$ref": "#/definitions/v1.LunarDay"}}, "month": {"description": "月份", "type": "string", "example": "正月"}}}, "v1.EnumsModuleRequest": {"type": "object"}, "v1.EnumsModuleResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/v1.EnumsModuleResponseItem"}}, "message": {"type": "string"}}}, "v1.EnumsModuleResponseItem": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "应用模块名称"}}}, "v1.EnumsNayinRequest": {"type": "object"}, "v1.EnumsNayinResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/v1.EnumsNayinResponseItem"}}, "message": {"type": "string"}}}, "v1.EnumsNayinResponseItem": {"type": "object", "properties": {"hanyi": {"description": "含义", "type": "string", "example": "..."}, "id": {"description": "ID", "type": "integer", "example": 1}, "name": {"description": "纳音名称", "type": "string", "example": "盖中王"}, "wuxing": {"description": "五行", "type": "string", "example": "木"}, "zhu1": {"description": "纳音名称", "type": "string", "example": "甲"}, "zhu2": {"description": "纳音名称", "type": "string", "example": "子"}}}, "v1.EnumsShenshaRequest": {"type": "object"}, "v1.EnumsShenshaResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/v1.EnumsShenshaResponseItem"}}, "message": {"type": "string"}}}, "v1.EnumsShenshaResponseItem": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "天乙贵人"}}}, "v1.EnumsShierchangshengRequest": {"type": "object"}, "v1.EnumsShierchangshengResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/v1.EnumsShierchangshengResponseItem"}}, "message": {"type": "string"}}}, "v1.EnumsShierchangshengResponseItem": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "长生"}}}, "v1.EnumsShishenRequest": {"type": "object"}, "v1.EnumsShishenResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/v1.EnumsShishenResponseItem"}}, "message": {"type": "string"}}}, "v1.EnumsShishenResponseItem": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "正官"}}}, "v1.EnumsTianganRequest": {"type": "object"}, "v1.EnumsTianganResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/v1.EnumsTianganResponseItem"}}, "message": {"type": "string"}}}, "v1.EnumsTianganResponseItem": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "甲"}, "tiangan": {"description": "天干", "type": "string"}, "wuxing": {"description": "五行", "type": "string"}, "yinyang": {"description": "阴阳", "type": "string"}}}, "v1.EnumsWuxingRequest": {"type": "object"}, "v1.EnumsWuxingResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/v1.EnumsWuxingResponseItem"}}, "message": {"type": "string"}}}, "v1.EnumsWuxingResponseItem": {"type": "object", "properties": {"id": {"description": "ID", "type": "integer", "example": 1}, "name": {"description": "五行名称", "type": "string", "example": "金"}, "position": {"description": "方位", "type": "string"}, "profession": {"description": "职业", "type": "string"}, "season": {"description": "季节", "type": "string"}, "wuxing": {"description": "五行", "type": "string"}}}, "v1.EnumsXijiRequest": {"type": "object"}, "v1.EnumsXijiResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/v1.EnumsXijiResponseItem"}}, "message": {"type": "string"}}}, "v1.EnumsXijiResponseItem": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "喜"}}}, "v1.EnumsZuoduiPlusRequest": {"type": "object"}, "v1.EnumsZuoduiPlusResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.EnumsZuoduiPlusResponseData"}, "message": {"type": "string"}}}, "v1.EnumsZuoduiPlusResponseData": {"type": "object", "additionalProperties": {}}, "v1.EnumsZuoduiRequest": {"type": "object"}, "v1.EnumsZuoduiResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/v1.EnumsZuoduiResponseItem"}}, "message": {"type": "string"}}}, "v1.EnumsZuoduiResponseItem": {"type": "object", "properties": {"dui": {"type": "string", "example": "支"}, "id": {"type": "integer", "example": 1}, "type": {"type": "integer", "example": 1}, "zuo": {"type": "string", "example": "干"}}}, "v1.GetMingliRuleConditionDetailRequest": {"type": "object", "required": ["id"], "properties": {"id": {"description": "命理规则条件ID", "type": "integer", "example": 1}}}, "v1.GetMingliRuleConditionDetailResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.GetMingliRuleConditionDetailResponseData"}, "message": {"type": "string"}}}, "v1.GetMingliRuleConditionDetailResponseData": {"type": "object", "properties": {"category": {"description": "类别：2-坐对、3-喜忌、1-全选", "type": "integer", "example": 1}, "criterion": {"description": "判断依据说明", "type": "string", "example": "判断依据说明"}, "gender": {"description": "日主性别（2-男、3-女、1-无关性别）", "type": "integer", "example": 1}, "id": {"description": "主键ID", "type": "integer", "example": 1}, "name": {"description": "条件名称", "type": "string", "example": "条件名称"}, "no": {"description": "条件编号", "type": "string", "example": "条件编号"}, "type": {"description": "条件类型（坐-对）", "type": "integer", "example": 1}, "weizhiDui": {"description": "位置对（1-年、2-月、3-日、4-时、5-大运、6-流年、7-流月、8-流日、9-流时）", "type": "array", "items": {"type": "integer"}, "example": [1, 2, 3]}, "weizhiZuo": {"description": "位置坐（1-年、2-月、3-日、4-时、5-大运、6-流年、7-流月、8-流日、9-流时）", "type": "array", "items": {"type": "integer"}, "example": [1, 2, 3]}, "xiji": {"description": "喜忌", "type": "array", "items": {"$ref": "#/definitions/v1.MingliRuleConditionXiji"}}, "zuodui": {"description": "坐对", "type": "array", "items": {"$ref": "#/definitions/v1.MingliRuleConditionZuodui"}}}}, "v1.ListPortalSectionRequest": {"type": "object", "properties": {"search": {"description": "关键词搜索：名称/备注", "type": "string"}}}, "v1.ListPortalSectionResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/v1.ListPortalSectionResponseDataItem"}}, "message": {"type": "string"}}}, "v1.ListPortalSectionResponseDataItem": {"type": "object", "properties": {"createdAt": {"description": "创建时间", "type": "string"}, "id": {"description": "栏目ID", "type": "integer"}, "isEnable": {"description": "是否启用", "type": "boolean"}, "name": {"description": "栏目名称", "type": "string"}, "remark": {"description": "栏目备注", "type": "string"}, "updatedAt": {"description": "更新时间", "type": "string"}}}, "v1.ListTagRequest": {"type": "object"}, "v1.ListTagResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"type": "string"}}, "message": {"type": "string"}}}, "v1.LunarDay": {"type": "object", "properties": {"date": {"description": "日期", "type": "string", "example": "2020-01-01"}, "lunarDate": {"description": "农历日期", "type": "string", "example": "正月初一"}, "name": {"description": "名称", "type": "string", "example": "初一"}}}, "v1.MatchMingliRuleRequest": {"type": "object", "properties": {"birthday": {"description": "出生时间", "type": "string"}, "currentDate": {"description": "当前时间", "type": "string"}, "gender": {"description": "性别", "type": "integer"}}}, "v1.MatchMingliRuleResponseDataCondition": {"type": "object", "properties": {"criterion": {"description": "条件依据", "type": "string", "example": "依据"}, "id": {"description": "条件ID", "type": "integer", "example": 1}}}, "v1.MatchMingliRulesResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.MatchMingliRulesResponseData"}, "message": {"type": "string"}}}, "v1.MatchMingliRulesResponseData": {"type": "object", "properties": {"birth": {"description": "出生信息（索引：0-年、1-月、2-日、3-时）", "type": "array", "items": {"type": "string"}}, "rules": {"description": "匹配规则", "type": "array", "items": {"$ref": "#/definitions/v1.MatchMingliRulesResponseDataRule"}}}}, "v1.MatchMingliRulesResponseDataRule": {"type": "object", "properties": {"conditions": {"description": "匹配条件", "type": "array", "items": {"$ref": "#/definitions/v1.MatchMingliRuleResponseDataCondition"}}, "id": {"description": "规则ID", "type": "integer", "example": 1}, "result": {"description": "判断结果", "type": "string", "example": "判断结果"}}}, "v1.MingliRuleConditionXiji": {"type": "object", "properties": {"checked": {"description": "是否选中", "type": "boolean", "example": true}, "key": {"type": "integer", "example": 1}, "values": {"type": "array", "items": {"$ref": "#/definitions/model.MingliRuleConditionValue"}}}}, "v1.MingliRuleConditionZuodui": {"type": "object", "properties": {"checked": {"description": "是否选中", "type": "boolean", "example": true}, "key": {"type": "integer", "example": 1}, "values": {"type": "array", "items": {"$ref": "#/definitions/model.MingliRuleConditionValue"}}}}, "v1.MpQrCodePageListRequest": {"type": "object", "required": ["pageNum", "pageSize"], "properties": {"pageNum": {"type": "integer", "minimum": 1, "example": 1}, "pageSize": {"type": "integer", "minimum": 1, "example": 10}, "param": {"$ref": "#/definitions/v1.MpQrCodePageListRequestParam"}}}, "v1.MpQrCodePageListRequestParam": {"type": "object", "properties": {"appIDs": {"type": "array", "items": {"type": "integer"}}, "createAtEnd": {"type": "string"}, "createAtStart": {"type": "string"}, "remark": {"type": "string"}, "sceneStr": {"type": "string"}}}, "v1.MpQrCodePageListResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.MpQrCodePageListResponseData"}, "message": {"type": "string"}}}, "v1.MpQrCodePageListResponseData": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/v1.MpQrCodePageListResponseDataItem"}}, "total": {"type": "integer"}}}, "v1.MpQrCodePageListResponseDataItem": {"type": "object", "properties": {"appID": {"type": "string"}, "appName": {"type": "string"}, "createdAt": {"type": "string"}, "imageData": {"type": "array", "items": {"type": "integer"}}, "imageType": {"type": "string"}, "page": {"type": "string"}, "remark": {"type": "string"}, "sceneStr": {"type": "string"}}}, "v1.MpQrCodeRequest": {"type": "object", "required": ["appID", "channelID", "page"], "properties": {"appID": {"type": "integer"}, "channelID": {"type": "integer"}, "page": {"type": "string"}}}, "v1.OaAppMaterialResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.OaAppMaterialResponseData"}, "message": {"type": "string"}}}, "v1.OaAppMaterialResponseData": {"type": "object", "properties": {"mediaID": {"type": "string"}, "url": {"type": "string"}}}, "v1.OaCreateMenuRequest": {"type": "object", "properties": {"button": {"type": "array", "items": {"$ref": "#/definitions/request.Button"}}}}, "v1.OaCreateMenuResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.OaCreateMenuResponseData"}, "message": {"type": "string"}}}, "v1.OaCreateMenuResponseData": {"type": "object"}, "v1.OaCreateQrcodeRequest": {"type": "object", "required": ["scene_str"], "properties": {"remark": {"type": "string"}, "scene_str": {"type": "string", "maxLength": 64, "minLength": 1}}}, "v1.OaCreateQrcodeResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.OaCreateQrcodeResponseData"}, "message": {"type": "string"}}}, "v1.OaCreateQrcodeResponseData": {"type": "object", "properties": {"expire_seconds": {"type": "integer"}, "ticket": {"type": "string"}, "url": {"type": "string"}}}, "v1.OaGetMenuDetailResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.OaGetMenuDetailResponseData"}, "message": {"type": "string"}}}, "v1.OaGetMenuDetailResponseData": {"type": "object", "properties": {"conditionalmenu": {"type": "array", "items": {"$ref": "#/definitions/response.ConditionalMenu"}}, "menu": {"$ref": "#/definitions/response.Menu"}}}, "v1.OrderDetailRequest": {"type": "object", "required": ["orderNo"], "properties": {"orderNo": {"description": "订单编号", "type": "string"}}}, "v1.OrderDetailResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.OrderDetailResponseData"}, "message": {"type": "string"}}}, "v1.OrderDetailResponseData": {"type": "object", "properties": {"amount": {"description": "订单金额", "type": "integer"}, "app_id": {"type": "integer"}, "createdAt": {"description": "创建时间", "type": "string"}, "expireTime": {"description": "订单过期时间", "type": "string"}, "extraInfo": {"description": "额外信息 // 备注", "type": "object", "additionalProperties": {}}, "id": {"type": "integer"}, "ip": {"type": "string"}, "orderNo": {"description": "订单号", "type": "string"}, "payAmount": {"description": "支付金额", "type": "integer"}, "payChannel": {"description": "支付渠道：1-微信支付，2-支付宝", "type": "integer"}, "payStatus": {"description": "支付状态：0-待支付，1-支付成功，2-支付失败，3-已退款", "type": "integer"}, "payTime": {"description": "支付时间", "type": "string"}, "productId": {"description": "商品ID", "type": "integer"}, "productSnapshot": {"description": "商品快照", "type": "array", "items": {"type": "integer"}}, "quantity": {"description": "购买数量", "type": "integer"}, "remark": {"type": "string"}, "source": {"description": "订单来源：0-未知，1-安卓应用，2-苹果应用，3-安卓h5，4-苹果h5", "type": "integer"}, "status": {"description": "订单状态：0-待支付，1-已完成，2-已取消，3-已退款", "type": "integer"}, "transactionId": {"description": "支付平台交易号", "type": "string"}, "ua": {"type": "string"}, "updatedAt": {"description": "更新时间", "type": "string"}, "userId": {"description": "用户ID", "type": "string"}}}, "v1.PageListAppChannelRequest": {"type": "object", "required": ["pageNum", "pageSize"], "properties": {"pageNum": {"type": "integer", "minimum": 1, "example": 1}, "pageSize": {"type": "integer", "minimum": 1, "example": 10}, "param": {"$ref": "#/definitions/v1.PageListAppChannelRequestParam"}}}, "v1.PageListAppChannelRequestParam": {"type": "object"}, "v1.PageListAppChannelResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.PageListAppChannelResponseData"}, "message": {"type": "string"}}}, "v1.PageListAppChannelResponseData": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/v1.PageListAppChannelResponseDataItem"}}, "total": {"type": "integer"}}}, "v1.PageListAppChannelResponseDataItem": {"type": "object", "properties": {"code": {"type": "string"}, "createdAt": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "remark": {"type": "string"}, "statistics": {"$ref": "#/definitions/v1.ChannelStatistics"}}}, "v1.PageListAppUserPromotionRequest": {"type": "object", "required": ["pageNum", "pageSize"], "properties": {"pageNum": {"type": "integer", "minimum": 1, "example": 1}, "pageSize": {"type": "integer", "minimum": 1, "example": 10}, "param": {"$ref": "#/definitions/v1.PageListAppUserPromotionRequestParam"}}}, "v1.PageListAppUserPromotionRequestParam": {"type": "object"}, "v1.PageListAppUserPromotionResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.PageListAppUserPromotionResponseData"}, "message": {"type": "string"}}}, "v1.PageListAppUserPromotionResponseData": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/v1.PageListAppUserPromotionResponseDataItem"}}, "total": {"type": "integer"}}}, "v1.PageListAppUserPromotionResponseDataItem": {"type": "object", "properties": {"createdAt": {"description": "创建时间", "type": "string"}, "displayName": {"description": "显示名称", "type": "string"}, "level": {"description": "渠道级别：1-推广渠道用户", "type": "integer"}, "level1Ratio": {"description": "一级返利比例", "type": "integer"}, "level2Ratio": {"description": "二级返利比例", "type": "integer"}, "name": {"description": "渠道名称", "type": "string"}, "password": {"description": "渠道密码", "type": "string"}, "phone": {"description": "渠道手机号", "type": "string"}, "promotionCode": {"description": "推广码（邀请码）", "type": "string"}, "rebateWithdrawn": {"description": "已提现返利", "type": "integer"}, "remark": {"description": "备注", "type": "string"}, "updatedAt": {"description": "更新时间", "type": "string"}}}, "v1.PageListAppUserRequest": {"type": "object", "required": ["pageNum", "pageSize"], "properties": {"pageNum": {"type": "integer", "minimum": 1, "example": 1}, "pageSize": {"type": "integer", "minimum": 1, "example": 10}, "param": {"$ref": "#/definitions/v1.PageListAppUserRequestParam"}}}, "v1.PageListAppUserRequestParam": {"type": "object", "properties": {"name": {"type": "string"}, "phone": {"type": "string"}, "roleIDs": {"type": "array", "items": {"type": "integer"}}, "signupApplicationIDs": {"type": "array", "items": {"type": "integer"}}, "signupTimeEnd": {"type": "string"}, "signupTimeStart": {"type": "string"}}}, "v1.PageListAppUserResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.PageListAppUserResponseData"}, "message": {"type": "string"}}}, "v1.PageListAppUserResponseData": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/v1.PageListAppUserResponseDataItem"}}, "total": {"type": "integer"}}}, "v1.PageListAppUserResponseDataItem": {"type": "object", "properties": {"avatar": {"type": "string"}, "channel": {"type": "string"}, "channelID": {"type": "integer"}, "channelName": {"type": "string"}, "countryCode": {"type": "string"}, "displayName": {"type": "string"}, "isMember": {"description": "是否会员：0-否，1-是，2-曾是", "type": "integer"}, "isPaid": {"description": "是否付费", "type": "boolean"}, "lastLoginApp": {"type": "string"}, "lastLoginTime": {"type": "string"}, "memberDuration": {"type": "array", "items": {"$ref": "#/definitions/v1.AppUserMemberDuration"}}, "name": {"type": "string"}, "phone": {"type": "string"}, "signupApplication": {"type": "string"}, "signupApplicationID": {"type": "integer"}, "signupApplicationName": {"type": "string"}, "signupTime": {"type": "string"}, "user_id": {"type": "string"}}}, "v1.PageListAppUserVipRequest": {"type": "object", "required": ["pageNum", "pageSize"], "properties": {"pageNum": {"type": "integer", "minimum": 1, "example": 1}, "pageSize": {"type": "integer", "minimum": 1, "example": 10}, "param": {"$ref": "#/definitions/v1.PageListAppUserVipRequestParam"}}}, "v1.PageListAppUserVipRequestParam": {"type": "object", "properties": {"appIDs": {"type": "array", "items": {"type": "integer"}}, "expireTimeEnd": {"type": "string"}, "expireTimeStart": {"type": "string"}, "name": {"type": "string"}, "phone": {"type": "string"}, "roleIDs": {"type": "array", "items": {"type": "integer"}}}}, "v1.PageListAppUserVipResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.PageListAppUserVipResponseData"}, "message": {"type": "string"}}}, "v1.PageListAppUserVipResponseData": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/v1.PageListAppUserVipResponseDataItem"}}, "total": {"type": "integer"}}}, "v1.PageListAppUserVipResponseDataItem": {"type": "object", "properties": {"avatar": {"type": "string"}, "channel": {"type": "string"}, "channelID": {"type": "integer"}, "channelName": {"type": "string"}, "countryCode": {"type": "string"}, "displayName": {"type": "string"}, "expireTime": {"type": "string"}, "name": {"type": "string"}, "phone": {"type": "string"}, "roleID": {"type": "integer"}, "roleName": {"type": "string"}, "signupApplication": {"type": "string"}, "signupApplicationID": {"type": "integer"}, "signupApplicationName": {"type": "string"}, "signupTime": {"type": "string"}, "userID": {"type": "string"}}}, "v1.PageListAppVersionRequest": {"type": "object", "required": ["pageNum", "pageSize"], "properties": {"pageNum": {"type": "integer", "minimum": 1, "example": 1}, "pageSize": {"type": "integer", "minimum": 1, "example": 10}, "param": {"$ref": "#/definitions/v1.PageListAppVersionRequestParam"}}}, "v1.PageListAppVersionRequestParam": {"type": "object", "properties": {"appIDs": {"description": "应用ID", "type": "array", "items": {"type": "integer"}}, "osType": {"description": "1:android, 2:ios", "type": "array", "items": {"type": "integer"}}, "remark": {"description": "备注", "type": "string"}, "updateNote": {"description": "更新说明", "type": "string"}, "versionName": {"description": "版本名称", "type": "string"}}}, "v1.PageListAppVersionResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.PageListAppVersionResponseData"}, "message": {"type": "string"}}}, "v1.PageListAppVersionResponseData": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/v1.PageListAppVersionResponseDateItem"}}, "total": {"type": "integer"}}}, "v1.PageListAppVersionResponseDateItem": {"type": "object", "properties": {"appID": {"description": "应用ID", "type": "integer"}, "appName": {"description": "应用名称", "type": "string"}, "createdAt": {"description": "创建时间", "type": "string"}, "id": {"description": "ID", "type": "integer"}, "isForceUpdate": {"description": "是否强制更新", "type": "boolean"}, "isHotUpdate": {"description": "是否热更新", "type": "boolean"}, "osType": {"description": "1:android, 2:ios", "type": "integer"}, "remark": {"description": "备注", "type": "string"}, "status": {"description": "状态：0-未发布、1-已发布、2-已撤回", "type": "integer"}, "updateNote": {"description": "更新说明", "type": "string"}, "updatedAt": {"description": "更新时间", "type": "string"}, "url": {"description": "下载地址", "type": "string"}, "versionCode": {"description": "版本号", "type": "integer"}, "versionName": {"description": "版本名称", "type": "string"}}}, "v1.PageListFeedbackRequest": {"type": "object", "required": ["pageNum", "pageSize"], "properties": {"pageNum": {"type": "integer", "minimum": 1, "example": 1}, "pageSize": {"type": "integer", "minimum": 1, "example": 10}, "param": {"$ref": "#/definitions/v1.PageListFeedbackRequestParam"}}}, "v1.PageListFeedbackRequestParam": {"type": "object"}, "v1.PageListFeedbackResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.PageListFeedbackResponseData"}, "message": {"type": "string"}}}, "v1.PageListFeedbackResponseData": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/v1.PageListFeedbackResponseDataItem"}}, "total": {"type": "integer"}}}, "v1.PageListFeedbackResponseDataItem": {"type": "object", "properties": {"application": {"type": "string"}, "applicationName": {"type": "string"}, "avatar": {"type": "string"}, "content": {"type": "string"}, "createdAtStr": {"type": "string"}, "displayName": {"type": "string"}, "id": {"type": "integer"}, "imageKey": {"type": "array", "items": {"type": "string"}}, "imageUrl": {"type": "array", "items": {"type": "string"}}, "name": {"type": "string"}, "phone": {"type": "string"}, "replyContent": {"type": "string"}, "replyImageKey": {"type": "array", "items": {"type": "string"}}, "replyImageUrl": {"type": "array", "items": {"type": "string"}}, "replyTimeStr": {"type": "string"}, "userId": {"type": "string"}}}, "v1.PageListMingliRuleConditionRequest": {"type": "object", "required": ["pageNum", "pageSize"], "properties": {"pageNum": {"type": "integer", "minimum": 1, "example": 1}, "pageSize": {"type": "integer", "minimum": 1, "example": 10}, "param": {"$ref": "#/definitions/v1.PageListMingliRuleConditionRequestParam"}}}, "v1.PageListMingliRuleConditionRequestParam": {"type": "object", "required": ["mingliRuleId"], "properties": {"mingliRuleId": {"description": "命理规则ID", "type": "integer", "example": 1}}}, "v1.PageListMingliRuleConditionResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.PageListMingliRuleConditionResponseData"}, "message": {"type": "string"}}}, "v1.PageListMingliRuleConditionResponseData": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/v1.PageListMingliRuleConditionResponseItem"}}, "total": {"type": "integer"}}}, "v1.PageListMingliRuleConditionResponseItem": {"type": "object", "required": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "properties": {"category": {"description": "类别：2-坐对、3-喜忌、1-全选", "type": "integer", "example": 1}, "criterion": {"description": "判断依据说明", "type": "string", "example": "判断依据说明"}, "gender": {"description": "日主性别（2-男、3-女、1-无关性别）", "type": "integer", "example": 1}, "id": {"description": "主键ID", "type": "integer", "example": 1}, "name": {"description": "条件名称", "type": "string", "example": "条件名称"}, "no": {"description": "条件编号", "type": "string", "example": "条件编号"}, "type": {"description": "条件类型（坐-对）", "type": "integer", "example": 1}, "weizhiDui": {"description": "位置对（1-年、2-月、3-日、4-时、5-大运、6-流年、7-流月、8-流日、9-流时）", "type": "array", "items": {"type": "integer"}, "example": [1, 2, 3]}, "weizhiZuo": {"description": "位置坐（1-年、2-月、3-日、4-时、5-大运、6-流年、7-流月、8-流日、9-流时）", "type": "array", "items": {"type": "integer"}, "example": [1, 2, 3]}, "xiji": {"description": "喜忌", "type": "array", "items": {"$ref": "#/definitions/v1.MingliRuleConditionXiji"}}, "zuodui": {"description": "坐对", "type": "array", "items": {"$ref": "#/definitions/v1.MingliRuleConditionZuodui"}}}}, "v1.PageListMingliRuleRequest": {"type": "object", "required": ["pageNum", "pageSize"], "properties": {"pageNum": {"type": "integer", "minimum": 1, "example": 1}, "pageSize": {"type": "integer", "minimum": 1, "example": 10}, "param": {"$ref": "#/definitions/v1.PageListMingliRuleRequestParam"}}}, "v1.PageListMingliRuleRequestParam": {"type": "object"}, "v1.PageListMingliRuleResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.PageListMingliRuleResponseData"}, "message": {"type": "string"}}}, "v1.PageListMingliRuleResponseData": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/v1.PageListMingliRuleResponseItem"}}, "total": {"type": "integer"}}}, "v1.PageListMingliRuleResponseItem": {"type": "object", "properties": {"creatorName": {"type": "string", "example": "创建者"}, "description": {"type": "string", "example": "规则说明"}, "id": {"type": "integer", "example": 1}, "isEnabled": {"type": "boolean", "example": true}, "module": {"type": "integer", "example": 1}, "moduleName": {"type": "string", "example": "应用模块名称"}, "name": {"type": "string", "example": "规则名称"}, "no": {"type": "string", "example": "NO1001"}, "result": {"type": "string", "example": "判断结果"}, "source": {"description": "规则来源：1 管理员 2 用户", "type": "integer", "example": 1}}}, "v1.PageListPaipanRecordRequest": {"type": "object", "required": ["pageNum", "pageSize"], "properties": {"pageNum": {"type": "integer", "minimum": 1, "example": 1}, "pageSize": {"type": "integer", "minimum": 1, "example": 10}, "param": {"$ref": "#/definitions/v1.PageListPaipanRecordRequestParam"}}}, "v1.PageListPaipanRecordRequestParam": {"type": "object", "properties": {"appIDs": {"description": "应用ID：2-排盘、3-万年历、4-运势、5-论财", "type": "array", "items": {"type": "integer"}}, "application": {"description": "应用标识", "type": "string"}, "bazi": {"description": "八字", "type": "string"}, "birthTimeEnd": {"description": "出生日期结束", "type": "string"}, "birthTimeStart": {"description": "出生日期开始", "type": "string"}, "birthTimeSunEnd": {"description": "真太阳时结束", "type": "string"}, "birthTimeSunStart": {"description": "真太阳时开始", "type": "string"}, "birthplace": {"description": "出生地", "type": "string"}, "gender": {"description": "性别：1-男、2-女", "type": "array", "items": {"type": "integer"}}, "name": {"description": "命例名称", "type": "string"}}}, "v1.PageListPaipanRecordResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.PageListPaipanRecordResponseData"}, "message": {"type": "string"}}}, "v1.PageListPaipanRecordResponseData": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/v1.PageListPaipanRecordResponseDataItem"}}, "total": {"type": "integer"}}}, "v1.PageListPaipanRecordResponseDataItem": {"type": "object", "properties": {"appID": {"description": "应用ID", "type": "integer", "example": 1}, "appName": {"description": "应用名称", "type": "string"}, "appPlatformID": {"description": "应用平台ID", "type": "integer", "example": 1}, "appPlatformName": {"description": "应用平台名称", "type": "string"}, "bazi": {"description": "八字", "type": "array", "items": {"type": "string"}}, "birthplace": {"description": "出生地", "type": "array", "items": {"type": "string"}}, "birthtime": {"description": "出生日期", "type": "string", "example": "2021-01-01 00:00:00"}, "birthtimeLunar": {"description": "农历出生日期", "type": "string"}, "birthtimeSun": {"description": "真太阳时", "type": "string"}, "createdAt": {"description": "创建时间", "type": "string"}, "createdTime": {"description": "创建时间", "type": "string"}, "extraInfo": {"description": "额外信息", "type": "object", "additionalProperties": {}}, "gender": {"description": "性别：1-男, 2-女", "type": "string", "example": "1"}, "id": {"type": "integer"}, "name": {"description": "命例名称", "type": "string"}, "saveTime": {"description": "保存时间", "type": "string"}, "type": {"description": "类型", "type": "string"}, "userAgent": {"description": "用户代理", "type": "string"}, "userID": {"description": "用户ID", "type": "string"}}}, "v1.PageListPortalArticleRequest": {"type": "object", "required": ["pageNum", "pageSize"], "properties": {"pageNum": {"type": "integer", "minimum": 1, "example": 1}, "pageSize": {"type": "integer", "minimum": 1, "example": 10}, "param": {"$ref": "#/definitions/v1.PageListPortalArticleRequestParam"}}}, "v1.PageListPortalArticleRequestParam": {"type": "object", "properties": {"createTime": {"description": "创建时间", "allOf": [{"$ref": "#/definitions/v1.TimeRange"}]}, "isModify": {"description": "是否修改", "type": "boolean"}, "isPublish": {"description": "是否发布", "type": "boolean"}, "isTop": {"description": "是否置顶", "type": "boolean"}, "keywords": {"description": "关键词搜索：标题/作者/内容/备注", "type": "string"}, "modifyTime": {"description": "修改时间（仅已修改）", "allOf": [{"$ref": "#/definitions/v1.TimeRange"}]}, "publishTime": {"description": "发布时间（仅已发布）", "allOf": [{"$ref": "#/definitions/v1.TimeRange"}]}, "sectionIds": {"description": "栏目ID", "type": "array", "items": {"type": "integer"}}, "topTime": {"description": "置顶时间（仅已置顶）", "allOf": [{"$ref": "#/definitions/v1.TimeRange"}]}}}, "v1.PageListPortalArticleResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.PageListPortalArticleResponseData"}, "message": {"type": "string"}}}, "v1.PageListPortalArticleResponseData": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/v1.PageListPortalArticleResponseDataItem"}}, "total": {"type": "integer"}}}, "v1.PageListPortalArticleResponseDataItem": {"type": "object", "properties": {"author": {"description": "作者", "type": "string"}, "content": {"description": "文章内容", "type": "string"}, "createdAt": {"description": "创建时间", "type": "string"}, "id": {"description": "文章ID", "type": "integer"}, "isModify": {"description": "是否修改", "type": "boolean"}, "isPublish": {"description": "是否发布", "type": "boolean"}, "isTop": {"description": "是否置顶", "type": "boolean"}, "modifyTime": {"description": "修改时间", "type": "string"}, "publishTime": {"description": "发布时间", "type": "string"}, "remark": {"description": "备注", "type": "string"}, "sectionId": {"description": "栏目ID", "type": "integer"}, "sectionName": {"description": "栏目名称", "type": "string"}, "tags": {"description": "标签", "type": "array", "items": {"type": "string"}}, "title": {"description": "文章标题", "type": "string"}, "topTime": {"description": "置顶时间", "type": "string"}, "updatedAt": {"description": "更新时间", "type": "string"}}}, "v1.PageListUserMingliRequest": {"type": "object", "required": ["pageNum", "pageSize"], "properties": {"pageNum": {"type": "integer", "minimum": 1, "example": 1}, "pageSize": {"type": "integer", "minimum": 1, "example": 10}, "param": {"$ref": "#/definitions/v1.PageListUserMingliRequestParam"}}}, "v1.PageListUserMingliRequestParam": {"type": "object", "properties": {"appIDs": {"description": "应用ID：2-排盘、3-万年历、4-运势、5-论财", "type": "array", "items": {"type": "integer"}}, "bazi": {"description": "八字", "type": "string"}, "birthTimeEnd": {"description": "出生日期结束", "type": "string"}, "birthTimeStart": {"description": "出生日期开始", "type": "string"}, "birthTimeSunEnd": {"description": "真太阳时结束", "type": "string"}, "birthTimeSunStart": {"description": "真太阳时开始", "type": "string"}, "birthplace": {"description": "出生地", "type": "string"}, "gender": {"description": "性别：1-男、2-女", "type": "array", "items": {"type": "integer"}}, "name": {"description": "命例名称", "type": "string"}}}, "v1.PageListUserMingliResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.PageListUserMingliResponseData"}, "message": {"type": "string"}}}, "v1.PageListUserMingliResponseData": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/v1.PageListUserMingliResponseDataItem"}}, "total": {"type": "integer"}}}, "v1.PageListUserMingliResponseDataItem": {"type": "object", "properties": {"address": {"description": "地址", "type": "array", "items": {"type": "string"}, "example": ["北京市", "市辖区", "东城区"]}, "appID": {"description": "应用：2-排盘、3-万年历、4-运势、5-论财", "type": "integer", "example": 2}, "appName": {"description": "应用名称", "type": "string"}, "bazi": {"description": "八字：年份干支,月份干支,日期干支,时辰干支", "type": "array", "items": {"type": "string"}}, "birthplace": {"description": "出生地", "type": "array", "items": {"type": "string"}, "example": ["[\"北京市\"", "\"市辖区\"", "\"东城区\"]"]}, "birthtime": {"description": "出生时间（公历）", "type": "string", "example": "2021-01-01 12:00:00"}, "birthtimeLunar": {"description": "出生时间（农历）", "type": "string", "example": "2021年闰四月十一子时"}, "birthtimeSun": {"description": "真太阳时", "type": "string", "example": "2021-01-01 12:00:00"}, "dayun": {"description": "大运", "allOf": [{"$ref": "#/definitions/model.UserMingliDayun"}]}, "gender": {"description": "性别：1-男，2-女", "type": "integer", "example": 1}, "groupID": {"description": "分组ID", "type": "integer", "example": 1}, "groupName": {"description": "分组名称", "type": "string"}, "id": {"description": "ID", "type": "integer", "example": 1}, "isDefault": {"description": "是否默认", "type": "boolean", "example": true}, "lunarBirthtime": {"description": "出生时间（农历）", "type": "string", "example": "2021年闰四月十一子时"}, "name": {"description": "姓名", "type": "string", "example": "张三"}, "userID": {"description": "用户ID", "type": "string", "example": "1"}, "wuxing": {"description": "五行：用神,喜神,忌神,仇神,闲神", "type": "array", "items": {"type": "string"}}, "xiaoyun": {"description": "小运", "allOf": [{"$ref": "#/definitions/model.UserMingliXiaoyun"}]}}}, "v1.PageListUserOrderRequest": {"type": "object", "required": ["pageNum", "pageSize"], "properties": {"pageNum": {"type": "integer", "minimum": 1, "example": 1}, "pageSize": {"type": "integer", "minimum": 1, "example": 10}, "param": {"$ref": "#/definitions/v1.PageListUserOrderRequestParam"}}}, "v1.PageListUserOrderRequestParam": {"type": "object", "properties": {"createdTimeEnd": {"description": "创建时间结束", "type": "string"}, "createdTimeStart": {"description": "创建时间开始", "type": "string"}, "orderNo": {"description": "订单ID", "type": "string"}, "payAmountMax": {"description": "支付金额最大值", "type": "integer"}, "payAmountMin": {"description": "支付金额最小值", "type": "integer"}, "payStatus": {"description": "支付状态：0-待支付、1-已支付、2-支付失败、3-已退款", "type": "array", "items": {"type": "integer"}}, "payTimeEnd": {"description": "支付时间结束", "type": "string"}, "payTimeStart": {"description": "支付时间开始", "type": "string"}, "productID": {"description": "商品ID", "type": "array", "items": {"type": "integer"}}, "skuName": {"description": "SKU名称", "type": "string"}, "userID": {"description": "用户ID", "type": "array", "items": {"type": "string"}}, "userName": {"description": "用户名", "type": "string"}, "userPhone": {"description": "用户手机号", "type": "string"}}}, "v1.PageListUserOrderResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.PageListUserOrderResponseData"}, "message": {"type": "string"}}}, "v1.PageListUserOrderResponseData": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/v1.PageListUserOrderResponseDataItem"}}, "total": {"type": "integer"}}}, "v1.PageListUserOrderResponseDataItem": {"type": "object", "properties": {"amount": {"description": "订单金额", "type": "integer"}, "appId": {"description": "app id", "type": "string"}, "appName": {"description": "app name", "type": "string"}, "createdTime": {"description": "创建时间", "type": "string"}, "expireTime": {"description": "订单未支付失效时间", "type": "string"}, "ip": {"description": "ip", "type": "string"}, "orderNo": {"description": "订单号", "type": "string"}, "payAmount": {"description": "实际支付金额", "type": "integer"}, "payChannel": {"description": "支付渠道：1-微信、2-支付宝", "type": "integer"}, "payStatus": {"description": "支付状态：0-待支付、1-已支付、2-支付失败、3-已退款", "type": "integer"}, "payTime": {"description": "支付时间", "type": "string"}, "productId": {"description": "商品ID", "type": "integer"}, "productSnapshot": {"description": "商品快照", "allOf": [{"$ref": "#/definitions/v1.ProductSnapshot"}]}, "quantity": {"description": "购买数量", "type": "integer"}, "source": {"description": "订单来源：0-未知，1-安卓应用，2-苹果应用，3-安卓h5，4-苹果h5", "type": "integer"}, "transactionId": {"description": "交易流水号", "type": "string"}, "ua": {"description": "ua", "type": "string"}, "userAvatar": {"description": "用户头像", "type": "string"}, "userDisplayName": {"description": "用户昵称", "type": "string"}, "userId": {"description": "用户ID", "type": "string"}, "userName": {"description": "用户名", "type": "string"}, "userPhone": {"description": "用户手机号", "type": "string"}}}, "v1.ProductSnapshot": {"type": "object", "properties": {"enable": {"description": "是否启用", "type": "integer"}, "id": {"description": "ID", "type": "integer"}, "remark": {"description": "备注", "type": "string"}, "sku_code": {"description": "SKU编码", "type": "string"}, "sku_name": {"description": "SKU名称", "type": "string"}, "stock": {"description": "库存", "type": "integer"}, "type": {"description": "类型", "type": "integer"}}}, "v1.PublishAppVersionRequest": {"type": "object", "required": ["id"], "properties": {"id": {"description": "ID", "type": "integer"}}}, "v1.PublishAppVersionResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "message": {"type": "string"}}}, "v1.QueryTermRequest": {"type": "object", "properties": {"name": {"type": "string", "example": "术语"}}}, "v1.QueryTermResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.QueryTermResponseData"}, "message": {"type": "string"}}}, "v1.QueryTermResponseData": {"type": "object", "properties": {"category1": {"type": "string", "example": "类别1"}, "description": {"type": "string", "example": "描述"}, "name": {"type": "string", "example": "术语"}}}, "v1.QwBillCommodity": {"type": "object", "properties": {"amount": {"description": "商品数量", "type": "integer"}, "description": {"description": "商品描述", "type": "string"}}}, "v1.QwBillContact": {"type": "object", "properties": {"address": {"description": "联系人地址", "type": "string"}, "name": {"description": "联系人姓名", "type": "string"}, "phone": {"description": "联系人电话", "type": "string"}}}, "v1.QwBillMiniProgram": {"type": "object", "properties": {"appid": {"description": "小程序appid", "type": "string"}, "name": {"description": "小程序名称", "type": "string"}}}, "v1.QwBillRefund": {"type": "object", "properties": {"out_refund_no": {"description": "退款单号", "type": "string"}, "refund_comment": {"description": "退款备注", "type": "string"}, "refund_fee": {"description": "退款金额（单位：分）", "type": "integer"}, "refund_reqtime": {"description": "退款发起时间", "type": "integer"}, "refund_status": {"description": "退款状态：0-已申请退款 1-退款处理中 2-退款成功 3-退款关闭 4-退款异常 5-审批中 6-审批失败 7-审批取消", "type": "integer"}, "refund_userid": {"description": "退款发起人ID", "type": "string"}}}, "v1.QwContactFollowUsersRequest": {"type": "object"}, "v1.QwContactFollowUsersResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/v1.QwMember"}}, "message": {"type": "string"}}}, "v1.QwCreateAcquisitionLinkRequest": {"type": "object", "required": ["appID", "name", "platformID", "userIDs"], "properties": {"appID": {"description": "应用ID", "type": "integer"}, "name": {"description": "联系我的配置名称", "type": "string"}, "platformID": {"description": "平台ID", "type": "integer"}, "skipVerify": {"description": "联系我是否跳过验证", "type": "boolean"}, "userIDs": {"description": "联系我的成员列表", "type": "array", "items": {"type": "string"}}}}, "v1.QwCreateAcquisitionLinkResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "message": {"type": "string"}}}, "v1.QwCreateContactWayRequest": {"type": "object", "required": ["appID", "name", "platformID", "userIDs"], "properties": {"addState": {"description": "联系我的添加参数", "type": "string"}, "appID": {"description": "应用ID", "type": "integer"}, "name": {"description": "联系我的配置名称", "type": "string"}, "platformID": {"description": "平台ID", "type": "integer"}, "skipVerify": {"description": "联系我是否跳过验证", "type": "boolean"}, "userIDs": {"description": "联系我的成员列表", "type": "array", "items": {"type": "string"}}}}, "v1.QwCreateContactWayResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "message": {"type": "string"}}}, "v1.QwDeleteContactWayRequest": {"type": "object", "required": ["id"], "properties": {"id": {"description": "联系我的配置ID", "type": "integer"}}}, "v1.QwDeleteContactWayResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "message": {"type": "string"}}}, "v1.QwDepartmentTreeNode": {"type": "object", "properties": {"children": {"type": "array", "items": {"$ref": "#/definitions/v1.QwDepartmentTreeNode"}}, "id": {"description": "部门ID", "type": "integer"}, "members": {"description": "部门成员", "type": "array", "items": {"$ref": "#/definitions/v1.QwMember"}}, "name": {"description": "部门名称", "type": "string"}, "order": {"description": "部门排序", "type": "integer"}, "parent_id": {"description": "父部门ID", "type": "integer"}}}, "v1.QwDepartmentsRequest": {"type": "object", "properties": {"containsMember": {"description": "是否包含成员", "type": "boolean"}, "parentID": {"description": "父部门ID", "type": "integer"}}}, "v1.QwDepartmentsResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "array", "items": {"$ref": "#/definitions/v1.QwDepartmentTreeNode"}}, "message": {"type": "string"}}}, "v1.QwMember": {"type": "object", "properties": {"name": {"description": "成员名称", "type": "string"}, "user_id": {"description": "成员UserID", "type": "string"}}}, "v1.QwPageListBillRequest": {"type": "object", "required": ["pageNum", "pageSize"], "properties": {"pageNum": {"type": "integer", "minimum": 1, "example": 1}, "pageSize": {"type": "integer", "minimum": 1, "example": 10}, "param": {"$ref": "#/definitions/v1.QwPageListBillRequestParam"}}}, "v1.QwPageListBillRequestParam": {"type": "object", "properties": {"billType": {"description": "交易类型：0-收款记录 1-退款记录", "type": "integer"}, "id": {"description": "联系我的配置ID", "type": "integer"}, "payEndTime": {"description": "支付时间结束", "type": "string"}, "payStartTime": {"description": "支付时间起始", "type": "string"}, "payeeUserIDs": {"description": "收款人UserID列表", "type": "array", "items": {"type": "string"}}, "tradeState": {"description": "交易状态（退款记录不返回该字段）：1-已完成 3-已完成有退款", "type": "integer"}, "transactionID": {"description": "交易单号", "type": "string"}}}, "v1.QwPageListBillResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.QwPageListBillResponseData"}, "message": {"type": "string"}}}, "v1.QwPageListBillResponseData": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/v1.QwPageListBillResponseItem"}}, "total": {"type": "integer"}}}, "v1.QwPageListBillResponseItem": {"type": "object", "properties": {"bill_type": {"description": "交易类型：0-收款记录 1-退款记录", "type": "integer"}, "commodity_list": {"description": "商品信息详情列表（仅收款记录返回）", "type": "array", "items": {"$ref": "#/definitions/v1.QwBillCommodity"}}, "contact_info": {"description": "联系人信息（如创建收款项目时设置为不需要联系地址，则该字段为空，退款记录不返回该字段）", "allOf": [{"$ref": "#/definitions/v1.QwBillContact"}]}, "external_userid": {"description": "付款人的userid", "type": "string"}, "id": {"description": "主键ID", "type": "integer"}, "mch_id": {"description": "收款商户号id", "type": "string"}, "miniprogram_info": {"description": "小程序信息（收款方式为小程序时返回该字段）", "allOf": [{"$ref": "#/definitions/v1.QwBillMiniProgram"}]}, "out_refund_no": {"description": "商户退款单号（仅退款记录返回该字段）", "type": "string"}, "out_trade_no": {"description": "商户单号（退款记录返回对应收款记录的商户单号）", "type": "string"}, "pay_time": {"description": "支付时间", "type": "integer"}, "payee_userid": {"description": "收款成员/退款成员的userid", "type": "string"}, "payment_type": {"description": "收款方式：0-聊天中收款 1-收款码收款 2-直播间收款 3-产品图册收款 4-转账 5-小程序", "type": "integer"}, "refund_list": {"description": "退款单据详情列表（仅收款记录返回）", "type": "array", "items": {"$ref": "#/definitions/v1.QwBillRefund"}}, "remark": {"description": "收款/退款备注", "type": "string"}, "total_fee": {"description": "收款总金额（单位：分）", "type": "integer"}, "total_refund_fee": {"description": "退款总金额（单位：分）", "type": "integer"}, "trade_state": {"description": "交易状态（退款记录不返回该字段）：1-已完成 3-已完成有退款", "type": "integer"}, "transaction_id": {"description": "交易单号", "type": "string"}}}, "v1.QwPageListContactFollowRequest": {"type": "object", "required": ["pageNum", "pageSize"], "properties": {"pageNum": {"type": "integer", "minimum": 1, "example": 1}, "pageSize": {"type": "integer", "minimum": 1, "example": 10}, "param": {"$ref": "#/definitions/v1.QwPageListContactFollowRequestParam"}}}, "v1.QwPageListContactFollowRequestParam": {"type": "object", "properties": {"addEndAt": {"description": "添加时间结束", "type": "string"}, "addStartAt": {"description": "添加时间起始", "type": "string"}, "appIDs": {"description": "应用ID", "type": "array", "items": {"type": "integer"}}, "appPlatformIDs": {"description": "应用平台ID", "type": "array", "items": {"type": "integer"}}, "delFlag": {"description": "删除标记", "type": "array", "items": {"type": "integer"}}, "gender": {"description": "性别：1-男 2-女 0-未知", "type": "array", "items": {"type": "integer"}}, "name": {"description": "昵称", "type": "string"}, "phone": {"description": "手机号（手动填写）", "type": "string"}, "realName": {"description": "真实姓名（手动填写）", "type": "string"}, "remark": {"description": "姓名", "type": "string"}, "userIDs": {"description": "成员UserID列表", "type": "array", "items": {"type": "string"}}}}, "v1.QwPageListContactFollowResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.QwPageListContactFollowResponseData"}, "message": {"type": "string"}}}, "v1.QwPageListContactFollowResponseData": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/v1.QwPageListContactFollowResponseItem"}}, "total": {"type": "integer"}}}, "v1.QwPageListContactFollowResponseItem": {"type": "object", "properties": {"addState": {"description": "外部联系人的添加状态", "type": "string"}, "addTime": {"description": "外部联系人的添加时间", "type": "integer"}, "addWay": {"description": "外部联系人添加方式", "type": "integer"}, "appID": {"description": "应用ID（暂无）", "type": "string"}, "appName": {"description": "应用名称（暂无）", "type": "string"}, "appPlatformID": {"description": "应用平台ID（暂无）", "type": "integer"}, "appPlatformName": {"description": "应用平台名称（暂无）", "type": "string"}, "avatar": {"description": "外部联系人的头像", "type": "string"}, "delFlag": {"description": "外部联系人的删除时间", "type": "integer"}, "delSource": {"description": "外部联系人的删除来源", "type": "string"}, "delTime": {"description": "外部联系人的删除时间", "type": "integer"}, "description": {"description": "外部联系人的描述", "type": "string"}, "events": {"description": "外部联系人的跟进事件", "type": "array", "items": {"$ref": "#/definitions/model.QwContactFollowEvent"}}, "gender": {"description": "外部联系人的性别", "type": "integer"}, "id": {"description": "外部联系人的userid", "type": "integer"}, "name": {"description": "外部联系人的名称", "type": "string"}, "phone": {"description": "手机号（手动填写）", "type": "string"}, "realName": {"description": "真实姓名（手动填写）", "type": "string"}, "remark": {"description": "外部联系人的备注", "type": "string"}, "userID": {"description": "外部联系人的userid", "type": "string"}}}, "v1.QwPageListContactWayRequest": {"type": "object", "required": ["pageNum", "pageSize"], "properties": {"pageNum": {"type": "integer", "minimum": 1, "example": 1}, "pageSize": {"type": "integer", "minimum": 1, "example": 10}, "param": {"$ref": "#/definitions/v1.QwPageListContactWayRequestParam"}}}, "v1.QwPageListContactWayRequestParam": {"type": "object", "properties": {"addState": {"description": "联系我的添加参数", "type": "string"}, "appIDs": {"description": "应用ID列表", "type": "array", "items": {"type": "integer"}}, "id": {"description": "联系我的配置ID", "type": "integer"}, "name": {"description": "联系我的配置名称", "type": "string"}, "platformIDs": {"description": "平台ID列表", "type": "array", "items": {"type": "integer"}}}}, "v1.QwPageListContactWayResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/v1.QwPageListContactWayResponseData"}, "message": {"type": "string"}}}, "v1.QwPageListContactWayResponseData": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/v1.QwPageListContactWayResponseItem"}}, "total": {"type": "integer"}}}, "v1.QwPageListContactWayResponseItem": {"type": "object", "properties": {"addState": {"description": "联系我的添加参数", "type": "string"}, "appID": {"description": "应用ID", "type": "string"}, "appName": {"description": "应用名称", "type": "string"}, "createdTime": {"description": "创建时间", "type": "string"}, "id": {"description": "联系我的配置ID", "type": "integer"}, "link": {"description": "联系我的链接", "type": "string"}, "name": {"description": "联系我的配置名称", "type": "string"}, "platformID": {"description": "平台ID", "type": "integer"}, "platformName": {"description": "平台名称", "type": "string"}, "skipVerify": {"description": "联系我是否跳过验证", "type": "boolean"}, "updatedTime": {"description": "更新时间", "type": "string"}, "userIDs": {"description": "联系我的成员列表", "type": "string"}}}, "v1.QwUpdateContactRequest": {"type": "object", "required": ["id"], "properties": {"gender": {"description": "性别：1-男 2-女 0-未知", "type": "integer"}, "id": {"type": "integer"}, "phone": {"description": "手机号", "type": "string"}, "realName": {"description": "真实姓名", "type": "string"}}}, "v1.QwUpdateContactResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "message": {"type": "string"}}}, "v1.QwUpdateContactWayRequest": {"type": "object", "required": ["id", "name", "userIDs"], "properties": {"id": {"description": "联系我的配置ID", "type": "integer"}, "name": {"description": "联系我的配置名称", "type": "string"}, "skipVerify": {"description": "联系我是否跳过验证", "type": "boolean"}, "userIDs": {"description": "联系我的成员列表", "type": "array", "items": {"type": "string"}}}}, "v1.QwUpdateContactWayResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "message": {"type": "string"}}}, "v1.RecallAppVersionRequest": {"type": "object", "required": ["id"], "properties": {"id": {"description": "ID", "type": "integer"}}}, "v1.RecallAppVersionResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "message": {"type": "string"}}}, "v1.ReplyFeedbackRequest": {"type": "object", "properties": {"id": {"type": "integer"}, "replyContent": {"type": "string"}, "replyImageKey": {"type": "array", "items": {"type": "string"}}}}, "v1.Response": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "message": {"type": "string"}}}, "v1.SetMingliRuleConditionXijiRequest": {"type": "object", "required": ["id", "xiji"], "properties": {"id": {"description": "命理规则条件ID", "type": "integer", "example": 1}, "xiji": {"description": "喜忌", "type": "array", "items": {"$ref": "#/definitions/v1.MingliRuleConditionXiji"}}}}, "v1.SetMingliRuleConditionXijiResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "message": {"type": "string"}}}, "v1.SetMingliRuleConditionZuoduiRequest": {"type": "object", "required": ["id", "<PERSON><PERSON><PERSON>"], "properties": {"id": {"description": "命理规则条件ID", "type": "integer", "example": 1}, "zuodui": {"description": "坐对", "type": "array", "items": {"$ref": "#/definitions/v1.MingliRuleConditionZuodui"}}}}, "v1.SetMingliRuleConditionZuoduiResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "message": {"type": "string"}}}, "v1.TimeRange": {"type": "object", "required": ["endAt", "startAt"], "properties": {"endAt": {"description": "结束时间：RFC3339 / ISO8601 标准格式", "type": "string"}, "startAt": {"description": "开始时间：RFC3339 / ISO8601 标准格式", "type": "string"}}}, "v1.UpdateAppChannelRequest": {"type": "object", "required": ["id", "name", "remark"], "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "remark": {"type": "string"}}}, "v1.UpdateAppChannelResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "message": {"type": "string"}}}, "v1.UpdateAppUserPromotionRequest": {"type": "object", "required": ["id"], "properties": {"id": {"description": "ID", "type": "integer"}, "level1Ratio": {"description": "一级返利比例", "type": "integer"}, "level2Ratio": {"description": "二级返利比例", "type": "integer"}, "name": {"description": "渠道名称", "type": "string"}, "password": {"description": "渠道密码", "type": "string"}, "remark": {"description": "备注", "type": "string"}}}, "v1.UpdateAppUserPromotionResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "message": {"type": "string"}}}, "v1.UpdateAppVersionRequest": {"type": "object", "required": ["id", "updateNote", "url", "versionName"], "properties": {"id": {"description": "ID", "type": "integer"}, "isForceUpdate": {"description": "是否强制更新", "type": "boolean"}, "isHotUpdate": {"description": "是否热更新", "type": "boolean"}, "remark": {"description": "备注", "type": "string"}, "updateNote": {"description": "更新说明", "type": "string"}, "url": {"description": "下载地址", "type": "string"}, "versionName": {"description": "版本名称：1.2.0", "type": "string"}}}, "v1.UpdateAppVersionResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "message": {"type": "string"}}}, "v1.UpdateMingliRuleConditionRequest": {"type": "object", "required": ["id", "name", "no"], "properties": {"category": {"description": "类别：2-坐对、3-喜忌、1-全选", "type": "integer", "example": 1}, "criterion": {"description": "判断依据说明", "type": "string", "example": "判断依据说明"}, "gender": {"description": "日主性别（2-男、3-女、1-无关性别）", "type": "integer", "example": 1}, "id": {"description": "命理规则条件ID", "type": "integer", "example": 1}, "name": {"description": "条件名称", "type": "string", "example": "条件名称"}, "no": {"description": "条件编号", "type": "string", "example": "条件编号"}, "type": {"description": "条件类型（坐-对）", "type": "integer", "example": 1}, "weizhiDui": {"description": "位置对（1-年、2-月、3-日、4-时、5-大运、6-流年、7-流月、8-流日、9-流时）", "type": "array", "items": {"type": "integer"}, "example": [1, 2, 3]}, "weizhiZuo": {"description": "位置坐（1-年、2-月、3-日、4-时、5-大运、6-流年、7-流月、8-流日、9-流时）", "type": "array", "items": {"type": "integer"}, "example": [1, 2, 3]}}}, "v1.UpdateMingliRuleConditionResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "message": {"type": "string"}}}, "v1.UpdateMingliRuleRequest": {"type": "object", "required": ["description", "id", "module", "name", "no", "result"], "properties": {"description": {"description": "规则说明", "type": "string", "example": "规则说明"}, "id": {"description": "规则ID", "type": "integer", "example": 1}, "isEnabled": {"description": "是否启用", "type": "boolean", "example": true}, "module": {"description": "应用模块", "type": "integer", "example": 1}, "name": {"description": "规则名称", "type": "string", "example": "规则名称"}, "no": {"description": "规则编号", "type": "string", "maxLength": 32, "example": "NO1001"}, "result": {"description": "判断结果", "type": "string", "example": "判断结果"}}}, "v1.UpdateMingliRuleResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "message": {"type": "string"}}}, "v1.UpdatePortalArticleRequest": {"type": "object", "required": ["id"], "properties": {"author": {"description": "作者：传null不更新", "type": "string"}, "content": {"description": "文章内容：传null不更新", "type": "string"}, "id": {"description": "文章ID", "type": "integer"}, "isPublish": {"description": "是否发布：传null不更新", "type": "boolean"}, "isTop": {"description": "是否置顶：传null不更新", "type": "boolean"}, "remark": {"description": "备注：传null不更新", "type": "string"}, "sectionId": {"description": "栏目ID：传null不更新", "type": "integer"}, "tags": {"description": "标签：传null不更新", "type": "array", "items": {"type": "string"}}, "title": {"description": "文章标题：传null不更新", "type": "string"}}}, "v1.UpdatePortalSectionRequest": {"type": "object", "required": ["id"], "properties": {"id": {"description": "栏目ID", "type": "integer"}, "isEnable": {"description": "是否启用：传null不更新", "type": "boolean"}, "name": {"description": "栏目名称：传null不更新", "type": "string"}, "remark": {"description": "栏目备注：传null不更新", "type": "string"}}}}, "securityDefinitions": {"BearerAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}