definitions:
  model.UserMingliDayun:
    properties:
      endYear:
        type: integer
      startYear:
        type: integer
      values:
        items:
          type: string
        type: array
    type: object
  model.UserMingliXiaoyun:
    properties:
      endYear:
        type: integer
      startYear:
        type: integer
      subYear:
        type: integer
      values:
        items:
          type: string
        type: array
    type: object
  v1.CalendarDayDoWhatRequest:
    properties:
      appID:
        description: 应用：2-排盘、3-万年历、4-运势、5-论财
        type: integer
      mingliID:
        description: 命例ID
        example: 1
        type: integer
      time:
        description: 时间
        example: "2021-01-01 12:00:00"
        type: string
    type: object
  v1.CalendarDayDoWhatResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.CalendarDayDoWhatResponseData'
      message:
        type: string
    type: object
  v1.CalendarDayDoWhatResponseData:
    properties:
      dressing:
        description: 1.5 每日穿衣五行
        items:
          type: string
        type: array
      fateScore:
        description: 1.1 今日运势分数
        example: 75
        type: number
      guiren:
        description: 1.8 贵人
        example: 天乙贵人
        type: string
      hasJiaoyun:
        description: 是否已交运
        example: true
        type: boolean
      healthScore:
        description: 1.2.4 健康分数
        example: 75
        type: number
      healthWarning:
        description: 1.2.4 健康预警
        example: true
        type: boolean
      jiWhat:
        description: 每日忌what
        example:
        - 阴宅破土
        - 安葬
        - 启攒
        - 探亲访友
        items:
          type: string
        type: array
      jiaoyunTime:
        description: 1.9 交运时间
        example: "2021-01-01 12:00:00"
        type: string
      loveLocation:
        description: 1.7 桃花位
        example:
        - 东南
        items:
          type: string
        type: array
      loveScore:
        description: 1.2.3 桃花分数
        example: 75
        type: number
      luckyNum:
        description: 1.6 幸运数字
        example: 8
        type: integer
      lunarDay:
        description: 1.0 农历日期
        type: string
      pianPropertyClock:
        description: 偏财时辰
        example:
        - 1
        - 2
        - 3
        items:
          type: integer
        type: array
      pianPropertyLocation:
        description: 偏财位
        example:
        - 东南
        items:
          type: string
        type: array
      propertyLocation:
        description: 1.7 财位
        example:
        - 东南
        items:
          type: string
        type: array
      propertyScore:
        description: 1.2.2 今日财运分数
        example: 75
        type: number
      tenGodPower:
        additionalProperties:
          type: number
        description: 1.2.1 流日十神能量
        type: object
      tenTianganPower:
        additionalProperties:
          type: number
        description: 1.2.1 流日十天干能量
        type: object
      yiWhat:
        description: 1.4 每日宜what
        example:
        - 祭祀
        - 打扫
        - 破屋坏垣
        items:
          type: string
        type: array
      zhishen:
        description: 1.3 每日建议（根据值神匹配）
        type: string
    type: object
  v1.CalendarDayRequest:
    properties:
      appID:
        description: 应用：2-排盘、3-万年历、4-运势、5-论财
        example: 3
        type: integer
      date:
        description: 公历日期
        example: "2020-01-01"
        type: string
    type: object
  v1.CalendarDayResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.CalendarDayResponseData'
      message:
        type: string
    type: object
  v1.CalendarDayResponseData:
    properties:
      bazi1:
        description: 八字1（年份干支）
        example: 庚申
        type: string
      bazi2:
        description: 八字2（月份干支）
        example: 壬午
        type: string
      bazi2Next:
        description: 八字2（下个月份干支）
        example: 壬午
        type: string
      bazi3:
        description: 八字3（日期干支）
        example: 辛巳
        type: string
      caiLocation:
        description: 财位
        example: 东北
        type: string
      constellation:
        description: 星座
        example: 双鱼座
        type: string
      date:
        description: 公历日期
        example: "2099-03-12"
        type: string
      festival:
        description: 节日
        example:
        - 北方小年
        - 南方小年
        items:
          type: string
        type: array
      fuLocation:
        description: 福位
        example: 西南
        type: string
      heidao:
        description: 黑道
        example: 白虎
        type: string
      hou:
        description: 七十二侯
        example: 半夏生
        type: string
      huangdao:
        description: 黄道
        example: 青龙
        type: string
      ji:
        description: 忌
        example:
        - 阴宅破土
        - 安葬
        - 启攒
        - 探亲访友
        items:
          type: string
        type: array
      jieqi:
        description: 节气（今日或之前的最后一个节气）
        example: 大雪
        type: string
      jieqiDate:
        description: 节气日期
        example: "2006-01-02"
        type: string
      jieqiTime:
        description: 节气时间
        example: "2006-01-02 15:00:59"
        type: string
      jishen:
        description: 吉神
        example:
        - 天德合
        - 月德合
        items:
          type: string
        type: array
      luLocation:
        description: 禄位
        example: 东南
        type: string
      lunarDate:
        description: 农历日期
        example: 二月廿一
        type: string
      pengzubaiji:
        description: 彭祖百忌
        example:
        - 乙不栽植 千株不长
        - 未不服药 毒气入肠
        items:
          type: string
        type: array
      pengzubaijiOverview:
        description: 彭祖百忌概述
        example: 猴日冲虎煞南
        type: string
      shierjianri:
        description: 十二建日
        example: 定日
        type: string
      taishen:
        description: 胎神
        example: 房床厕外
        type: string
      taishenLocation:
        description: 胎神位置
        example: 西北
        type: string
      times:
        description: 时辰（共13个，包含早子时与晚子时）
        items:
          $ref: '#/definitions/v1.CalendarShichen'
        type: array
      weekday:
        description: 星期
        example: 星期四
        type: string
      wuxing:
        description: 五行
        example: 山下火
        type: string
      xiLocation:
        description: 喜位
        example: 西北
        type: string
      xingxiu:
        description: 星宿
        example: 张月鹿
        type: string
      xiongshen:
        description: 凶神
        example:
        - 月破
        - 大耗
        - 四击
        - 九空
        items:
          type: string
        type: array
      yellowYears:
        description: 黄帝纪年：公元年+2697
        example: 4721
        type: integer
      yellowYearsZh:
        description: 黄帝纪年
        example: 四千七百二十一
        type: string
      yi:
        description: 宜
        example:
        - 祭祀
        - 打扫
        - 破屋坏垣
        items:
          type: string
        type: array
      zeri:
        description: 择日
        example: 大吉
        type: string
      zodiac:
        description: 生肖
        example: 鼠
        type: string
    type: object
  v1.CalendarEachDayOfMonth:
    properties:
      bazi:
        description: 八字
        example: 庚申
        type: string
      currentMonth:
        description: 是否为当前月份
        example: true
        type: boolean
      date:
        description: 公历日期
        example: "2020-01-01"
        type: string
      holidayOff:
        description: 节假日调休：1休，2班
        example: 1
        type: integer
      ji:
        description: 忌
        example:
        - 阴宅破土
        - 安葬
        - 启攒
        - 探亲访友
        items:
          type: string
        type: array
      jieqi:
        description: 节气
        example: 大雪
        type: string
      jieqiTime:
        description: 节气时间
        type: string
      jieri:
        description: 节日
        example:
        - 北方小年
        - 南方小年
        items:
          type: string
        type: array
      liuriShensha:
        description: 流日神煞
        items:
          type: string
        type: array
      lunarDate:
        description: 农历日期
        example: 二月廿一
        type: string
      vipShishen:
        description: VIP的干十神与支十神
        items:
          type: string
        type: array
      weekday:
        description: 星期
        example: 星期四
        type: string
      yi:
        description: 宜
        example:
        - 祭祀
        - 打扫
        - 破屋坏垣
        items:
          type: string
        type: array
    type: object
  v1.CalendarMonthRequest:
    properties:
      appID:
        description: 应用：2-排盘、3-万年历、4-运势、5-论财
        type: integer
      month:
        description: 公历月份
        example: 2020-01
        type: string
    type: object
  v1.CalendarMonthResponse:
    properties:
      code:
        type: integer
      data:
        items:
          items:
            $ref: '#/definitions/v1.CalendarEachDayOfMonth'
          type: array
        type: array
      message:
        type: string
    type: object
  v1.CalendarShichen:
    properties:
      bazi:
        description: 八字
        example: 丙子
        type: string
      caiLocation:
        description: 财位
        example: 财神东北
        type: string
      chong:
        description: 冲
        example: 虎
        type: string
      fuLocation:
        description: 福位
        example: 福神西南
        type: string
      ji:
        description: 忌
        example: 忌
        type: string
      jixiong:
        description: 吉
        example: 吉
        type: string
      luLocation:
        description: 禄位
        example: 阳贵东南
        type: string
      sha:
        description: 煞
        example: 南
        type: string
      time:
        description: 时辰
        example: 23:00-00:59
        type: string
      xiLocation:
        description: 喜位
        example: 喜神西北
        type: string
      yi:
        description: 宜
        example: 宜
        type: string
    type: object
  v1.CheckAppUpdateRequest:
    properties:
      osType:
        description: 1:android, 2:ios
        type: integer
      versionName:
        description: 版本名称
        type: string
    required:
    - osType
    - versionName
    type: object
  v1.CheckAppUpdateResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.CheckAppUpdateResponseData'
      message:
        type: string
    type: object
  v1.CheckAppUpdateResponseData:
    properties:
      createdAt:
        description: 创建时间
        type: string
      isForceUpdate:
        description: 是否强制更新
        type: boolean
      isHotUpdate:
        description: 是否热更新
        type: boolean
      updateNote:
        description: 更新说明
        type: string
      updatedAt:
        description: 更新时间
        type: string
      url:
        description: 下载地址
        type: string
      versionCode:
        description: 版本号
        type: integer
      versionName:
        description: 版本名称
        type: string
    type: object
  v1.CreateUserCountdownDayRequest:
    properties:
      isTop:
        description: 是否置顶
        example: true
        type: boolean
      name:
        description: 名称
        example: 张三
        type: string
      remindTime:
        description: 提醒时间
        example: "2021-01-01 12:00:00"
        type: string
      remindType:
        description: 提醒类型：0-不提醒，1-整点提醒，2-提前5分钟，3-提前10分钟，4-提前15分钟，5-提前30分钟，6-提前1小时，7-提前1天，8-提前3天，9-提前7天
        example: 1
        type: integer
      repeatTime:
        description: 重复时间：0-不重复，1-每年，2-每月，3-每日，4-每时，5-法定节假日重复（智能跳过工作日），6-法定工作日重复（智能跳过节假日）
        example: 1
        type: integer
      type:
        description: 类型：1-纪念日，2-生日，3-日程
        example: 1
        type: integer
    type: object
  v1.CreateUserCountdownDayResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.CreateUserCountdownDayResponseData'
      message:
        type: string
    type: object
  v1.CreateUserCountdownDayResponseData:
    properties:
      id:
        type: integer
    type: object
  v1.CreateUserMingliRequest:
    properties:
      address:
        description: 地址
        example:
        - 北京市
        - 市辖区
        - 东城区
        items:
          type: string
        type: array
      appID:
        description: 应用：2-排盘、3-万年历、4-运势、5-论财
        example: 2
        type: integer
      birthtime:
        description: 出生时间（公历）
        example: "2021-01-01 12:00:00"
        type: string
      gender:
        description: 性别：1-男，2-女
        example: 1
        type: integer
      groupID:
        description: 分组ID
        example: 1
        type: integer
      isDefault:
        description: 是否默认
        example: true
        type: boolean
      name:
        description: 姓名
        example: 张三
        type: string
    required:
    - birthtime
    - gender
    - name
    type: object
  v1.CreateUserMingliResponse:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  v1.DateSubFutureRequest:
    properties:
      date:
        description: 日期
        example: "2021-01-01"
        type: string
      days:
        description: 天数
        example: 1
        type: integer
    required:
    - date
    - days
    type: object
  v1.DateSubFutureResponse:
    properties:
      code:
        type: integer
      data:
        type: string
      message:
        type: string
    type: object
  v1.DateSubPastRequest:
    properties:
      date:
        description: 日期
        example: "2021-01-01"
        type: string
      days:
        description: 天数
        example: 1
        type: integer
    required:
    - date
    - days
    type: object
  v1.DateSubPastResponse:
    properties:
      code:
        type: integer
      data:
        type: string
      message:
        type: string
    type: object
  v1.DateSubRequest:
    properties:
      date1:
        description: 日期1
        example: "2021-01-01"
        type: string
      date2:
        description: 日期2
        example: "2021-01-02"
        type: string
    required:
    - date1
    - date2
    type: object
  v1.DateSubResponse:
    properties:
      code:
        type: integer
      data:
        type: integer
      message:
        type: string
    type: object
  v1.DeleteUserCountdownDayRequest:
    properties:
      id:
        type: integer
    required:
    - id
    type: object
  v1.DeleteUserMingliRequest:
    properties:
      id:
        description: ID
        example: 1
        type: integer
    required:
    - id
    type: object
  v1.DeleteUserMingliResponse:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  v1.EnumsJiXiongRequest:
    type: object
  v1.EnumsJiXiongResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.EnumsJiXiongResponseItem'
        type: array
      message:
        type: string
    type: object
  v1.EnumsJiXiongResponseItem:
    properties:
      detail:
        description: 吉/凶神详情
        example: '...你不要给我哇哇叫'
        type: string
      id:
        description: ID
        example: 1
        type: integer
      name:
        description: 吉/凶神名称
        example: 同德合
        type: string
    type: object
  v1.EnumsJieqiRequest:
    type: object
  v1.EnumsJieqiResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.EnumsJieqiResponseDateItem'
        type: array
      message:
        type: string
    type: object
  v1.EnumsJieqiResponseDateItem:
    properties:
      health:
        example: 春有百花秋有月，夏有凉风冬有雪。若无闲事挂心头，便是人间好时节。
        type: string
      id:
        example: 1
        type: integer
      name:
        example: 立春
        type: string
      season:
        example: 春
        type: string
      url:
        example: https://mp.weixin.qq.com/s/Am_Myb-K04M5REtnkmEuuQ
        type: string
    type: object
  v1.EnumsLocationRequest:
    properties:
      overseas:
        description: 是否海外
        example: false
        type: boolean
    type: object
  v1.EnumsLocationResponse:
    properties:
      EnumsLocationResponseData:
        items:
          $ref: '#/definitions/v1.LocationTree'
        type: array
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  v1.EnumsLunarRequest:
    properties:
      year:
        description: 年份
        example: "2020"
        type: string
    required:
    - year
    type: object
  v1.EnumsLunarResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.EnumsLunarResponseItem'
        type: array
      message:
        type: string
    type: object
  v1.EnumsLunarResponseItem:
    properties:
      days:
        description: 日期
        items:
          $ref: '#/definitions/v1.LunarDay'
        type: array
      month:
        description: 月份
        example: 正月
        type: string
    type: object
  v1.EnumsNayinRequest:
    type: object
  v1.EnumsNayinResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.EnumsNayinResponseItem'
        type: array
      message:
        type: string
    type: object
  v1.EnumsNayinResponseItem:
    properties:
      hanyi:
        description: 含义
        example: '...'
        type: string
      id:
        description: ID
        example: 1
        type: integer
      name:
        description: 纳音名称
        example: 盖中王
        type: string
      wuxing:
        description: 五行
        example: 木
        type: string
      zhu1:
        description: 纳音名称
        example: 甲
        type: string
      zhu2:
        description: 纳音名称
        example: 子
        type: string
    type: object
  v1.EnumsPengzubaijiRequest:
    type: object
  v1.EnumsPengzubaijiResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.EnumsPengzubaijiResponseItem'
        type: array
      message:
        type: string
    type: object
  v1.EnumsPengzubaijiResponseItem:
    properties:
      detail:
        description: 彭祖百忌详情
        example: '...'
        type: string
      id:
        description: ID
        example: 1
        type: integer
      name:
        description: 彭祖百忌名称
        example: 小呆比 一比雕凿
        type: string
    type: object
  v1.EnumsShierjianriRequest:
    type: object
  v1.EnumsShierjianriResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.EnumsShierjianriResponseItem'
        type: array
      message:
        type: string
    type: object
  v1.EnumsShierjianriResponseItem:
    properties:
      alias:
        description: 十二建日别名
        example: 建寅
        type: string
      detail:
        description: 十二建日详情
        example: '...'
        type: string
      id:
        description: ID
        example: 1
        type: integer
      name:
        description: 十二建日名称
        example: 建寅
        type: string
    type: object
  v1.EnumsTimeRequest:
    type: object
  v1.EnumsTimeResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.EnumsTimeResponseItem'
        type: array
      message:
        type: string
    type: object
  v1.EnumsTimeResponseItem:
    properties:
      alias:
        description: 时辰别名
        example: 夜半
        type: string
      dangling:
        description: 经络
        example: 胆经
        type: string
      id:
        description: ID
        example: 1
        type: integer
      ji:
        description: 忌
        example: 忌
        type: string
      jingmai:
        description: 经脉
        example: 足少阳
        type: string
      name:
        description: 时辰名称
        example: 子时
        type: string
      time:
        description: 时辰时间
        example: 23:00-0:59
        type: string
      yi:
        description: 宜
        example: 宜
        type: string
    type: object
  v1.EnumsWuxingRequest:
    type: object
  v1.EnumsWuxingResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.EnumsWuxingResponseItem'
        type: array
      message:
        type: string
    type: object
  v1.EnumsWuxingResponseItem:
    properties:
      id:
        description: ID
        example: 1
        type: integer
      name:
        description: 五行名称
        example: 金
        type: string
      position:
        description: 方位
        type: string
      profession:
        description: 职业
        type: string
      season:
        description: 季节
        type: string
      wuxing:
        description: 五行
        type: string
    type: object
  v1.EnumsYijiRequest:
    type: object
  v1.EnumsYijiResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.EnumsYijiResponseItem'
        type: array
      message:
        type: string
    type: object
  v1.EnumsYijiResponseItem:
    properties:
      detail:
        description: 宜忌详情
        example: 祭祀神佛，祈福祈福
        type: string
      id:
        description: ID
        example: 1
        type: integer
      name:
        description: 宜忌名称
        example: 祭祀
        type: string
    type: object
  v1.EnumsZhishenRequest:
    type: object
  v1.EnumsZhishenResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.EnumsZhishenResponseItem'
        type: array
      message:
        type: string
    type: object
  v1.EnumsZhishenResponseItem:
    properties:
      daily:
        description: 日值神
        example: '...'
        type: string
      detail:
        description: 值神详情
        example: '...'
        type: string
      id:
        description: ID
        example: 1
        type: integer
      name:
        description: 值神名称
        example: 青龙
        type: string
      symbol:
        description: 值神符号
        example: 青龙
        type: string
      type:
        description: 值神类型：黄道/黑道
        example: 黄道
        type: string
    type: object
  v1.GetDatetimeBySizhuRequest:
    properties:
      endYear:
        description: 默认2099
        example: 2099
        type: integer
      sizhu:
        description: 四柱
        example:
        - 癸亥
        - 戊午
        - 壬寅
        - 己酉
        items:
          type: string
        type: array
      startYear:
        description: 默认1801
        example: 1801
        type: integer
    type: object
  v1.GetDatetimeBySizhuResponse:
    properties:
      code:
        type: integer
      data:
        items:
          type: string
        type: array
      message:
        type: string
    type: object
  v1.GetHistoryDayEventsRequest:
    properties:
      date:
        description: 日期
        example: "2006-01-02"
        type: string
    required:
    - date
    type: object
  v1.GetHistoryDayEventsResponse:
    properties:
      code:
        type: integer
      data:
        allOf:
        - $ref: '#/definitions/v1.GetHistoryDayEventsResponseData'
        description: 数据
      message:
        type: string
    type: object
  v1.GetHistoryDayEventsResponseData:
    properties:
      actions:
        description: 活动
        items:
          $ref: '#/definitions/v1.GetHistoryDayEventsResponseItem'
        type: array
      events:
        description: 事件
        items:
          $ref: '#/definitions/v1.GetHistoryDayEventsResponseItem'
        type: array
    type: object
  v1.GetHistoryDayEventsResponseItem:
    properties:
      ad:
        description: 公元后
        example: true
        type: boolean
      content:
        description: 内容
        example: '****************'
        type: string
      date:
        description: 日期
        example: "2006-01-02"
        type: string
      keyword:
        description: 关键词
        example: 大学生爱国运D
        type: string
      title:
        description: 标题
        example: 大学生爱国运D
        type: string
      type:
        description: 类型
        example: 大事记
        type: string
    type: object
  v1.GetJiaoyunRequest:
    properties:
      birthplace:
        description: 出生地
        items:
          type: string
        type: array
      birthtime:
        description: 生日
        example: "2006-01-02 15:04:05"
        type: string
      gender:
        enum:
        - 男
        - 女
        example: 男
        type: string
    required:
    - birthtime
    - gender
    type: object
  v1.GetJiaoyunResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.GetJiaoyunResponseData'
      message:
        type: string
    type: object
  v1.GetJiaoyunResponseData:
    properties:
      jiaoyunTime:
        description: 交运时间
        example: "2021-01-01 12:00:00"
        type: string
    type: object
  v1.JiemengRequest:
    properties:
      pageNum:
        example: 1
        minimum: 1
        type: integer
      pageSize:
        example: 10
        minimum: 1
        type: integer
      param:
        $ref: '#/definitions/v1.JiemengRequestParam'
    required:
    - pageNum
    - pageSize
    type: object
  v1.JiemengRequestParam:
    properties:
      content:
        description: 梦境内容（非必须）
        example: 梦见大海（非必须）
        type: string
    type: object
  v1.JiemengResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.JiemengResponseData'
      message:
        type: string
    type: object
  v1.JiemengResponseData:
    properties:
      list:
        items:
          $ref: '#/definitions/v1.JiemengResponseDataItem'
        type: array
      total:
        type: integer
    type: object
  v1.JiemengResponseDataItem:
    properties:
      content:
        description: 内容
        example: 梦见大海（内容）
        type: string
      title:
        description: 标题
        example: 梦见大海（标题）
        type: string
    type: object
  v1.ListUserCountdownDayRequest:
    type: object
  v1.ListUserCountdownDayResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.UserCountdownDay'
        type: array
      message:
        type: string
    type: object
  v1.ListUserMingliRequest:
    properties:
      appID:
        type: integer
      groupID:
        type: integer
    type: object
  v1.ListUserMingliResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.UserMingli'
        type: array
      message:
        type: string
    type: object
  v1.LocationTree:
    properties:
      children:
        description: 子节点，省包含市，市包含区
        items:
          $ref: '#/definitions/v1.LocationTree'
        type: array
      code:
        type: integer
      name:
        type: string
    type: object
  v1.LunarDay:
    properties:
      date:
        description: 日期
        example: "2020-01-01"
        type: string
      lunarDate:
        description: 农历日期
        example: 正月初一
        type: string
      name:
        description: 名称
        example: 初一
        type: string
    type: object
  v1.MingliDayunliulianScoreRequest:
    properties:
      mingliID:
        description: 命例ID
        example: 1
        type: integer
    type: object
  v1.MingliDayunliulianScoreResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.MingliDayunliulianScoreResponseData'
      message:
        type: string
    type: object
  v1.MingliDayunliulianScoreResponseData:
    properties:
      Dyun:
        description: 大运分（移除末尾0值，每两个一组，共二十四组）
        items:
          type: integer
        type: array
      Lnian:
        description: 流年分（每五个一组，共二十四组，每两组与大运分的一组对应）
        items:
          type: integer
        type: array
      Xxian:
        description: 小运分（每一个小运年对应一个分数）
        items:
          type: integer
        type: array
      Zscore:
        description: 综合分（移除末尾0值）
        items:
          type: integer
        type: array
    type: object
  v1.MingliJieqiScoreRequest:
    properties:
      mingliID:
        description: 命例ID
        example: 1
        type: integer
      time:
        description: 时间
        example: "2021-01-01 12:00:00"
        type: string
    type: object
  v1.MingliJieqiScoreResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.MingliJieqiScoreResponseData'
      message:
        type: string
    type: object
  v1.MingliJieqiScoreResponseData:
    properties:
      DayGanzhiList:
        description: 日干支列表
        items:
          type: string
        type: array
      dayFen:
        description: 日分
        items:
          type: integer
        type: array
      dayNum:
        description: 日数（两个节气交替日+中间的天数）
        type: integer
      jqBegin:
        description: 节气开始时间（节气交替时的半天记作一天）
        type: string
      jqEnd:
        description: 节气结束时间（节气交替时的半天记作一天）
        type: string
      monthGanzhiList:
        description: 月干支列表
        items:
          type: string
        type: array
      nowMonthFen:
        description: 当前月分
        items:
          type: integer
        type: array
      paiYueJq:
        description: 排月节气
        items:
          type: string
        type: array
    type: object
  v1.PaipanRequest:
    properties:
      mingliID:
        description: 命例ID
        example: 1
        type: integer
      time:
        description: 时间
        example: "2021-01-01 12:00:00"
        type: string
    type: object
  v1.PaipanResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.PaipanResponseData'
      message:
        type: string
    type: object
  v1.PaipanResponseData:
    properties:
      benqi:
        description: 本气
        items:
          type: string
        type: array
      benqiShishen:
        description: 本气十神
        items:
          type: string
        type: array
      dizhi:
        description: 地支
        items:
          type: string
        type: array
      kongwang:
        description: 空亡
        items:
          type: string
        type: array
      nayin:
        description: 纳音
        items:
          type: string
        type: array
      shenShaJiShen:
        description: 神煞
        items:
          items:
            type: string
          type: array
        type: array
      tiangan:
        description: 天干
        items:
          type: string
        type: array
      xingyun:
        description: 星运
        items:
          type: string
        type: array
      yuqi:
        description: 余气
        items:
          type: string
        type: array
      yuqiShishen:
        description: 余气十神
        items:
          type: string
        type: array
      zhongqi:
        description: 中气
        items:
          type: string
        type: array
      zhongqiShishen:
        description: 中气十神
        items:
          type: string
        type: array
      zhuxing:
        description: 主星
        items:
          type: string
        type: array
      zizuo:
        description: 自坐
        items:
          type: string
        type: array
    type: object
  v1.SetDefaultUserMingliRequest:
    properties:
      id:
        description: ID
        example: 1
        type: integer
    required:
    - id
    type: object
  v1.SetDefaultUserMingliResponse:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  v1.SetMingliWuxingRequest:
    properties:
      id:
        description: ID
        example: 1
        type: integer
      wuxing:
        description: 五行：用神,喜神,忌神,仇神,闲神
        items:
          type: string
        type: array
    required:
    - id
    type: object
  v1.SetMingliWuxingResponse:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  v1.UpdateUserCountdownDayRequest:
    properties:
      id:
        description: ID
        example: 1
        type: integer
      isTop:
        description: 是否置顶
        example: true
        type: boolean
      name:
        description: 名称
        example: 张三
        type: string
      remindTime:
        description: 提醒时间
        example: "2021-01-01 12:00:00"
        type: string
      remindType:
        description: 提醒类型：0-不提醒，1-整点提醒，2-提前5分钟，3-提前10分钟，4-提前15分钟，5-提前30分钟，6-提前1小时，7-提前1天，8-提前3天，9-提前7天
        example: 1
        type: integer
      repeatTime:
        description: 重复时间：0-不重复，1-每年，2-每月，3-每日，4-每时，5-法定节假日重复（智能跳过工作日），6-法定工作日重复（智能跳过节假日）
        example: 1
        type: integer
      type:
        description: 类型：1-纪念日，2-生日，3-日程
        example: 1
        type: integer
    type: object
  v1.UpdateUserMingliRequest:
    properties:
      address:
        description: 地址
        example:
        - '["北京市"'
        - '"市辖区"'
        - '"东城区"]'
        items:
          type: string
        type: array
      birthtime:
        description: 出生时间（公历）
        example: "2021-01-01 12:00:00"
        type: string
      gender:
        description: 性别：1-男，2-女
        example: 1
        type: integer
      id:
        description: ID
        example: 1
        type: integer
      isDefault:
        description: 是否默认
        example: true
        type: boolean
      name:
        description: 姓名
        example: 张三
        type: string
    required:
    - birthtime
    - gender
    - id
    - name
    type: object
  v1.UpdateUserMingliResponse:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  v1.UserCountdownDay:
    properties:
      id:
        description: ID
        example: 1
        type: integer
      isTop:
        description: 是否置顶
        example: true
        type: boolean
      name:
        description: 名称
        example: 张三
        type: string
      remindTime:
        description: 提醒时间
        example: "2021-01-01 12:00:00"
        type: string
      remindType:
        description: 提醒类型：0-不提醒，1-整点提醒，2-提前5分钟，3-提前10分钟，4-提前15分钟，5-提前30分钟，6-提前1小时，7-提前1天，8-提前3天，9-提前7天
        example: 1
        type: integer
      repeatTime:
        description: 重复时间：0-不重复，1-每年，2-每月，3-每日，4-每时，5-法定节假日重复（智能跳过工作日），6-法定工作日重复（智能跳过节假日）
        example: 1
        type: integer
      type:
        description: 类型：1-纪念日，2-生日，3-日程
        example: 1
        type: integer
    type: object
  v1.UserMingli:
    properties:
      address:
        description: 地址
        example:
        - 北京市
        - 市辖区
        - 东城区
        items:
          type: string
        type: array
      appID:
        description: 应用：2-排盘、3-万年历、4-运势、5-论财
        example: 2
        type: integer
      appName:
        description: 应用名称
        type: string
      bazi:
        description: 八字：年份干支,月份干支,日期干支,时辰干支
        items:
          type: string
        type: array
      birthplace:
        description: 出生地
        example:
        - '["北京市"'
        - '"市辖区"'
        - '"东城区"]'
        items:
          type: string
        type: array
      birthtime:
        description: 出生时间（公历）
        example: "2021-01-01 12:00:00"
        type: string
      birthtimeLunar:
        description: 出生时间（农历）
        example: 2021年闰四月十一子时
        type: string
      birthtimeSun:
        description: 真太阳时
        example: "2021-01-01 12:00:00"
        type: string
      dayun:
        allOf:
        - $ref: '#/definitions/model.UserMingliDayun'
        description: 大运
      gender:
        description: 性别：1-男，2-女
        example: 1
        type: integer
      groupID:
        description: 分组ID
        example: 1
        type: integer
      groupName:
        description: 分组名称
        type: string
      id:
        description: ID
        example: 1
        type: integer
      isDefault:
        description: 是否默认
        example: true
        type: boolean
      lunarBirthtime:
        description: 出生时间（农历）
        example: 2021年闰四月十一子时
        type: string
      name:
        description: 姓名
        example: 张三
        type: string
      userID:
        description: 用户ID
        example: "1"
        type: string
      wuxing:
        description: 五行：用神,喜神,忌神,仇神,闲神
        items:
          type: string
        type: array
      xiaoyun:
        allOf:
        - $ref: '#/definitions/model.UserMingliXiaoyun'
        description: 小运
    type: object
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: This is a http server template.
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: 万年历
  version: 1.0.0
paths:
  /app/version/newest:
    post:
      consumes:
      - application/json
      description: 最新版本
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.CheckAppUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 最新版本
          schema:
            $ref: '#/definitions/v1.CheckAppUpdateResponse'
      summary: 最新版本
      tags:
      - 应用版本
  /calendar/day:
    post:
      consumes:
      - application/json
      description: 获取本日日历
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.CalendarDayRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.CalendarDayResponse'
      summary: 获取本日日历
      tags:
      - 日历
  /calendar/day/dowhat:
    post:
      consumes:
      - application/json
      description: 今天干什么
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.CalendarDayDoWhatRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.CalendarDayDoWhatResponse'
      security:
      - BearerAuth: []
      summary: 今天干什么
      tags:
      - 日历
  /calendar/day/vip:
    post:
      consumes:
      - application/json
      description: 获取本日日历
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.CalendarDayRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.CalendarDayResponse'
      security:
      - BearerAuth: []
      summary: 获取本日日历
      tags:
      - 日历
  /calendar/jiaoyun:
    post:
      consumes:
      - application/json
      description: 交运
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.GetJiaoyunRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.GetJiaoyunResponse'
      summary: 交运
      tags:
      - 日历
  /calendar/month:
    post:
      consumes:
      - application/json
      description: 获取本月日历
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.CalendarMonthRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.CalendarMonthResponse'
      summary: 获取本月日历
      tags:
      - 日历
  /calendar/month/vip:
    post:
      consumes:
      - application/json
      description: 获取本月日历
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.CalendarMonthRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.CalendarMonthResponse'
      security:
      - BearerAuth: []
      summary: 获取本月日历
      tags:
      - 日历
  /datesub:
    post:
      consumes:
      - application/json
      description: 日期换算
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.DateSubRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.DateSubResponse'
      summary: 日期换算
      tags:
      - 日期换算
  /datesub/future:
    post:
      consumes:
      - application/json
      description: 未来日期
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.DateSubFutureRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.DateSubFutureResponse'
      summary: 未来日期
      tags:
      - 日期换算
  /datesub/past:
    post:
      consumes:
      - application/json
      description: 过去日期
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.DateSubPastRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.DateSubPastResponse'
      summary: 过去日期
      tags:
      - 日期换算
  /datetime/fromSizhu:
    post:
      consumes:
      - application/json
      description: 从四柱获取时间
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.GetDatetimeBySizhuRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.GetDatetimeBySizhuResponse'
      summary: 从四柱获取时间
      tags:
      - 时间
  /enums/jieqi:
    post:
      consumes:
      - application/json
      description: 获取节气列表
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.EnumsJieqiRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsJieqiResponse'
      summary: 获取节气列表
      tags:
      - 枚举
  /enums/jixiong:
    post:
      consumes:
      - application/json
      description: 获取吉/凶神列表
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.EnumsJiXiongRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsJiXiongResponse'
      summary: 获取吉/凶神列表
      tags:
      - 枚举
  /enums/location:
    post:
      consumes:
      - application/json
      description: 获取地区列表
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.EnumsLocationRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsLocationResponse'
      summary: 获取地区列表
      tags:
      - 枚举
  /enums/lunar:
    post:
      consumes:
      - application/json
      description: 获取农历列表
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.EnumsLunarRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsLunarResponse'
      summary: 获取农历列表
      tags:
      - 枚举
  /enums/nayin:
    post:
      consumes:
      - application/json
      description: 获取纳音列表
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.EnumsNayinRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsNayinResponse'
      summary: 获取纳音列表
      tags:
      - 枚举
  /enums/pengzubaiji:
    post:
      consumes:
      - application/json
      description: 获取彭祖百忌列表
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.EnumsPengzubaijiRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsPengzubaijiResponse'
      summary: 获取彭祖百忌列表
      tags:
      - 枚举
  /enums/shierjianri:
    post:
      consumes:
      - application/json
      description: 获取十二建日列表
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.EnumsShierjianriRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsShierjianriResponse'
      summary: 获取十二建日列表
      tags:
      - 枚举
  /enums/time:
    post:
      consumes:
      - application/json
      description: 获取时辰列表
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.EnumsTimeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsTimeResponse'
      summary: 获取时辰列表
      tags:
      - 枚举
  /enums/wuxing:
    post:
      consumes:
      - application/json
      description: 获取五行列表
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.EnumsWuxingRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsWuxingResponse'
      summary: 获取五行列表
      tags:
      - 枚举
  /enums/yiji:
    post:
      consumes:
      - application/json
      description: 获取宜忌列表
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.EnumsYijiRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsYijiResponse'
      summary: 获取宜忌列表
      tags:
      - 枚举
  /enums/zhishen:
    post:
      consumes:
      - application/json
      description: 获取值神列表
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.EnumsZhishenRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsZhishenResponse'
      summary: 获取值神列表
      tags:
      - 枚举
  /historyDayEvents:
    post:
      consumes:
      - application/json
      description: 获取历史上的今天
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.GetHistoryDayEventsRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.GetHistoryDayEventsResponse'
      summary: 获取历史上的今天
      tags:
      - 历史上的今天
  /jiemeng:
    post:
      consumes:
      - application/json
      description: 获取解梦
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.JiemengRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.JiemengResponse'
      summary: 获取解梦
      tags:
      - 解梦
  /userCountdownDay/create:
    post:
      consumes:
      - application/json
      description: 创建倒数日
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.CreateUserCountdownDayRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.CreateUserCountdownDayResponse'
      security:
      - BearerAuth: []
      summary: 创建倒数日
      tags:
      - 倒数日
  /userCountdownDay/delete:
    post:
      consumes:
      - application/json
      description: 删除倒数日
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.DeleteUserCountdownDayRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BearerAuth: []
      summary: 删除倒数日
      tags:
      - 倒数日
  /userCountdownDay/list:
    post:
      consumes:
      - application/json
      description: 获取倒数日列表
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.ListUserCountdownDayRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.ListUserCountdownDayResponse'
      security:
      - BearerAuth: []
      summary: 获取倒数日列表
      tags:
      - 倒数日
  /userCountdownDay/update:
    post:
      consumes:
      - application/json
      description: 更新倒数日
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.UpdateUserCountdownDayRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BearerAuth: []
      summary: 更新倒数日
      tags:
      - 倒数日
  /userMingli/create:
    post:
      consumes:
      - application/json
      description: 创建命例
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.CreateUserMingliRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.CreateUserMingliResponse'
      security:
      - BearerAuth: []
      summary: 创建命例
      tags:
      - 命例
  /userMingli/delete:
    post:
      consumes:
      - application/json
      description: 删除命例
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.DeleteUserMingliRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.DeleteUserMingliResponse'
      security:
      - BearerAuth: []
      summary: 删除命例
      tags:
      - 命例
  /userMingli/jieqiScore:
    post:
      consumes:
      - application/json
      description: 获取节气得分
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.MingliJieqiScoreRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.MingliJieqiScoreResponse'
      security:
      - BearerAuth: []
      summary: 获取节气得分
      tags:
      - 命例
  /userMingli/list:
    post:
      consumes:
      - application/json
      description: 获取命例列表
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.ListUserMingliRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.ListUserMingliResponse'
      security:
      - BearerAuth: []
      summary: 获取命例列表
      tags:
      - 命例
  /userMingli/paipan:
    post:
      consumes:
      - application/json
      description: 八字排盘
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.PaipanRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.PaipanResponse'
      security:
      - BearerAuth: []
      summary: 八字排盘
      tags:
      - 命例
  /userMingli/setDefault:
    post:
      consumes:
      - application/json
      description: 设置默认命例
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.SetDefaultUserMingliRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.SetDefaultUserMingliResponse'
      security:
      - BearerAuth: []
      summary: 设置默认命例
      tags:
      - 命例
  /userMingli/setWuxing:
    post:
      consumes:
      - application/json
      description: 设置命例五行
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.SetMingliWuxingRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.SetMingliWuxingResponse'
      security:
      - BearerAuth: []
      summary: 设置命例五行
      tags:
      - 命例
  /userMingli/update:
    post:
      consumes:
      - application/json
      description: 更新命例
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.UpdateUserMingliRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.UpdateUserMingliResponse'
      security:
      - BearerAuth: []
      summary: 更新命例
      tags:
      - 命例
  /userMingli/yunScore:
    post:
      consumes:
      - application/json
      description: 获取大运流年得分
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.MingliDayunliulianScoreRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.MingliDayunliulianScoreResponse'
      security:
      - BearerAuth: []
      summary: 获取大运流年得分
      tags:
      - 命例
securityDefinitions:
  BearerAuth:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
