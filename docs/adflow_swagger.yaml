definitions:
  corona.PaipanBaziLiutongGraph:
    properties:
      edges:
        items:
          properties:
            from:
              type: string
            isReverse:
              type: boolean
            isSingle:
              type: boolean
            relation:
              type: string
            to:
              type: string
          type: object
        type: array
      nodes:
        items:
          type: string
        type: array
      weight:
        additionalProperties: {}
        type: object
    type: object
  corona.PaipanShishenPowerItem:
    properties:
      attr:
        description: 五行
        type: string
      cangNum:
        description: 藏干数量
        type: integer
      liliang:
        description: 力量
        type: integer
      num:
        description: 五行数量
        type: integer
      power:
        description: 能量占比数组
        items:
          type: number
        type: array
      powerArr:
        description: 能量数组
        items:
          type: integer
        type: array
      shiShen:
        description: 十神数组
        items:
          type: string
        type: array
      shiShenName:
        description: 十神合称
        type: string
      tianganArr:
        description: 天干数组
        items:
          type: string
        type: array
      totalBfb:
        description: 总能量占比
        type: number
      totalPower:
        description: 总能量
        type: integer
    type: object
  model.Guaxiang:
    properties:
      duanri:
        description: 断日
        type: string
      gejue:
        description: 歌诀
        type: string
      guaji:
        description: 卦吉
        type: string
      id:
        description: 主键ID
        type: integer
      idiom:
        description: 成语
        type: string
      jieshi:
        description: 解释
        type: string
      name:
        description: 卦名
        type: string
      topdown:
        description: 上下卦
        type: string
      value:
        description: 卦值
        type: integer
      yao1:
        description: 爻1
        type: integer
      yao2:
        description: 爻2
        type: integer
      yao3:
        description: 爻3
        type: integer
      yao4:
        description: 爻4
        type: integer
      yao5:
        description: 爻5
        type: integer
      yao6:
        description: 爻6
        type: integer
      yaoValue:
        description: 爻值
        type: integer
    type: object
  v1.CalendarDayRequest:
    properties:
      appID:
        description: 应用：2-排盘、3-万年历、4-运势、5-论财
        example: 3
        type: integer
      date:
        description: 公历日期
        example: "2020-01-01"
        type: string
    type: object
  v1.CalendarDayResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.CalendarDayResponseData'
      message:
        type: string
    type: object
  v1.CalendarDayResponseData:
    properties:
      bazi1:
        description: 八字1（年份干支）
        example: 庚申
        type: string
      bazi2:
        description: 八字2（月份干支）
        example: 壬午
        type: string
      bazi2Next:
        description: 八字2（下个月份干支）
        example: 壬午
        type: string
      bazi3:
        description: 八字3（日期干支）
        example: 辛巳
        type: string
      caiLocation:
        description: 财位
        example: 东北
        type: string
      constellation:
        description: 星座
        example: 双鱼座
        type: string
      date:
        description: 公历日期
        example: "2099-03-12"
        type: string
      festival:
        description: 节日
        example:
        - 北方小年
        - 南方小年
        items:
          type: string
        type: array
      fuLocation:
        description: 福位
        example: 西南
        type: string
      heidao:
        description: 黑道
        example: 白虎
        type: string
      hou:
        description: 七十二侯
        example: 半夏生
        type: string
      huangdao:
        description: 黄道
        example: 青龙
        type: string
      ji:
        description: 忌
        example:
        - 阴宅破土
        - 安葬
        - 启攒
        - 探亲访友
        items:
          type: string
        type: array
      jieqi:
        description: 节气（今日或之前的最后一个节气）
        example: 大雪
        type: string
      jieqiDate:
        description: 节气日期
        example: "2006-01-02"
        type: string
      jieqiTime:
        description: 节气时间
        example: "2006-01-02 15:00:59"
        type: string
      jishen:
        description: 吉神
        example:
        - 天德合
        - 月德合
        items:
          type: string
        type: array
      luLocation:
        description: 禄位
        example: 东南
        type: string
      lunarDate:
        description: 农历日期
        example: 二月廿一
        type: string
      pengzubaiji:
        description: 彭祖百忌
        example:
        - 乙不栽植 千株不长
        - 未不服药 毒气入肠
        items:
          type: string
        type: array
      pengzubaijiOverview:
        description: 彭祖百忌概述
        example: 猴日冲虎煞南
        type: string
      shierjianri:
        description: 十二建日
        example: 定日
        type: string
      taishen:
        description: 胎神
        example: 房床厕外
        type: string
      taishenLocation:
        description: 胎神位置
        example: 西北
        type: string
      times:
        description: 时辰（共13个，包含早子时与晚子时）
        items:
          $ref: '#/definitions/v1.CalendarShichen'
        type: array
      weekday:
        description: 星期
        example: 星期四
        type: string
      wuxing:
        description: 五行
        example: 山下火
        type: string
      xiLocation:
        description: 喜位
        example: 西北
        type: string
      xingxiu:
        description: 星宿
        example: 张月鹿
        type: string
      xiongshen:
        description: 凶神
        example:
        - 月破
        - 大耗
        - 四击
        - 九空
        items:
          type: string
        type: array
      yellowYears:
        description: 黄帝纪年：公元年+2697
        example: 4721
        type: integer
      yellowYearsZh:
        description: 黄帝纪年
        example: 四千七百二十一
        type: string
      yi:
        description: 宜
        example:
        - 祭祀
        - 打扫
        - 破屋坏垣
        items:
          type: string
        type: array
      zeri:
        description: 择日
        example: 大吉
        type: string
      zodiac:
        description: 生肖
        example: 鼠
        type: string
    type: object
  v1.CalendarEachDayOfMonth:
    properties:
      bazi:
        description: 八字
        example: 庚申
        type: string
      currentMonth:
        description: 是否为当前月份
        example: true
        type: boolean
      date:
        description: 公历日期
        example: "2020-01-01"
        type: string
      holidayOff:
        description: 节假日调休：1休，2班
        example: 1
        type: integer
      ji:
        description: 忌
        example:
        - 阴宅破土
        - 安葬
        - 启攒
        - 探亲访友
        items:
          type: string
        type: array
      jieqi:
        description: 节气
        example: 大雪
        type: string
      jieqiTime:
        description: 节气时间
        type: string
      jieri:
        description: 节日
        example:
        - 北方小年
        - 南方小年
        items:
          type: string
        type: array
      liuriShensha:
        description: 流日神煞
        items:
          type: string
        type: array
      lunarDate:
        description: 农历日期
        example: 二月廿一
        type: string
      vipShishen:
        description: VIP的干十神与支十神
        items:
          type: string
        type: array
      weekday:
        description: 星期
        example: 星期四
        type: string
      yi:
        description: 宜
        example:
        - 祭祀
        - 打扫
        - 破屋坏垣
        items:
          type: string
        type: array
    type: object
  v1.CalendarMonthRequest:
    properties:
      appID:
        description: 应用：2-排盘、3-万年历、4-运势、5-论财
        type: integer
      month:
        description: 公历月份
        example: 2020-01
        type: string
    type: object
  v1.CalendarMonthResponse:
    properties:
      code:
        type: integer
      data:
        items:
          items:
            $ref: '#/definitions/v1.CalendarEachDayOfMonth'
          type: array
        type: array
      message:
        type: string
    type: object
  v1.CalendarShichen:
    properties:
      bazi:
        description: 八字
        example: 丙子
        type: string
      caiLocation:
        description: 财位
        example: 财神东北
        type: string
      chong:
        description: 冲
        example: 虎
        type: string
      fuLocation:
        description: 福位
        example: 福神西南
        type: string
      ji:
        description: 忌
        example: 忌
        type: string
      jixiong:
        description: 吉
        example: 吉
        type: string
      luLocation:
        description: 禄位
        example: 阳贵东南
        type: string
      sha:
        description: 煞
        example: 南
        type: string
      time:
        description: 时辰
        example: 23:00-00:59
        type: string
      xiLocation:
        description: 喜位
        example: 喜神西北
        type: string
      yi:
        description: 宜
        example: 宜
        type: string
    type: object
  v1.CheckAppUpdateRequest:
    properties:
      osType:
        description: 1:android, 2:ios
        type: integer
      versionName:
        description: 版本名称
        type: string
    required:
    - osType
    - versionName
    type: object
  v1.CheckAppUpdateResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.CheckAppUpdateResponseData'
      message:
        type: string
    type: object
  v1.CheckAppUpdateResponseData:
    properties:
      createdAt:
        description: 创建时间
        type: string
      isForceUpdate:
        description: 是否强制更新
        type: boolean
      isHotUpdate:
        description: 是否热更新
        type: boolean
      updateNote:
        description: 更新说明
        type: string
      updatedAt:
        description: 更新时间
        type: string
      url:
        description: 下载地址
        type: string
      versionCode:
        description: 版本号
        type: integer
      versionName:
        description: 版本名称
        type: string
    type: object
  v1.DeletePaipanRecordRequest:
    properties:
      ids:
        description: ID
        items:
          type: integer
        type: array
    type: object
  v1.DeletePaipanRecordResponse:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  v1.EnumsDizhiResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.EnumsDizhiResponseItem'
        type: array
      message:
        type: string
    type: object
  v1.EnumsDizhiResponseItem:
    properties:
      dizhi:
        description: 地支
        type: string
      id:
        example: 1
        type: integer
      jieqi:
        description: 节气
        type: string
      name:
        example: 子
        type: string
      shichen:
        description: 时辰
        type: string
      shuxiang:
        description: 属相
        type: string
      wuxing:
        description: 五行
        type: string
      yinyang:
        description: 阴阳
        type: string
      yuefen:
        description: 月份
        type: string
      zhongqi:
        description: 中气
        type: string
    type: object
  v1.EnumsLocationRequest:
    properties:
      overseas:
        description: 是否海外
        example: false
        type: boolean
    type: object
  v1.EnumsLocationResponse:
    properties:
      EnumsLocationResponseData:
        items:
          $ref: '#/definitions/v1.LocationTree'
        type: array
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  v1.EnumsLunarRequest:
    properties:
      year:
        description: 年份
        example: "2020"
        type: string
    required:
    - year
    type: object
  v1.EnumsLunarResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.EnumsLunarResponseItem'
        type: array
      message:
        type: string
    type: object
  v1.EnumsLunarResponseItem:
    properties:
      days:
        description: 日期
        items:
          $ref: '#/definitions/v1.LunarDay'
        type: array
      month:
        description: 月份
        example: 正月
        type: string
    type: object
  v1.EnumsShishenPropertyItem:
    properties:
      ability:
        description: 能力
        type: string
      bias:
        description: 偏向
        type: string
      category:
        description: 十神类型
        type: string
      insufficient:
        description: 不足
        type: string
      overview:
        description: 概述
        type: string
      preferredRegion:
        description: 适合地区
        type: string
      profession:
        description: 职业
        type: string
      superiority:
        description: 擅长
        type: string
      type:
        description: 类型
        type: string
      wealthBy:
        description: 财富来源
        type: string
    type: object
  v1.EnumsShishenPropertyResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.EnumsShishenPropertyItem'
        type: array
      message:
        type: string
    type: object
  v1.EnumsTianganResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.EnumsTianganResponseItem'
        type: array
      message:
        type: string
    type: object
  v1.EnumsTianganResponseItem:
    properties:
      id:
        example: 1
        type: integer
      name:
        example: 甲
        type: string
      tiangan:
        description: 天干
        type: string
      wuxing:
        description: 五行
        type: string
      yinyang:
        description: 阴阳
        type: string
    type: object
  v1.EnumsWuxingResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/v1.EnumsWuxingResponseItem'
        type: array
      message:
        type: string
    type: object
  v1.EnumsWuxingResponseItem:
    properties:
      id:
        description: ID
        example: 1
        type: integer
      name:
        description: 五行名称
        example: 金
        type: string
      position:
        description: 方位
        type: string
      profession:
        description: 职业
        type: string
      season:
        description: 季节
        type: string
      wuxing:
        description: 五行
        type: string
    type: object
  v1.GetBaziRequest:
    properties:
      birthtime:
        description: 出生时间：2006-01-02 15:03:04
        example: "2006-01-02 15:03:04"
        type: string
      gender:
        description: 性别：1-男、2-女
        example: 1
        type: integer
    required:
    - birthtime
    - gender
    type: object
  v1.GetBaziResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.GetBaziResponseData'
      message:
        type: string
    type: object
  v1.GetBaziResponseData:
    properties:
      bazi:
        items:
          type: string
        type: array
    type: object
  v1.GetDatetimeBySizhuRequest:
    properties:
      endYear:
        description: 默认2099
        example: 2099
        type: integer
      sizhu:
        description: 四柱
        example:
        - 癸亥
        - 戊午
        - 壬寅
        - 己酉
        items:
          type: string
        type: array
      startYear:
        description: 默认1801
        example: 1801
        type: integer
    type: object
  v1.GetDatetimeBySizhuResponse:
    properties:
      code:
        type: integer
      data:
        items:
          type: string
        type: array
      message:
        type: string
    type: object
  v1.LocationRequest:
    properties:
      overseas:
        description: 是否海外
        example: false
        type: boolean
    type: object
  v1.LocationResponse:
    properties:
      EnumsLocationResponseData:
        items:
          $ref: '#/definitions/v1.LocationTree'
        type: array
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  v1.LocationTree:
    properties:
      children:
        description: 子节点，省包含市，市包含区
        items:
          $ref: '#/definitions/v1.LocationTree'
        type: array
      code:
        type: integer
      name:
        type: string
    type: object
  v1.LunarDay:
    properties:
      date:
        description: 日期
        example: "2020-01-01"
        type: string
      lunarDate:
        description: 农历日期
        example: 正月初一
        type: string
      name:
        description: 名称
        example: 初一
        type: string
    type: object
  v1.LuncaiBelong:
    properties:
      detail:
        allOf:
        - $ref: '#/definitions/v1.LuncaiBelongDetail'
        description: 详情
      table:
        allOf:
        - $ref: '#/definitions/v1.LuncaiBelongTable'
        description: 表
    type: object
  v1.LuncaiBelongDetail:
    properties:
      caixingXiji:
        description: 财星喜忌
        type: string
      cangcai:
        allOf:
        - $ref: '#/definitions/v1.LuncaiBelongDetailCangcai'
        description: 命局藏财情况
      opportunity:
        description: 位置与时机
        type: string
    type: object
  v1.LuncaiBelongDetailCangcai:
    properties:
      ancangRuku:
        description: 是否暗藏入库
        type: boolean
      deling:
        description: 得令
        type: boolean
      dizhiCaiNum:
        description: 地支财位个数
        type: integer
      shiling:
        description: 失令
        type: boolean
      tianganCaiNum:
        description: 天干财位个数
        type: integer
    type: object
  v1.LuncaiBelongTable:
    properties:
      dizhi:
        description: 地支
        items:
          type: string
        type: array
      dizhiCaiwei:
        description: 地支财位
        items:
          type: string
        type: array
      gongwei:
        description: 宫位
        items:
          items:
            type: string
          type: array
        type: array
      lifeStage:
        description: 人生阶段
        items:
          type: string
        type: array
      nianling:
        description: 年龄
        items:
          type: string
        type: array
      sizhu:
        description: 四柱
        items:
          type: string
        type: array
      tiangan:
        description: 天干
        items:
          type: string
        type: array
      tianganCaiwei:
        description: 天干财位
        items:
          type: string
        type: array
    type: object
  v1.LuncaiCareerPalace:
    properties:
      down:
        description: 下（六合地支）
        type: string
      left:
        description: 左（三合地支1）
        type: string
      middle:
        description: 中（月支）
        type: string
      right:
        description: 右（三合地支2）
        type: string
      shishen:
        description: 月支十神
        type: string
      yueling:
        allOf:
        - $ref: '#/definitions/v1.LuncaiCareerPalaceYueling'
        description: 月令
    type: object
  v1.LuncaiCareerPalaceYueling:
    properties:
      dizhi:
        description: 地支（三合或六合地支）
        items:
          type: string
        type: array
      xiyong:
        description: 喜用（地支与地支五行）
        example: 卯木
        type: string
    type: object
  v1.LuncaiDancai:
    properties:
      ability:
        type: string
      dangyi:
        items:
          type: integer
        type: array
      dangyiPercent:
        items:
          type: number
        type: array
      gejuCankao:
        type: string
      recommendation:
        type: string
      wangshuai:
        type: string
    type: object
  v1.LuncaiDayunliunian:
    properties:
      dayunList:
        description: 大运列表
        items:
          type: string
        type: array
      endYear:
        description: 结束年份
        type: integer
      ganzhiList:
        description: 干支列表
        items:
          type: string
        type: array
      scoreList:
        description: 评分列表
        items:
          type: integer
        type: array
      startYear:
        description: 开始年份
        type: integer
      yearList:
        description: 年份列表
        items:
          type: integer
        type: array
    type: object
  v1.LuncaiMingli:
    properties:
      gejucankao:
        description: 格局参考
        type: string
      graph:
        allOf:
        - $ref: '#/definitions/corona.PaipanBaziLiutongGraph'
        description: 流通图
      liuTongNum:
        description: 流通个数
        type: integer
      pingfen:
        description: 评分
        type: string
      shishen:
        description: 四柱十神
        items:
          items:
            type: string
          type: array
        type: array
      tiaohou:
        description: 调候
        type: string
      tongYi:
        description: 同异
        items:
          type: integer
        type: array
      tongYiPercent:
        description: 同异百分比
        items:
          type: number
        type: array
      wangshuai:
        description: 旺衰
        type: string
      wuxing:
        description: 五行：用神,喜神,忌神,仇神,闲神
        items:
          type: string
        type: array
      yingYang:
        description: 阴阳数量
        items:
          type: integer
        type: array
      yingYangPercent:
        description: 阴阳百分比
        items:
          type: number
        type: array
      zuaiNum:
        description: 阻碍个数
        type: integer
    type: object
  v1.LuncaiMinzhu:
    properties:
      bazi:
        description: 八字
        items:
          type: string
        type: array
      benqi:
        description: 本气
        items:
          type: string
        type: array
      benqiShishen:
        description: 本气十神
        items:
          type: string
        type: array
      birthplace:
        description: 出生地
        items:
          type: string
        type: array
      birthtime:
        description: 出生日期
        type: string
      birthtimeLunar:
        description: 农历生日
        type: string
      dizhi:
        description: 地支
        items:
          type: string
        type: array
      dizhiShishen:
        description: 地支十神
        items:
          type: string
        type: array
      dizhiWuxing:
        description: 地支五行
        items:
          type: string
        type: array
      gender:
        description: 性别
        type: string
      jiaoyun:
        description: 交运
        type: string
      name:
        description: 姓名
        type: string
      nayin:
        description: 纳音
        items:
          type: string
        type: array
      qiyun:
        description: 命主起运
        type: string
      riyuan:
        description: 日元
        type: string
      shenshaJishen:
        description: 神煞吉神
        items:
          items:
            type: string
          type: array
        type: array
      tiangan:
        description: 天干，索引：0-年柱、1-月柱、2-日柱、3-时柱、4-大运、5-流年、6-流月、7-流日、8-流时
        items:
          type: string
        type: array
      tianganWuxing:
        description: 天干五行
        items:
          type: string
        type: array
      wuxing:
        description: 五行：用神,喜神,忌神,仇神,闲神
        items:
          type: string
        type: array
      wuxingNeed:
        description: 五行缺
        type: string
      wuxingWxxqs:
        additionalProperties:
          type: string
        description: 五行旺相休囚死
        type: object
      yuqi:
        description: 余气
        items:
          type: string
        type: array
      yuqiShishen:
        description: 余气十神
        items:
          type: string
        type: array
      zhongqi:
        description: 中气
        items:
          type: string
        type: array
      zhongqiShishen:
        description: 中气十神
        items:
          type: string
        type: array
      zhuxing:
        description: 主星
        items:
          type: string
        type: array
      zodiac:
        description: 生肖
        type: string
    type: object
  v1.LuncaiRisk:
    properties:
      detail:
        description: 风险详情
        items:
          type: string
        type: array
      shishen:
        description: 四柱十神
        items:
          items:
            type: string
          type: array
        type: array
    type: object
  v1.MingliBaziCurrentDayun:
    properties:
      bestYear:
        description: 最佳年份
        items:
          items: {}
          type: array
        type: array
      dayun:
        description: 当前大运
        type: string
      inDayun:
        description: 是否在大运中
        type: boolean
      jiaoyun:
        description: 交运
        type: string
      wangshuai:
        allOf:
        - $ref: '#/definitions/v1.MingliBaziCurrentDayunWangshuai'
        description: 旺衰
      worstYear:
        description: 最差年份
        items:
          items: {}
          type: array
        type: array
    type: object
  v1.MingliBaziCurrentDayunWangshuai:
    properties:
      dizhi:
        description: 地支
        type: string
      tiangan:
        description: 天干
        type: string
    type: object
  v1.MingliBaziQwLinkRequest:
    properties:
      btn:
        description: 按钮ID
        example: "00"
        type: string
      id:
        description: 记录ID
        example: 1
        type: integer
      phone:
        description: 手机号
        type: string
      type:
        description: 类型：1-联系我、2-获客链接
        example: 1
        type: integer
    required:
    - btn
    - id
    - type
    type: object
  v1.MingliBaziQwLinkResponse:
    properties:
      code:
        type: integer
      data:
        type: string
      message:
        type: string
    type: object
  v1.MingliBaziReplayRequest:
    properties:
      currentTime:
        description: 当前时间
        example: "2006-01-02 15:04:05"
        type: string
      id:
        description: 记录ID
        example: 1
        type: integer
    required:
    - currentTime
    - id
    type: object
  v1.MingliBaziReplayResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.MingliBaziReplayResponseData'
      message:
        type: string
    type: object
  v1.MingliBaziReplayResponseData:
    properties:
      advice:
        description: 建议
        items:
          type: string
        type: array
      belong:
        allOf:
        - $ref: '#/definitions/v1.LuncaiBelong'
        description: 财富所属
      caiyuan:
        description: 财源与求财意向
        items:
          type: string
        type: array
      careerPalace:
        allOf:
        - $ref: '#/definitions/v1.LuncaiCareerPalace'
        description: 事业宫看财富贵人
      careerSuggestion:
        description: 职业建议
        items:
          type: string
        type: array
      currentDayun:
        allOf:
        - $ref: '#/definitions/v1.MingliBaziCurrentDayun'
        description: 当前大运
      dancai:
        allOf:
        - $ref: '#/definitions/v1.LuncaiDancai'
        description: 担财能力
      dayunliunian:
        allOf:
        - $ref: '#/definitions/v1.LuncaiDayunliunian'
        description: 大运流年
      id:
        description: 返回记录ID（用于用户付费）
        type: integer
      isShow:
        description: 是否显示：true-显示，false-不显示
        type: boolean
      liunian:
        description: 流年
        type: string
      mingli:
        allOf:
        - $ref: '#/definitions/v1.LuncaiMingli'
        description: 命理
      mingzhu:
        allOf:
        - $ref: '#/definitions/v1.LuncaiMinzhu'
        description: 命主信息
      nengliang:
        description: 五行能量
        items:
          $ref: '#/definitions/corona.PaipanShishenPowerItem'
        type: array
      risk:
        allOf:
        - $ref: '#/definitions/v1.LuncaiRisk'
        description: 风险与偏好
      shensha:
        description: 神煞看财运
        items:
          type: string
        type: array
      wuxingTiaohe:
        allOf:
        - $ref: '#/definitions/v1.MingliBaziWuxingTiaohe'
        description: 五行调和
      xingge:
        description: 性格
        type: string
    type: object
  v1.MingliBaziRequest:
    properties:
      birthplace:
        description: 出生地
        items:
          type: string
        type: array
      birthtime:
        description: 生日
        example: "2006-01-02 15:04:05"
        type: string
      currentTime:
        description: 当前时间
        example: "2006-01-02 15:04:05"
        type: string
      gender:
        enum:
        - 男
        - 女
        example: 男
        type: string
      name:
        description: 姓名
        example: 张三
        type: string
      scene:
        description: 场景
        example: "1"
        type: string
    required:
    - birthtime
    - currentTime
    - gender
    type: object
  v1.MingliBaziResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.MingliBaziResponseData'
      message:
        type: string
    type: object
  v1.MingliBaziResponseData:
    properties:
      advice:
        description: 建议
        items:
          type: string
        type: array
      belong:
        allOf:
        - $ref: '#/definitions/v1.LuncaiBelong'
        description: 财富所属
      caiyuan:
        description: 财源与求财意向
        items:
          type: string
        type: array
      careerPalace:
        allOf:
        - $ref: '#/definitions/v1.LuncaiCareerPalace'
        description: 事业宫看财富贵人
      careerSuggestion:
        description: 职业建议
        items:
          type: string
        type: array
      currentDayun:
        allOf:
        - $ref: '#/definitions/v1.MingliBaziCurrentDayun'
        description: 当前大运
      dancai:
        allOf:
        - $ref: '#/definitions/v1.LuncaiDancai'
        description: 担财能力
      dayunliunian:
        allOf:
        - $ref: '#/definitions/v1.LuncaiDayunliunian'
        description: 大运流年
      id:
        description: 返回记录ID（用于用户付费）
        type: integer
      isShow:
        description: 是否显示：true-显示，false-不显示
        type: boolean
      liunian:
        description: 流年
        type: string
      mingli:
        allOf:
        - $ref: '#/definitions/v1.LuncaiMingli'
        description: 命理
      mingzhu:
        allOf:
        - $ref: '#/definitions/v1.LuncaiMinzhu'
        description: 命主信息
      nengliang:
        description: 五行能量
        items:
          $ref: '#/definitions/corona.PaipanShishenPowerItem'
        type: array
      risk:
        allOf:
        - $ref: '#/definitions/v1.LuncaiRisk'
        description: 风险与偏好
      shensha:
        description: 神煞看财运
        items:
          type: string
        type: array
      wuxingTiaohe:
        allOf:
        - $ref: '#/definitions/v1.MingliBaziWuxingTiaohe'
        description: 五行调和
      xingge:
        description: 性格
        type: string
    type: object
  v1.MingliBaziSMSRequest:
    properties:
      id:
        description: 论财ID（排盘ID）
        example: 1
        type: integer
      phone:
        description: 手机号
        example: "13800138000"
        type: string
    required:
    - id
    - phone
    type: object
  v1.MingliBaziSMSResponse:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  v1.MingliBaziWuxingTiaohe:
    properties:
      pingheng:
        description: 平衡
        items:
          type: string
        type: array
      wangshuai:
        description: 旺衰
        items:
          type: string
        type: array
    type: object
  v1.MingliBaziYearRequest:
    properties:
      currentYear:
        description: 当前年份
        example: 2021
        type: integer
      id:
        description: 记录ID
        example: 1
        type: integer
    required:
    - currentYear
    - id
    type: object
  v1.MingliBaziYearResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.YunshiResponseData'
      message:
        type: string
    type: object
  v1.PageListPaipanRecordRequest:
    properties:
      pageNum:
        example: 1
        minimum: 1
        type: integer
      pageSize:
        example: 10
        minimum: 1
        type: integer
      param:
        $ref: '#/definitions/v1.PageListPaipanRecordRequestParam'
    required:
    - pageNum
    - pageSize
    type: object
  v1.PageListPaipanRecordRequestParam:
    properties:
      appIDs:
        description: 应用ID：2-排盘、3-万年历、4-运势、5-论财
        items:
          type: integer
        type: array
      application:
        description: 应用标识
        type: string
      bazi:
        description: 八字
        type: string
      birthTimeEnd:
        description: 出生日期结束
        type: string
      birthTimeStart:
        description: 出生日期开始
        type: string
      birthTimeSunEnd:
        description: 真太阳时结束
        type: string
      birthTimeSunStart:
        description: 真太阳时开始
        type: string
      birthplace:
        description: 出生地
        type: string
      gender:
        description: 性别：1-男、2-女
        items:
          type: integer
        type: array
      name:
        description: 命例名称
        type: string
    type: object
  v1.PageListPaipanRecordResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/v1.PageListPaipanRecordResponseData'
      message:
        type: string
    type: object
  v1.PageListPaipanRecordResponseData:
    properties:
      list:
        items:
          $ref: '#/definitions/v1.PageListPaipanRecordResponseDataItem'
        type: array
      total:
        type: integer
    type: object
  v1.PageListPaipanRecordResponseDataItem:
    properties:
      appID:
        description: 应用ID
        example: 1
        type: integer
      appName:
        description: 应用名称
        type: string
      appPlatformID:
        description: 应用平台ID
        example: 1
        type: integer
      appPlatformName:
        description: 应用平台名称
        type: string
      bazi:
        description: 八字
        items:
          type: string
        type: array
      birthplace:
        description: 出生地
        items:
          type: string
        type: array
      birthtime:
        description: 出生日期
        example: "2021-01-01 00:00:00"
        type: string
      birthtimeLunar:
        description: 农历出生日期
        type: string
      birthtimeSun:
        description: 真太阳时
        type: string
      createdAt:
        description: 创建时间
        type: string
      createdTime:
        description: 创建时间
        type: string
      extraInfo:
        additionalProperties: {}
        description: 额外信息
        type: object
      gender:
        description: 性别：1-男, 2-女
        example: "1"
        type: string
      id:
        type: integer
      name:
        description: 命例名称
        type: string
      saveTime:
        description: 保存时间
        type: string
      type:
        description: 类型
        type: string
      userAgent:
        description: 用户代理
        type: string
      userID:
        description: 用户ID
        type: string
    type: object
  v1.PaipanRecordOwnRequest:
    properties:
      ids:
        description: ID
        items:
          type: integer
        type: array
    required:
    - ids
    type: object
  v1.PaipanRecordOwnResponse:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  v1.YunshiMinzhu:
    properties:
      bazi:
        description: 八字
        items:
          type: string
        type: array
      birthplace:
        description: 出生地
        items:
          type: string
        type: array
      birthtime:
        description: 出生日期
        type: string
      birthtimeLunar:
        description: 农历生日
        type: string
      gender:
        description: 性别
        type: string
      name:
        description: 姓名
        type: string
      wuxing:
        description: 五行：用神,喜神,忌神,仇神,闲神
        items:
          type: string
        type: array
      zodiac:
        description: 生肖
        type: string
    type: object
  v1.YunshiNianScore:
    properties:
      finalScore:
        type: number
      ganzhi:
        type: string
      keyword:
        type: string
    type: object
  v1.YunshiResponseData:
    properties:
      guaxiang:
        allOf:
        - $ref: '#/definitions/model.Guaxiang'
        description: 卦象
      id:
        type: integer
      mingzhu:
        allOf:
        - $ref: '#/definitions/v1.YunshiMinzhu'
        description: 命主信息
      score:
        allOf:
        - $ref: '#/definitions/v1.YunshiScore'
        description: 评分
      suggestions:
        description: 建议
        items:
          type: string
        type: array
    type: object
  v1.YunshiScore:
    properties:
      dayun:
        allOf:
        - $ref: '#/definitions/v1.YunshiScoreDayun'
        description: 大运
      liunian:
        allOf:
        - $ref: '#/definitions/v1.YunshiNianScore'
        description: 流年
      liuyue:
        description: 流月（十三个月，一月到下一年一月）
        items:
          $ref: '#/definitions/v1.YunshiYueScore'
        type: array
    type: object
  v1.YunshiScoreDayun:
    properties:
      ganzhi:
        type: string
      score:
        type: number
    type: object
  v1.YunshiYueScore:
    properties:
      finalScore:
        type: number
      ganzhi:
        type: string
      jieqi:
        type: string
      jieqiTime:
        type: string
    type: object
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: This is a http server template.
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: 在线投放
  version: 1.0.0
paths:
  /app/version/newest:
    post:
      consumes:
      - application/json
      description: 最新版本
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.CheckAppUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 最新版本
          schema:
            $ref: '#/definitions/v1.CheckAppUpdateResponse'
      summary: 最新版本
      tags:
      - 应用版本
  /date/day:
    post:
      consumes:
      - application/json
      description: 获取本日日历
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.CalendarDayRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.CalendarDayResponse'
      summary: 获取本日日历
      tags:
      - 日历
  /date/month:
    post:
      consumes:
      - application/json
      description: 获取本月日历
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.CalendarMonthRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.CalendarMonthResponse'
      summary: 获取本月日历
      tags:
      - 日历
  /datetime/bazi:
    post:
      consumes:
      - application/json
      description: 八字
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.GetBaziRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.GetBaziResponse'
      summary: 八字
      tags:
      - 时间
  /datetime/fromSizhu:
    post:
      consumes:
      - application/json
      description: 从四柱获取时间
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.GetDatetimeBySizhuRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.GetDatetimeBySizhuResponse'
      summary: 从四柱获取时间
      tags:
      - 时间
  /enums/dizhi:
    post:
      consumes:
      - application/json
      description: 获取地支
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsDizhiResponse'
      summary: 获取地支
      tags:
      - 枚举
  /enums/location:
    post:
      consumes:
      - application/json
      description: 获取地区列表
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.EnumsLocationRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsLocationResponse'
      summary: 获取地区列表
      tags:
      - 枚举
  /enums/lunar:
    post:
      consumes:
      - application/json
      description: 获取农历列表
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.EnumsLunarRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsLunarResponse'
      summary: 获取农历列表
      tags:
      - 枚举
  /enums/shishen/property:
    post:
      consumes:
      - application/json
      description: 获取十神特征
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsShishenPropertyResponse'
      summary: 获取十神特征
      tags:
      - 枚举
  /enums/tiangan:
    post:
      consumes:
      - application/json
      description: 获取天干
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsTianganResponse'
      summary: 获取天干
      tags:
      - 枚举
  /enums/wuxing:
    post:
      consumes:
      - application/json
      description: 获取五行
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.EnumsWuxingResponse'
      summary: 获取五行
      tags:
      - 枚举
  /location:
    post:
      consumes:
      - application/json
      description: 获取地区树
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.LocationRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.LocationResponse'
      summary: 获取地区树
      tags:
      - 地区
  /minglibazi/:
    post:
      consumes:
      - application/json
      description: 命理八字
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.MingliBaziRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.MingliBaziResponse'
      summary: 命理八字
      tags:
      - 命理八字
  /minglibazi/click:
    post:
      consumes:
      - application/json
      description: 命理八字点击
      produces:
      - application/json
      responses: {}
      summary: 命理八字点击
      tags:
      - 命理八字
  /minglibazi/qw:
    post:
      consumes:
      - application/json
      description: 命理八字测算企微联系我
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.MingliBaziQwLinkRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.MingliBaziQwLinkResponse'
      summary: 命理八字测算企微联系我
      tags:
      - 命理八字
  /minglibazi/replay:
    post:
      consumes:
      - application/json
      description: 命理八字重播
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.MingliBaziReplayRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.MingliBaziReplayResponse'
      summary: 命理八字重播
      tags:
      - 命理八字
  /minglibazi/sms:
    post:
      consumes:
      - application/json
      description: 命理八字短信
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.MingliBaziSMSRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.MingliBaziSMSResponse'
      summary: 命理八字短信
      tags:
      - 命理八字
  /minglibazi/year:
    post:
      consumes:
      - application/json
      description: 命理八字年运
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.MingliBaziYearRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.MingliBaziYearResponse'
      summary: 命理八字年运
      tags:
      - 命理八字
  /paipanRecord/delete:
    post:
      consumes:
      - application/json
      description: 删除用户排盘记录
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.DeletePaipanRecordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.DeletePaipanRecordResponse'
      security:
      - BearerAuth: []
      summary: 删除用户排盘记录
      tags:
      - 用户排盘记录
  /paipanRecord/own:
    post:
      consumes:
      - application/json
      description: 占有排盘记录
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.PaipanRecordOwnRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.PaipanRecordOwnResponse'
      security:
      - BearerAuth: []
      summary: 占有排盘记录
      tags:
      - 排盘
  /paipanRecord/pageList:
    post:
      consumes:
      - application/json
      description: 分页查询用户排盘记录
      parameters:
      - description: params
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.PageListPaipanRecordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.PageListPaipanRecordResponse'
      security:
      - BearerAuth: []
      summary: 分页查询用户排盘记录
      tags:
      - 用户排盘记录
securityDefinitions:
  BearerAuth:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
