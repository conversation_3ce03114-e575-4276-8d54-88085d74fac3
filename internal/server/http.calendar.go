package server

import (
	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	swaggerfiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"zodiacus/docs"
	"zodiacus/internal/handler/calendar"
	"zodiacus/internal/middleware"
	"zodiacus/pkg/jwthub"
	"zodiacus/pkg/log"
	"zodiacus/pkg/server/http"
	"zodiacus/third_party/casdoor"
)

func NewCalendarHTTPServer(
	logger *log.Logger,
	conf *viper.Viper,
	identity *casdoor.Client,
	dateHandler *calendar.DateHandler,
	datesubHandler *calendar.DateSubHandler,
	datetimeHandler *calendar.DateTimeHandler,
	enumsHandler *calendar.EnumsHandler,
	historyHandler *calendar.HistoryHandler,
	jiemengHandler *calendar.JiemengHandler,
	mingliHandler *calendar.UserMingliHandler,
	countdownHandler *calendar.CountdownDayHandler,
	avHandler *calendar.AppVersion<PERSON>and<PERSON>,
	jh *jwthub.Jwthub,
) *http.Server {
	if conf.GetString("env") == "prod" {
		gin.SetMode(gin.ReleaseMode)
	} else {
		gin.SetMode(gin.DebugMode)
	}
	host, port := conf.GetString("http.calendar.host"), conf.GetInt("http.calendar.port")
	srv := http.NewServer(
		gin.Default(),
		logger,
		http.WithServerHost(host),
		http.WithServerPort(port),
	)

	// swagger doc
	docs.SwaggerInfocalendar.BasePath = "/v1"
	srv.GET("/swagger/*any", ginSwagger.WrapHandler(
		swaggerfiles.Handler,
		ginSwagger.DefaultModelsExpandDepth(-1),
		ginSwagger.PersistAuthorization(true),
		ginSwagger.InstanceName("calendar"),
	))

	srv.Use(
		middleware.CORSMiddleware(),
		middleware.ResponseLogMiddleware(logger),
		middleware.ResponseLanguageConvertMiddleware(logger),
		//middleware.RequestLanguageConvertMiddleware(),
		middleware.RequestLogMiddleware(logger),
		//middleware.SignMiddleware(log),
	)

	v1Group := srv.Group("/v1")
	{
		// 强制鉴权
		required := v1Group.Group("/")
		required.Use(middleware.StrictAuth(identity, jh, logger))
		// 可选鉴权
		optional := v1Group.Group("/")
		optional.Use(middleware.NoStrictAuth(identity, jh, logger))
		// 无需鉴权
		nameless := v1Group.Group("/")

		// 日历、月历、个人历、今天干什么
		dateHandler.Handle(required, optional, nameless)
		// 日期计算
		datesubHandler.Handle(required, optional, nameless)
		// 四柱反查时间
		datetimeHandler.Handle(required, optional, nameless)
		// 枚举
		enumsHandler.Handle(required, optional, nameless)
		// 历史事件
		historyHandler.Handle(required, optional, nameless)
		// 解梦
		jiemengHandler.Handle(required, optional, nameless)
		// 命例
		mingliHandler.Handle(required, optional, nameless)
		// 倒数日
		countdownHandler.Handle(required, optional, nameless)
		// 版本
		avHandler.Handle(required, optional, nameless)
	}

	return srv
}
