package server

import (
	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	swaggerfiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"zodiacus/docs"
	"zodiacus/internal/handler/adflow"
	"zodiacus/internal/middleware"
	"zodiacus/internal/repository"
	"zodiacus/pkg/jwthub"
	"zodiacus/pkg/log"
	"zodiacus/pkg/server/http"
	"zodiacus/third_party/casdoor"
)

func NewAdFlowHTTPServer(
	logger *log.Logger,
	conf *viper.Viper,
	identity *casdoor.Client,
	datetimeHandler *adflow.DateTimeHandler,
	locationHandler *adflow.LocationHandler,
	enumsHandler *adflow.EnumsHandler,
	paipanRecordHandler *adflow.PaipanRecordHandler,
	vipRepo repository.VIPRepository,
	dateHandler *adflow.DateHandler,
	avHandler *adflow.AppVersionHandler,
	mlbz *adflow.AdflowHandler,
	jh *jwthub.Jwthub,
) *http.Server {
	if conf.GetString("env") == "prod" {
		gin.SetMode(gin.ReleaseMode)
	} else {
		gin.SetMode(gin.DebugMode)
	}
	host, port := conf.GetString("http.adflow.host"), conf.GetInt("http.adflow.port")
	srv := http.NewServer(
		gin.Default(),
		logger,
		http.WithServerHost(host),
		http.WithServerPort(port),
	)

	// swagger doc
	docs.SwaggerInfoadflow.BasePath = "/v1"
	srv.GET("/swagger/*any", ginSwagger.WrapHandler(
		swaggerfiles.Handler,
		ginSwagger.DefaultModelsExpandDepth(-1),
		ginSwagger.PersistAuthorization(true),
		ginSwagger.InstanceName("adflow"),
	))

	srv.Use(
		middleware.CORSMiddleware(),
		middleware.ResponseLogMiddleware(logger),
		middleware.ResponseLanguageConvertMiddleware(logger),
		//middleware.RequestLanguageConvertMiddleware(),
		middleware.RequestLogMiddleware(logger),
		//middleware.SignMiddleware(log),
	)

	v1Group := srv.Group("/v1")
	{
		// 强制鉴权
		required := v1Group.Group("/")
		//required.Use(middleware.StrictAuth(identity, jh, logger), middleware.VIP(vipRepo, logger))
		required.Use(middleware.StrictAuth(identity, jh, logger))
		// 可选鉴权
		optional := v1Group.Group("/")
		//optional.Use(middleware.NoStrictAuth(identity, jh, logger), middleware.VIP(vipRepo, logger))
		optional.Use(middleware.NoStrictAuth(identity, jh, logger))
		// 无需鉴权
		nameless := v1Group.Group("/")

		// 四柱反查时间
		datetimeHandler.Handle(required, optional, nameless)
		// 枚举
		enumsHandler.Handle(required, optional, nameless)
		// 地区
		locationHandler.Handle(required, optional, nameless)
		// 排盘记录
		paipanRecordHandler.Handle(required, optional, nameless)
		// 日期
		dateHandler.Handle(required, optional, nameless)
		// 应用版本
		avHandler.Handle(required, optional, nameless)
		// 命理八字
		mlbz.Handle(required, optional, nameless)
	}

	return srv
}
