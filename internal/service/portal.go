package service

import (
	"context"
	"time"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
	"zodiacus/internal/repository"
)

type PortalService interface {
	CreateSection(ctx context.Context, req *v1.CreatePortalSectionRequest) (*v1.CreatePortalSectionResponseData, error)
	UpdateSection(ctx context.Context, req *v1.UpdatePortalSectionRequest) error
	DeleteSection(ctx context.Context, req *v1.DeletePortalSectionRequest) error
	ListSection(ctx context.Context, req *v1.ListPortalSectionRequest) (v1.ListPortalSectionResponseData, error)
	CreateArticle(ctx context.Context, req *v1.CreatePortalArticleRequest) (*v1.CreatePortalArticleResponseData, error)
	UpdateArticle(ctx context.Context, req *v1.UpdatePortalArticleRequest) error
	DeleteArticle(ctx context.Context, req *v1.DeletePortalArticleRequest) error
	PageListArticle(ctx context.Context, req *v1.PageListPortalArticleRequest) (*v1.PageListPortalArticleResponseData, error)
	ListTag(ctx context.Context) (v1.ListTagResponseData, error)
	DeleteTag(ctx context.Context, req *v1.DeletePortalTagRequest) error
}

func NewPortalService(
	service *Service,
	portalRepo repository.PortalRepository,
) PortalService {
	return &portalService{
		Service:    service,
		portalRepo: portalRepo,
	}
}

type portalService struct {
	*Service
	portalRepo repository.PortalRepository
}

// DeleteTag 删除文章标签
func (slf *portalService) DeleteTag(ctx context.Context, req *v1.DeletePortalTagRequest) error {
	return slf.portalRepo.DeleteTags(ctx, req.Names)
}

// ListTag 文章标签列表
func (slf *portalService) ListTag(ctx context.Context) (v1.ListTagResponseData, error) {
	return slf.portalRepo.ListTag(ctx)
}

// CreateSection 创建门户栏目
func (slf *portalService) CreateSection(ctx context.Context, req *v1.CreatePortalSectionRequest) (*v1.CreatePortalSectionResponseData, error) {
	id, err := slf.portalRepo.CreateSection(ctx, &model.PortalSection{
		Name:   req.Name,
		Remark: req.Remark,
	})
	if err != nil {
		return nil, err
	}
	return &v1.CreatePortalSectionResponseData{
		ID: id,
	}, nil
}

// UpdateSection 更新门户栏目
func (slf *portalService) UpdateSection(ctx context.Context, req *v1.UpdatePortalSectionRequest) error {
	section, err := slf.portalRepo.FetchSectionByID(ctx, req.ID)
	if err != nil {
		return err
	}
	if section == nil {
		return nil
	}
	if req.Name != nil {
		section.Name = *req.Name
	}
	if req.Remark != nil {
		section.Remark = *req.Remark
	}
	if req.IsEnable != nil {
		section.IsEnable = *req.IsEnable
	}
	return slf.portalRepo.UpdateSection(ctx, section)
}

// DeleteSection 删除门户栏目
func (slf *portalService) DeleteSection(ctx context.Context, req *v1.DeletePortalSectionRequest) error {
	if err := slf.tx.Transaction(ctx, func(ctx context.Context) error {
		count, err := slf.portalRepo.CountArticleBySectionID(ctx, req.ID)
		if err != nil {
			return err
		}
		if count > 0 {
			return v1.ErrPortalSectionHasArticle
		}
		return slf.portalRepo.DeleteSection(ctx, req.ID)
	}); err != nil {
		return err
	}
	return nil
}

// ListSection 列出门户栏目
func (slf *portalService) ListSection(ctx context.Context, req *v1.ListPortalSectionRequest) (v1.ListPortalSectionResponseData, error) {
	return slf.portalRepo.ListSection(ctx, req)
}

func (slf *portalService) CreateArticle(ctx context.Context, req *v1.CreatePortalArticleRequest) (*v1.CreatePortalArticleResponseData, error) {
	section, err := slf.portalRepo.FetchSectionByID(ctx, req.SectionID)
	if err != nil {
		return nil, err
	}
	if section == nil {
		return nil, v1.ErrPortalSectionNotFound
	}
	current := time.Now()
	var (
		topTime     *time.Time
		publishTime *time.Time
	)
	if req.IsTop {
		topTime = &current
	}
	if req.IsPublish {
		publishTime = &current
	}
	var id int64
	if err = slf.tx.Transaction(ctx, func(ctx context.Context) error {
		if err = slf.portalRepo.CreateTags(ctx, req.Tags); err != nil {
			return err
		}
		id, err = slf.portalRepo.CreateArticle(ctx, &model.PortalArticle{
			SectionID:   req.SectionID,
			Title:       req.Title,
			Author:      req.Author,
			Tags:        req.Tags,
			IsTop:       req.IsTop,
			TopTime:     topTime,
			IsPublish:   req.IsPublish,
			PublishTime: publishTime,
			IsModify:    false,
			ModifyTime:  nil,
			Content:     req.Content,
			Remark:      req.Remark,
		})
		if err != nil {
			return err
		}
		return nil
	}); err != nil {
		return nil, err
	}
	return &v1.CreatePortalArticleResponseData{
		ID: id,
	}, nil
}

func (slf *portalService) UpdateArticle(ctx context.Context, req *v1.UpdatePortalArticleRequest) error {
	article, err := slf.portalRepo.FetchArticleByID(ctx, req.ID)
	if err != nil {
		return err
	}
	if article == nil {
		return v1.ErrPortalArticleNotFound
	}
	var (
		current  = time.Now()
		isModify = false
	)
	if req.SectionID != nil {
		section, err := slf.portalRepo.FetchSectionByID(ctx, *req.SectionID)
		if err != nil {
			return err
		}
		if section == nil {
			return v1.ErrPortalSectionNotFound
		}
		article.SectionID = *req.SectionID
	}
	if req.Title != nil {
		article.Title = *req.Title
		isModify = true
	}
	if req.Author != nil {
		article.Author = *req.Author
	}
	if req.Tags != nil {
		article.Tags = req.Tags
	}
	if req.IsTop != nil {
		article.IsTop = *req.IsTop
		if article.IsTop {
			article.TopTime = &current
		} else {
			article.TopTime = nil
		}
	}
	if req.IsPublish != nil {
		article.IsPublish = *req.IsPublish
		if article.IsPublish {
			article.PublishTime = &current
		} else {
			article.PublishTime = nil
		}
	}
	if req.Content != nil {
		article.Content = *req.Content
		isModify = true
	}
	if req.Remark != nil {
		article.Remark = *req.Remark
	}
	if isModify {
		article.IsModify = true
		article.ModifyTime = &current
	}
	if err = slf.tx.Transaction(ctx, func(ctx context.Context) error {
		if err = slf.portalRepo.CreateTags(ctx, article.Tags); err != nil {
			return err
		}
		if err = slf.portalRepo.UpdateArticle(ctx, article); err != nil {
			return err
		}
		return nil
	}); err != nil {
		return err
	}
	return nil
}

func (slf *portalService) DeleteArticle(ctx context.Context, req *v1.DeletePortalArticleRequest) error {
	return slf.portalRepo.DeleteArticle(ctx, req.ID)
}

func (slf *portalService) PageListArticle(ctx context.Context, req *v1.PageListPortalArticleRequest) (*v1.PageListPortalArticleResponseData, error) {
	return slf.portalRepo.PageListArticle(ctx, req)
}
