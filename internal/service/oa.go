package service

import (
	"bytes"
	"context"
	qrcodeRequest "github.com/ArtisanCloud/PowerWeChat/v3/src/basicService/qrCode/request"
	"github.com/ArtisanCloud/PowerWeChat/v3/src/kernel/contract"
	"github.com/ArtisanCloud/PowerWeChat/v3/src/kernel/messages"
	"github.com/ArtisanCloud/PowerWeChat/v3/src/kernel/models"
	materialResponse "github.com/ArtisanCloud/PowerWeChat/v3/src/officialAccount/material/response"
	oaModel "github.com/ArtisanCloud/PowerWeChat/v3/src/officialAccount/server/handlers/models"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"io"
	"mime/multipart"
	"net/http"
	"strconv"
	"strings"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
	"zodiacus/internal/repository"
	"zodiacus/third_party/offiaccount"
)

type OffiaccountService interface {
	OffiaccountVerify(r *http.Request) (string, error)
	OffiaccountNotify(r *http.Request) (*http.Response, error)
	CreateQrcode(ctx context.Context, req *v1.OaCreateQrcodeRequest) (*v1.OaCreateQrcodeResponseData, error)
	CreateMenu(ctx context.Context, req *v1.OaCreateMenuRequest) error
	GetMenuDetail(ctx context.Context) (*v1.OaGetMenuDetailResponseData, error)
	AddMaterial(ctx context.Context, req *v1.OaAppMaterialRequest) (*v1.OaAppMaterialResponseData, error)
}

func NewOffiaccountService(svc *Service, offiaccount *offiaccount.Application, offiaccountRepository repository.OffiaccountRepository) OffiaccountService {
	return &offiaccountService{
		Service:         svc,
		offiaccount:     offiaccount,
		offiaccountRepo: offiaccountRepository,
	}
}

type offiaccountService struct {
	*Service
	offiaccount     *offiaccount.Application
	offiaccountRepo repository.OffiaccountRepository
}

func (slf *offiaccountService) fileHeader2Bytes(fh *multipart.FileHeader) ([]byte, error) {
	fp, err := fh.Open()
	if err != nil {
		return nil, err
	}
	defer fp.Close()
	var buf bytes.Buffer
	if _, err = io.Copy(&buf, fp); err != nil {
		return nil, err
	}
	return buf.Bytes(), nil
}

func (slf *offiaccountService) AddMaterial(ctx context.Context, req *v1.OaAppMaterialRequest) (*v1.OaAppMaterialResponseData, error) {
	path, err := req.FileHeader.GenerateTempFile()
	if err != nil {
		return nil, err
	}
	defer req.FileHeader.DeleteTempFile()
	var resp *materialResponse.ResponseMaterialAddMaterial
	switch req.Type {
	case "image":
		resp, err = slf.offiaccount.Material.UploadImage(ctx, path)
		if err != nil {
			return nil, err
		}
	default:
		return nil, errors.Errorf("unsupported material type: %s", req.Type)
	}
	if resp.ErrCode != 0 {
		return nil, errors.Errorf("failed to upload offiaccount material: %s", resp.ErrMsg)
	}
	if _, err = slf.offiaccountRepo.CreateMaterial(ctx, &model.OffiaccountMaterial{
		MediaID: resp.MediaID,
		Type:    req.Type,
		Remark:  req.Remark,
		Url:     resp.URL,
	}); err != nil {
		return nil, err
	}
	return &v1.OaAppMaterialResponseData{
		MediaID: resp.MediaID,
		Url:     resp.URL,
	}, nil
}

func (slf *offiaccountService) GetMenuDetail(ctx context.Context) (*v1.OaGetMenuDetailResponseData, error) {
	resp, err := slf.offiaccount.Menu.Get(ctx)
	if err != nil {
		return nil, err
	}
	if resp.ErrCode != 0 {
		return nil, errors.Errorf("failed to get offiaccount menu: %s", resp.ErrMsg)
	}
	return &v1.OaGetMenuDetailResponseData{
		Menus:            resp.Menus,
		ConditionalMenus: resp.ConditionalMenus,
	}, nil
}

func (slf *offiaccountService) CreateMenu(ctx context.Context, req *v1.OaCreateMenuRequest) error {
	resp, err := slf.offiaccount.Menu.Create(ctx, req.Buttons)
	if err != nil {
		return err
	}
	if resp.ErrCode != 0 {
		return errors.Errorf("failed to create offiaccount menu: %s", resp.ErrMsg)
	}
	return nil
}

func (slf *offiaccountService) CreateQrcode(ctx context.Context, req *v1.OaCreateQrcodeRequest) (*v1.OaCreateQrcodeResponseData, error) {
	var res *v1.OaCreateQrcodeResponseData
	if err := slf.tx.Transaction(ctx, func(ctx context.Context) error {
		qrcode, err := slf.offiaccountRepo.FetchQrcodeBySceneStr(ctx, req.SceneStr)
		if err != nil {
			return err
		}
		if qrcode != nil {
			return v1.ErrOaQrcodeSceneStrAlreadyTaken
		}
		resp, err := slf.offiaccount.QRCode.Create(ctx, &qrcodeRequest.RequestQRCodeCreate{
			ActionName: "QR_LIMIT_STR_SCENE",
			ActionInfo: &qrcodeRequest.ActionInfo{
				Scene: map[string]any{
					"scene_str": req.SceneStr,
				},
			},
		}, false, 0)
		if err != nil {
			return err
		}
		res = &v1.OaCreateQrcodeResponseData{
			Ticket:        resp.Ticket,
			ExpireSeconds: resp.ExpireSeconds,
			Url:           resp.Url,
		}
		if _, err = slf.offiaccountRepo.CreateQrcode(ctx, &model.OaQrcode{
			SceneStr:      req.SceneStr,
			Remark:        req.Remark,
			Ticket:        res.Ticket,
			ExpireSeconds: res.ExpireSeconds,
			Url:           res.Url,
		}); err != nil {
			return err
		}
		return nil
	}); err != nil {
		return nil, err
	}
	return res, nil
}

func (slf *offiaccountService) OffiaccountVerify(r *http.Request) (string, error) {
	// TODO：烦死了，总是token验证失败，why？
	var (
		//signature = r.Url.Query().Get("signature")
		//timestamp = r.Url.Query().Get("timestamp")
		//nonce     = r.Url.Query().Get("nonce")
		echostr = r.URL.Query().Get("echostr")
		//token     = slf.offiaccount.UserConfig.Get("token").(string)
	)
	/*
		if func() bool {
			var (
				arr     = []string{timestamp, nonce, token}
				l       = len(timestamp) + len(nonce) + len(token)
				builder strings.Builder
			)
			sort.Strings(arr)
			builder.Grow(l)
			for i := 0; i < len(arr); i++ {
				builder.WriteString(arr[i])
			}
			return func() string {
				h := sha1.New()
				h.Write([]byte(builder.String()))
				return hex.EncodeToString(h.Sum(nil))
			}() == signature
		}() {
			return "", errors.Errorf("invalid signature")
		}
	*/
	return echostr, nil
}

func (slf *offiaccountService) OffiaccountNotify(r *http.Request) (*http.Response, error) {
	return slf.offiaccount.Server.Notify(r, func(event contract.EventInterface) any {
		switch event.GetMsgType() {
		case models.CALLBACK_MSG_TYPE_EVENT:
			switch event.GetEvent() {
			case oaModel.CALLBACK_EVENT_SUBSCRIBE:
				slf.logger.Info("微信公众号订阅事件")
				payload := oaModel.EventSubscribe{}
				if err := event.ReadMessage(&payload); err != nil {
					slf.logger.Error("failed to read message", zap.Error(err))
					return nil
				}
				res, err := slf.handleSubscribeEvent(r.Context(), payload)
				if err != nil {
					slf.logger.Error("failed to handle subscribe event", zap.Error(err))
					return nil
				}
				return res
			case oaModel.CALLBACK_EVENT_UNSUBSCRIBE:
				slf.logger.Info("微信公众号取消订阅事件")
				payload := oaModel.EventUnSubscribe{}
				if err := event.ReadMessage(&payload); err != nil {
					slf.logger.Error("failed to read message", zap.Error(err))
					return nil
				}
				res, err := slf.handleUnsubscribeEvent(r.Context(), payload)
				if err != nil {
					slf.logger.Error("failed to handle unsubscribe event", zap.Error(err))
					return nil
				}
				return res
			case oaModel.CALLBACK_EVENT_CLICK:
				slf.logger.Info("微信公众号菜单点击事件")
				payload := oaModel.EventClick{}
				if err := event.ReadMessage(&payload); err != nil {
					slf.logger.Error("failed to read message", zap.Error(err))
					return nil
				}
				res, err := slf.handleClickEvent(r.Context(), payload)
				if err != nil {
					slf.logger.Error("failed to handle click event", zap.Error(err))
					return nil
				}
				return res
			default:
				return nil
			}
		case models.CALLBACK_MSG_TYPE_TEXT:
			payload := oaModel.MessageText{}
			if err := event.ReadMessage(&payload); err != nil {
				slf.logger.Error("failed to read message", zap.Error(err))
				return nil
			}
			if strings.Contains(payload.Content, "投诉") {
				return "很抱歉给您带来不好的体验，请描述您需要投诉内容，或在投诉建议模块（超链接https://pan.laibuyi.com/#/page_feedback/feedback/index）提交您的诉求，我们将竭诚为您服务，解决您的问题。"
			}
			return "请描述您的诉求，或添加客服企业微信，我们将竭诚为您服务"
		default:
			return nil
		}
	})
}

func (slf *offiaccountService) handleClickEvent(ctx context.Context, eve oaModel.EventClick) (any, error) {
	slf.logger.Info("微信公众号菜单点击事件", zap.Any("event", eve))
	switch eve.EventKey {
	case "kefu_qrcode":
		material, err := slf.offiaccountRepo.FetchMaterialByRemark(ctx, eve.EventKey)
		if err != nil {
			return nil, err
		}
		if material == nil {
			return nil, nil
		}
		if _, err = slf.offiaccount.CustomerService.Message(ctx, messages.Image{
			Media: messages.NewImage(material.MediaID, nil).Media,
		}).SetTo(eve.FromUserName).Send(ctx); err != nil {
			return nil, err
		}
		return nil, nil
	}
	return nil, nil
}

func (slf *offiaccountService) handleMessageEvent() {

}

func (slf *offiaccountService) handleSubscribeEvent(ctx context.Context, eve oaModel.EventSubscribe) (any, error) {
	slf.logger.Info("微信公众号订阅事件", zap.Any("event", eve))
	resp1, err := slf.offiaccount.User.Get(ctx, eve.FromUserName, "zh_CN")
	if err != nil {
		return nil, errors.Wrap(err, "failed to get user")
	}
	if resp1.ErrCode != 0 {
		return nil, errors.Errorf("failed to get user: %s", resp1.ErrMsg)
	}
	if strings.Contains(eve.EventKey, "qrscene_") {
		slf.logger.Info("微信公众号带参数扫码事件", zap.Any("event", eve))
	} else {
		slf.logger.Info("微信公众号普通订阅事件", zap.Any("event", eve))
	}
	oaUser, err := slf.offiaccountRepo.FetchOffiaccountUserByOpenID(ctx, resp1.OpenID)
	if err != nil {
		return nil, err
	}
	if oaUser != nil {
		oaUser.Subscribe = 1
		oaUser.UnsubscribeTime = 0
		if err = slf.offiaccountRepo.UpdateOffiaccountUser(ctx, oaUser); err != nil {
			return nil, err
		}
		return "\"感谢您关注论玄！\n知识改变命运，科技改变生活。\n\n点击帮您您的生辰（超链接），享受更多服务\"", nil
	}
	oaUser = &model.OffiaccountUser{
		OpenID:         resp1.OpenID,
		UnionID:        resp1.UnionID,
		Subscribe:      resp1.Subscribe,
		Language:       resp1.Language,
		SubscribeTime:  resp1.SubscribeTime,
		Remark:         resp1.Remark,
		GroupID:        resp1.GroupID,
		TagIDList:      resp1.TagIDList,
		SubscribeScene: resp1.SubscribeScene,
		QRScene:        resp1.QrScene,
		QRSceneStr:     resp1.QrSceneStr,
	}
	if _, err = slf.offiaccountRepo.CreateOffiaccountUser(ctx, oaUser); err != nil {
		return nil, err
	}
	return "\"感谢您关注论玄！\n知识改变命运，科技改变生活。\n\n点击帮您您的生辰（超链接），享受更多服务\"", nil
}

func (slf *offiaccountService) handleUnsubscribeEvent(ctx context.Context, eve oaModel.EventUnSubscribe) (any, error) {
	slf.logger.Info("微信公众号取消订阅事件", zap.Any("event", eve))
	oaUser, err := slf.offiaccountRepo.FetchOffiaccountUserByOpenID(ctx, eve.FromUserName)
	if err != nil {
		return nil, err
	}
	if oaUser == nil {
		return nil, nil
	}
	oaUser.Subscribe = 0
	oaUser.UnsubscribeTime, _ = strconv.Atoi(eve.CreateTime)
	if err = slf.offiaccountRepo.UpdateOffiaccountUser(ctx, oaUser); err != nil {
		return nil, err
	}
	return nil, nil
}
