package service

import (
	"context"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
	"zodiacus/internal/repository"
)

type AppVersionService interface {
	CreateAppVersion(ctx context.Context, req *v1.CreateAppVersionRequest) (*v1.CreateAppVersionResponseData, error)
	UpdateAppVersion(ctx context.Context, req *v1.UpdateAppVersionRequest) error
	PublishAppVersion(ctx context.Context, req *v1.PublishAppVersionRequest) error
	RecallAppVersion(ctx context.Context, req *v1.RecallAppVersionRequest) error
	PageListAppVersion(ctx context.Context, req *v1.PageListAppVersionRequest) (*v1.PageListAppVersionResponseData, error)
	CheckUpdate(ctx context.Context, req *v1.CheckAppUpdateRequest) (*v1.CheckAppUpdateResponseData, error)
}

func NewAppVersionService(service *Service, appRepo repository.AppRepository, appVersionRepo repository.AppVersionRepository) AppVersionService {
	return &appVersion{
		Service:        service,
		appRepo:        appRepo,
		appVersionRepo: appVersionRepo,
	}
}

type appVersion struct {
	*Service
	appRepo        repository.AppRepository
	appVersionRepo repository.AppVersionRepository
}

func (slf *appVersion) CheckUpdate(ctx context.Context, req *v1.CheckAppUpdateRequest) (*v1.CheckAppUpdateResponseData, error) {
	app, err := slf.appRepo.FetchAppByAppKey(ctx, req.Application)
	if err != nil {
		return nil, err
	}
	if app == nil {
		return nil, nil
	}
	current, err := slf.appVersionRepo.FetchAppVersionByName(ctx, app.ID, req.VersionName, req.OsType)
	if err != nil {
		return nil, err
	}
	if current == nil {
		return nil, nil
	}
	next, err := slf.appVersionRepo.FetchNextVersion(ctx, app.ID, req.OsType, current.VersionCode, false)
	if err != nil {
		return nil, err
	}
	if next == nil {
		next, err = slf.appVersionRepo.FetchNextVersion(ctx, app.ID, req.OsType, current.VersionCode, true)
		if err != nil {
			return nil, err
		}
	}
	if next == nil {
		return nil, nil
	}
	return &v1.CheckAppUpdateResponseData{
		VersionCode:   next.VersionCode,
		VersionName:   next.VersionName,
		UpdateNote:    next.UpdateNote,
		Url:           next.Url,
		IsForceUpdate: next.IsForceUpdate,
		IsHotUpdate:   next.IsHotUpdate,
		CreatedAt:     next.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:     next.UpdatedAt.Format("2006-01-02 15:04:05"),
	}, nil
}

func (slf *appVersion) CreateAppVersion(ctx context.Context, req *v1.CreateAppVersionRequest) (*v1.CreateAppVersionResponseData, error) {
	var id int64
	if err := slf.tx.Transaction(ctx, func(ctx context.Context) error {
		if _, err := slf.appVersionRepo.FetchAppVersionByName(ctx, req.AppID, req.VersionName, req.OsType); err != nil {
			return v1.ErrAppVersionNameAlreadyTaken
		}
		lastVersion, err := slf.appVersionRepo.FetchLatestAppVersionByAppID(ctx, req.AppID, req.OsType)
		if err != nil {
			return err
		}
		code := 1
		if lastVersion != nil {
			code = lastVersion.VersionCode + 1
		}
		id, err = slf.appVersionRepo.CreateAppVersion(ctx, &model.AppVersion{
			AppID:         req.AppID,
			VersionCode:   code,
			VersionName:   req.VersionName,
			UpdateNote:    req.UpdateNote,
			OsType:        req.OsType,
			Url:           req.Url,
			IsForceUpdate: req.IsForceUpdate,
			Remark:        req.Remark,
			Status:        0, // 未发布
		})
		if err != nil {
			return err
		}
		return nil
	}); err != nil {
		return nil, err
	}
	return &v1.CreateAppVersionResponseData{ID: id}, nil
}

func (slf *appVersion) UpdateAppVersion(ctx context.Context, req *v1.UpdateAppVersionRequest) error {
	version, err := slf.appVersionRepo.FetchAppVersionByID(ctx, req.ID)
	if err != nil {
		return err
	}
	if version == nil {
		return v1.ErrAppVersionNotFound
	}
	if version.Status == 1 {
		return v1.ErrAppVersionIsPublished
	}
	if version.VersionName != req.VersionName {
		if _, err := slf.appVersionRepo.FetchAppVersionByName(ctx, version.AppID, req.VersionName, version.OsType); err != nil {
			return v1.ErrAppVersionNameAlreadyTaken
		}
	}
	version.VersionName = req.VersionName
	version.UpdateNote = req.UpdateNote
	version.Url = req.Url
	version.IsForceUpdate = req.IsForceUpdate
	version.IsHotUpdate = req.IsHotUpdate
	version.Remark = req.Remark
	if err := slf.appVersionRepo.UpdateAppVersion(ctx, version); err != nil {
		return err
	}
	return nil
}

func (slf *appVersion) PublishAppVersion(ctx context.Context, req *v1.PublishAppVersionRequest) error {
	version, err := slf.appVersionRepo.FetchAppVersionByID(ctx, req.ID)
	if err != nil {
		return err
	}
	if version == nil {
		return v1.ErrAppVersionNotFound
	}
	if version.Status == 1 {
		return v1.ErrAppVersionIsPublished
	}
	version.Status = 1
	if err := slf.appVersionRepo.UpdateAppVersion(ctx, version); err != nil {
		return err
	}
	return nil
}

func (slf *appVersion) RecallAppVersion(ctx context.Context, req *v1.RecallAppVersionRequest) error {
	version, err := slf.appVersionRepo.FetchAppVersionByID(ctx, req.ID)
	if err != nil {
		return err
	}
	if version == nil {
		return v1.ErrAppVersionNotFound
	}
	if version.Status == 0 {
		return v1.ErrAppVersionIsNotPublished
	}
	version.Status = 2
	if err := slf.appVersionRepo.UpdateAppVersion(ctx, version); err != nil {
		return err
	}
	return nil
}

func (slf *appVersion) PageListAppVersion(ctx context.Context, req *v1.PageListAppVersionRequest) (*v1.PageListAppVersionResponseData, error) {
	return slf.appVersionRepo.PageListAppVersion(ctx, req)
}
