package service

import (
	"context"
	"fmt"
	"time"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/repository"
)

type HistoryTodayService interface {
	GetHistoryDayEvents(ctx context.Context, req *v1.GetHistoryDayEventsRequest) (*v1.GetHistoryDayEventsResponseData, error)
}

func NewHistoryTodayService(
	service *Service,
	historyDayEventRepo repository.HistoryTodayRepository,
) HistoryTodayService {
	return &calendarHistoryService{
		historyDayEventRepo: historyDayEventRepo,
		Service:             service,
	}
}

type calendarHistoryService struct {
	historyDayEventRepo repository.HistoryTodayRepository
	*Service
}

func (slf *calendarHistoryService) GetHistoryDayEvents(ctx context.Context, req *v1.GetHistoryDayEventsRequest) (*v1.GetHistoryDayEventsResponseData, error) {
	date, err := time.Parse("2006-01-02", req.Date)
	if err != nil {
		return nil, err
	}
	month, day := date.Month(), date.Day()
	var data v1.GetHistoryDayEventsResponseData
	actions, err := slf.historyDayEventRepo.GetDayActions(ctx, int(month), day)
	if err != nil {
		return nil, err
	}
	for _, item := range actions {
		var date string
		if item.Year == "" {
			date = fmt.Sprintf("%02d-%02d", item.Day/100, item.Day%100)
		} else {
			date = fmt.Sprintf("%s-%02d-%02d", item.Year, item.Day/100, item.Day%100)
		}
		data.Actions = append(data.Actions, &v1.GetHistoryDayEventsResponseItem{
			Date:    date,
			AD:      item.Ad == 1,
			Keyword: item.Keyword,
			Type:    item.Type,
			Title:   item.Title,
			Content: item.Content,
		})
	}
	events, err := slf.historyDayEventRepo.GetDayEvents4AD(ctx, int(month), day)
	if err != nil {
		return nil, err
	}
	for _, item := range events {
		var date string
		if item.Year == "" {
			date = fmt.Sprintf("%02d-%02d", item.Day/100, item.Day%100)
		} else {
			date = fmt.Sprintf("%s-%02d-%02d", item.Year, item.Day/100, item.Day%100)
		}
		data.Events = append(data.Events, &v1.GetHistoryDayEventsResponseItem{
			Date:    date,
			AD:      item.Ad == 1,
			Keyword: item.Keyword,
			Type:    item.Type,
			Title:   item.Title,
			Content: item.Content,
		})
	}
	events, err = slf.historyDayEventRepo.GetDayEvents4BC(ctx, int(month), day)
	if err != nil {
		return nil, err
	}
	for _, item := range events {
		var date string
		if item.Year == "" {
			date = fmt.Sprintf("%02d-%02d", item.Day/100, item.Day%100)
		} else {
			date = fmt.Sprintf("%s-%02d-%02d", item.Year, item.Day/100, item.Day%100)
		}
		data.Events = append(data.Events, &v1.GetHistoryDayEventsResponseItem{
			Date:    date,
			AD:      item.Ad == 1,
			Keyword: item.Keyword,
			Type:    item.Type,
			Title:   item.Title,
			Content: item.Content,
		})
	}
	return &data, nil
}
