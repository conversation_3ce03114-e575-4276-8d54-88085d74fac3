package service

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"strings"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
	"zodiacus/internal/repository"
	"zodiacus/third_party/miniprogram"
)

type MiniProgramService interface {
	CreateQrCode(ctx context.Context, req *v1.MpQrCodeRequest) (*v1.MpQrCodeResponseData, error)
	PageListQrCode(ctx context.Context, req *v1.MpQrCodePageListRequest) (*v1.MpQrCodePageListResponseData, error)
}

func NewMiniProgramService(
	service *Service,
	mpMgr *miniprogram.Manager,
	miniProgramRepo repository.MinProgramRepository,
	appRepo repository.AppRepository,
	appChannelRepo repository.AppChannelRepository,
) MiniProgramService {
	return &miniProgramService{
		Service:         service,
		mpMgr:           mpMgr,
		miniProgramRepo: miniProgramRepo,
		appRepo:         appRepo,
		appChannelRepo:  appChannelRepo,
	}
}

type miniProgramService struct {
	*Service
	mpMgr           *miniprogram.Manager
	miniProgramRepo repository.MinProgramRepository
	appRepo         repository.AppRepository
	appChannelRepo  repository.AppChannelRepository
}

func (slf *miniProgramService) PageListQrCode(ctx context.Context, req *v1.MpQrCodePageListRequest) (*v1.MpQrCodePageListResponseData, error) {
	list, err := slf.miniProgramRepo.PageListQrCode(ctx, req)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (slf *miniProgramService) CreateQrCode(ctx context.Context, req *v1.MpQrCodeRequest) (*v1.MpQrCodeResponseData, error) {
	channel, err := slf.appChannelRepo.FetchAppChannelByID(ctx, req.ChannelID)
	if err != nil {
		return nil, err
	}
	if channel == nil {
		return nil, v1.ErrBadRequest
	}
	app, err := slf.appRepo.FetchAppByID(ctx, req.AppID)
	if err != nil {
		return nil, err
	}
	if app == nil {
		return nil, v1.ErrBadRequest
	}
	mp := slf.mpMgr.Get(app.App)
	if mp == nil {
		return nil, v1.ErrBadRequest
	}
	resp, err := mp.WXACode.GetUnlimited(ctx, channel.Code, req.Page, true, "release",
		430, false, nil, false)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	contentType := resp.Header.Get("Content-Type")
	if strings.Contains(contentType, "application/json") {
		var errResp struct {
			ErrCode int    `json:"errcode"`
			ErrMsg  string `json:"errmsg"`
		}
		if err := json.NewDecoder(resp.Body).Decode(&errResp); err != nil {
			return nil, fmt.Errorf("failed to parse error response: %w", err)
		}
		return nil, fmt.Errorf("wxacode error: code=%d, message=%s", errResp.ErrCode, errResp.ErrMsg)
	}
	rawImg, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read image data: %w", err)
	}
	if _, err = slf.miniProgramRepo.CreateQrCode(ctx, &model.MpQrcode{
		AppID:     req.AppID,
		SceneStr:  channel.Code,
		Remark:    channel.Remark,
		Page:      req.Page,
		ImageData: rawImg,
		ImageType: contentType,
	}); err != nil {
		return nil, err
	}
	return &v1.MpQrCodeResponseData{
		Data:        rawImg,
		ContentType: contentType,
	}, nil
}
