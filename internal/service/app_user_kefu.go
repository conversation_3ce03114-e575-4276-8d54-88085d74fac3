package service

import (
	"context"
	"fmt"
	contactWayRequest "github.com/ArtisanCloud/PowerWeChat/v3/src/work/externalContact/contactWay/request"
	"github.com/lithammer/shortuuid/v4"
	"github.com/pkg/errors"
	"net/url"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
	"zodiacus/internal/repository"
	"zodiacus/pkg/array"
	"zodiacus/third_party/wecom"
)

type AppUserKefuService interface {
	Qrcode(ctx context.Context, req *v1.QwKefuRequest) (*string, error)
	VipQrcode(ctx context.Context, req *v1.QwKefuVipRequest) (*string, error)
}

func NewAppUserKefuService(service *Service, qwRepo repository.QwRepository,
	appUserKefuRepo repository.AppUserKefuRepository,
	doraemon *wecom.Application,
) AppUserKefuService {
	return &kefuService{
		Service:         service,
		qwRepo:          qwRepo,
		appUserKefuRepo: appUserKefuRepo,
		doraemon:        doraemon,
	}
}

type kefuService struct {
	*Service
	qwRepo          repository.QwRepository
	appUserKefuRepo repository.AppUserKefuRepository
	doraemon        *wecom.Application
}

func (slf *kefuService) Qrcode(ctx context.Context, req *v1.QwKefuRequest) (*string, error) {
	var (
		qwIds  = []string{"XuanBuJiuFei", "tx"}
		hashed = array.Hashed(qwIds)
	)
	kefu, err := slf.appUserKefuRepo.FetchAppUserQwKefu(ctx, req.Auth.UserID, hashed)
	if err != nil {
		return nil, err
	}
	if kefu != nil {
		return &kefu.QwLink, nil
	} else {
		var (
			name     = "论玄客服"
			addState = "kefu_" + shortuuid.New()
		)
		qwResp, err := slf.doraemon.ExternalContactContactWay.Add(ctx, &contactWayRequest.RequestAddContactWay{
			Type:       2,        // 多人
			Scene:      2,        // 二维码
			Remark:     name,     // 名称
			SkipVerify: true,     // 是否跳过验证
			User:       qwIds,    // 员工ID
			State:      addState, // 添加参数
		})
		if err != nil {
			return nil, err
		}
		if qwResp.ErrCode != 0 {
			return nil, errors.Errorf("创建联系我失败：code=%d, msg=%s", qwResp.ErrCode, qwResp.ErrMsg)
		}
		contactWayID, err := slf.qwRepo.CreateContactWay(ctx, &model.QwContactWay{
			Type:       1,
			AppID:      2,
			PlatformID: 2,
			Name:       name,
			Link:       qwResp.QRCode,
			UserIDs:    qwIds,
			ConfigID:   qwResp.ConfigID,
			SkipVerify: true,
			AddState:   addState,
		})
		if err != nil {
			return nil, err
		}
		kefu = &model.AppUserQwKefu{
			UserID:          req.Auth.UserID,
			AddState:        addState,
			Type:            1,
			QwContactWayID:  contactWayID,
			QwUserIDs:       qwIds,
			QwUserIDsHashed: hashed,
			QwLink:          qwResp.QRCode,
		}
		if _, err = slf.appUserKefuRepo.CreateAppUserQwKefu(ctx, kefu); err != nil {
			return nil, err
		}
		return &kefu.QwLink, nil
	}
}

func (slf *kefuService) VipQrcode(ctx context.Context, req *v1.QwKefuVipRequest) (*string, error) {
	var (
		contactWayID = int64(838) // 固定值
		qwLink       = "https://work.weixin.qq.com/ca/cawcde044b062d42e9"
		qwIds        = []string{"WuKeShuDeSenLin", "SunXianSheng", "XuanBuJiuFei", "tx"}
	)
	kefu, err := slf.appUserKefuRepo.FetchQwKefuVip(ctx, req.Auth.UserID, contactWayID)
	if err != nil {
		return nil, err
	}
	if kefu != nil {
		link := fmt.Sprintf("weixin://biz/ww/profile/%s", url.QueryEscape(kefu.QwLink))
		return &link, nil
	} else {
		var (
			addState = "kefu_" + shortuuid.New()
			link     = fmt.Sprintf("%s?customer_channel=%s", qwLink, addState)
		)
		kefu = &model.AppUserQwKefu{
			UserID:          req.Auth.UserID,
			AddState:        addState,
			Type:            2,
			QwContactWayID:  contactWayID,
			QwUserIDs:       qwIds,
			QwUserIDsHashed: array.Hashed(qwIds),
			QwLink:          link,
		}
		if _, err = slf.appUserKefuRepo.CreateAppUserQwKefu(ctx, kefu); err != nil {
			return nil, err
		}
		link = fmt.Sprintf("weixin://biz/ww/profile/%s", url.QueryEscape(kefu.QwLink))
		return &link, nil
	}
}
