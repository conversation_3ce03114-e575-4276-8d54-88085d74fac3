package service

import (
	"context"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/repository"
)

type TermService interface {
	QueryTermByName(ctx context.Context, request *v1.QueryTermRequest) (*v1.QueryTermResponseData, error)
}

func NewTermService(
	service *Service,
	termRepo repository.TermRepository,
) TermService {
	return &termService{
		termRepo: termRepo,
		Service:  service,
	}
}

type termService struct {
	termRepo repository.TermRepository
	*Service
}

func (slf *termService) QueryTermByName(ctx context.Context, req *v1.QueryTermRequest) (*v1.QueryTermResponseData, error) {
	term, err := slf.termRepo.GetTermByName(ctx, req.Name)
	if err != nil {
		return nil, err
	}
	if term == nil {
		return nil, nil
	}
	return &v1.QueryTermResponseData{
		Name:        term.Name,
		Category1:   term.Category1,
		Description: term.Description,
	}, nil
}
