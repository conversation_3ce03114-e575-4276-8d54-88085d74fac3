package service

import (
	"context"
	"fmt"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
	"zodiacus/internal/repository"
	"zodiacus/internal/types"
	"zodiacus/pkg/array"
	"zodiacus/pkg/strs"
	"zodiacus/third_party/corona"
)

/*
 - 食伤：日干五行所生
 - 财才：日干五行所克
 - 比劫：日干五行
 - 印枭：生日干五行
 - 官杀：克制日干五行
*/

var (
	comb23 = func(arr []string, targets ...string) ([][]string, [][]string) {
		var uniqueComb = func(combos [][]string) [][]string {
			seen := make(map[string]struct{})
			var tmp [][]string
			for _, combo := range combos {
				sorted := append([]string{}, combo...)
				sort.Strings(sorted)
				key := strings.Join(sorted, "|")
				if _, exists := seen[key]; !exists {
					seen[key] = struct{}{}
					tmp = append(tmp, combo)
				}
			}
			return tmp
		}
		arr = array.Unique(arr)
		var twoCombos [][]string
		var threeCombos [][]string
		n := len(arr)
		var actualTargets []string
		if len(targets) == 0 {
			seen := make(map[string]bool)
			for _, v := range arr {
				if !seen[v] {
					actualTargets = append(actualTargets, v)
					seen[v] = true
				}
			}
		} else {
			actualTargets = targets
		}

		for _, tgt := range actualTargets {
			// two-combos
			for j := 0; j < n; j++ {
				/*
					if arr[j] != tgt {
						twoCombos = append(twoCombos, []string{tgt, arr[j]})
					}
				*/
				twoCombos = append(twoCombos, []string{tgt, arr[j]}) // 自刑地支相同
			}
			// three-combos
			for j := 0; j < n; j++ {
				for k := j + 1; k < n; k++ {
					if arr[j] != arr[k] && arr[j] != tgt && arr[k] != tgt {
						threeCombos = append(threeCombos, []string{tgt, arr[j], arr[k]})
					}
				}
			}
		}
		return uniqueComb(twoCombos), uniqueComb(threeCombos)
	}
)

type MasterService interface {
	Master(ctx context.Context, req *v1.MasterRequest) (*v1.MasterResponseData, error)
	GeJuBianGe(ctx context.Context, req *v1.GeJuBianGeRequest) (*v1.GeJuBianGeResponseData, error)
	XiyongWangshuai(ctx context.Context, req *v1.MasterXiyongWangshuaiRequest) (*v1.MasterXiyongWangshuaiResponseData, error)
	DayunLiunian(ctx context.Context, req *v1.DayunliunianRequest) (*v1.DayunliunianResponseData, error)
	DayunAnalysis(ctx context.Context, req *v1.DayunAnalysisRequest) (*v1.DayunAnalysisResponseData, error)
	LiunianAnalysis(ctx context.Context, req *v1.LiunianAnalysisRequest) (*v1.LiunianAnalysisResponseData, error)
	Hepan(ctx context.Context, req *v1.HepanRequest) (*v1.HepanResponseData, error)
	GaoKao(ctx context.Context, req *v1.GaoKaoRequest) (*v1.GaoKaoResponseData, error)
	GaoKaoPreference(ctx context.Context, req *v1.GaoKaoPreferenceRequest) (*v1.GaoKaoPreferenceResponseData, error)
	PageListHepan(ctx context.Context, req *v1.PageListHepanRequest) (*v1.PageListHepanResponseData, error)
	DeleteHepan(ctx context.Context, req *v1.DeleteHepanRequest) error
	ViewHepan(ctx context.Context, req *v1.ViewHepanRequest) (*v1.ViewHepanResponseData, error)
}

func NewMasterService(
	service *Service,
	masterRepo repository.MasterRepository,
	paipanRecordRepo repository.UserPaipanRecordRepository,
	hepanRecordRepo repository.UserHepanRecordRepository,
	luncaiRepo repository.LuncaiRepository,
	userMingliRepo repository.UserMingliRepository,
	mingliRuleRepo repository.MingliRuleRepository,
	mingliRuleConditionRepo repository.MingliRuleConditionRepository,
	enumsRepo repository.EnumsRepository,
	dateRepo repository.DateRepository,
) MasterService {
	return &masterService{
		Service:                 service,
		masterRepo:              masterRepo,
		paipanRecordRepo:        paipanRecordRepo,
		hepanRecordRepo:         hepanRecordRepo,
		luncaiRepo:              luncaiRepo,
		userMingliRepo:          userMingliRepo,
		mingliRuleRepo:          mingliRuleRepo,
		mingliRuleConditionRepo: mingliRuleConditionRepo,
		enumsRepo:               enumsRepo,
		dateRepo:                dateRepo,
		/*
			喜用	方位	季节	颜色	数字	居住地	饮食	药物
			水	北方	冬季	黑色	1、6	寒冷之地及江河湖泊海洋之地居住	寒性食物及肉食动物的肾膀胱和各种鱼类	寒性之药物
			木	东方	春季	绿色	3、8	温带区域，多树木花草之地居住	温性食物，食肉食动物的肝胆	温性之药物
			火	南方	夏季	红色	2、7	热带，大陆性区域及煤矿，电厂之地居住	热性食物，食肉食动物的小肠，心肝	热性之药物
			土	中央	四季	黄色	5、0	寒带适中之地及高山，平原之地居住	中性食物记及肉食动物的肺胃等	中性之药物
			金	西方	秋季	白色	4、9	凉性区域及多金属，矿产之地居住	凉性食物及肉食动物的肺大肠	凉性之药物
		*/
		wuxingEffects: map[string]*types.WuxingEffect{
			"水": {
				Fangwei: "北方",
				Jijie:   "冬季",
				Yanse:   "黑色",
				Shuzi:   "1、6",
				Juzhudi: "寒冷之地及江河湖泊海洋之地居住",
				Yinshi:  "寒性食物及肉食动物的肾膀胱和各种鱼类",
				Yaowu:   "寒性之药物",
			},
			"木": {
				Fangwei: "东方",
				Jijie:   "春季",
				Yanse:   "绿色",
				Shuzi:   "3、8",
				Juzhudi: "温带区域，多树木花草之地居住",
				Yinshi:  "温性食物，食肉食动物的肝胆",
				Yaowu:   "温性之药物",
			},
			"火": {
				Fangwei: "南方",
				Jijie:   "夏季",
				Yanse:   "红色",
				Shuzi:   "2、7",
				Juzhudi: "热带，大陆性区域及煤矿，电厂之地居住",
				Yinshi:  "热性食物，食肉食动物的小肠，心肝",
				Yaowu:   "热性之药物",
			},
			"土": {
				Fangwei: "中央",
				Jijie:   "四季",
				Yanse:   "黄色",
				Shuzi:   "5、0",
				Juzhudi: "寒带适中之地及高山，平原之地居住",
				Yinshi:  "中性食物记及肉食动物的肺胃等",
				Yaowu:   "中性之药物",
			},
			"金": {
				Fangwei: "西方",
				Jijie:   "秋季",
				Yanse:   "白色",
				Shuzi:   "4、9",
				Juzhudi: "凉性区域及多金属，矿产之地居住",
				Yinshi:  "凉性食物及肉食动物的肺大肠",
				Yaowu:   "凉性之药物",
			},
		},
		/*
			甲、己	丑、未
			乙、庚	子、申
			丙、辛	酉、亥
			丁、壬	申、戌
			戊、癸	卯、巳
		*/
		tianganTianyi: map[string]*types.TianyiGuiren{
			"甲": {"甲", []string{"丑", "未"}, []string{"牛", "羊"}},
			"乙": {"乙", []string{"子", "申"}, []string{"鼠", "猴"}},
			"丙": {"丙", []string{"亥", "酉"}, []string{"猪", "鸡"}},
			"丁": {"丁", []string{"酉", "亥"}, []string{"鸡", "猪"}},
			"戊": {"戊", []string{"丑", "未"}, []string{"牛", "羊"}},
			"己": {"己", []string{"子", "申"}, []string{"鼠", "猴"}},
			"庚": {"庚", []string{"未", "丑"}, []string{"羊", "牛"}},
			"辛": {"辛", []string{"寅", "午"}, []string{"虎", "马"}},
			"壬": {"壬", []string{"卯", "巳"}, []string{"兔", "蛇"}},
			"癸": {"癸", []string{"卯", "巳"}, []string{"兔", "蛇"}},
		},
		/*
			日支	时支	结果
			寅	申	水浸
			申	子	虎咬
			子	午	吃毒药
			酉	卯	被杀
			戌	辰	见天亡
			巳	亥	吊颈
			丑	未	独自亡
			辰	戌	冷作死
		*/
		shangcanDuan: map[string]string{
			"寅申": "水浸",
			"申子": "虎咬",
			"子午": "吃毒药",
			"酉卯": "被杀",
			"戌辰": "见天亡",
			"巳亥": "吊颈",
			"丑未": "独自亡",
			"辰戌": "冷作死",
		},
		shishenNumMap: map[string][]string{
			"正官": {"一官是官", "二官是狭", "三官是鬼", "四官是难", "五官牢刑难免", "六官死于非命", "七官从势反为福贵"},
			"七杀": {"一杀是官", "二杀恶权", "三杀鬼狱", "四杀伤残", "五杀短命孤雁", "六杀儿女无传", "七杀丰厚反到王前"},
			"正印": {"一印是权", "二印椿萱", "三印少决", "四印伤残", "五印埋儿断后", "六印懒惰偷馋", "七印势极反到佛前"},
			"偏印": {"一枭爹娘", "二枭多伤", "三枭换祖", "四枭凄凉", "五枭谋多成少", "六枭算进牢房", "七枭势成反坐高堂"},
			"比肩": {"一比仁义", "二比和气", "三比争夺", "四比乏利", "五比离乡背井", "六比贫困无疑", "七比势强反成名利"},
			"劫财": {"一劫情谊", "二劫克妻", "三劫损财", "四劫牢狱", "五劫伤人害命", "六劫尸骨分离", "七劫从恶名霸一方"},
			"食神": {"一食才子", "二食吃吃", "三食愚肿", "四食弱智", "五食败业贪欢", "六食妨祖短世", "七食花红反到治世"},
			"伤官": {"一伤生财", "二伤艺卖", "三伤官刑", "四伤破败", "五伤孤残无助", "六伤命挂泉台", "七伤泄尽精明得志"},
			"正财": {"一财是财", "二财是妾", "三财是色", "四财是贪", "五财聚散成灾", "六财身弱当贫", "七财从势金玉满堂"},
			"偏财": {"一妾娇花", "二妾财源", "三妾贪欢", "四妾孤寒", "五妾买身败业", "六妾刑狱牵连", "七妾银海金山"},
		},
	}
}

type masterService struct {
	*Service
	masterRepo              repository.MasterRepository
	paipanRecordRepo        repository.UserPaipanRecordRepository
	hepanRecordRepo         repository.UserHepanRecordRepository
	luncaiRepo              repository.LuncaiRepository
	mingliRuleRepo          repository.MingliRuleRepository
	userMingliRepo          repository.UserMingliRepository
	mingliRuleConditionRepo repository.MingliRuleConditionRepository
	enumsRepo               repository.EnumsRepository
	dateRepo                repository.DateRepository
	wuxingEffects           map[string]*types.WuxingEffect
	tianganTianyi           map[string]*types.TianyiGuiren // 天乙贵人
	shangcanDuan            map[string]string              // 伤残断
	shishenNumMap           map[string][]string            // 十神数量
}

func (slf *masterService) ViewHepan(ctx context.Context, req *v1.ViewHepanRequest) (*v1.ViewHepanResponseData, error) {
	record, err := slf.hepanRecordRepo.GetHepanRecordByID(ctx, req.User.UserID, req.ID)
	if err != nil {
		return nil, err
	}
	if record == nil {
		return nil, v1.ErrBadRequest
	}
	return slf.hepan(ctx, req.User.UserID, record.MingliA(), record.MingliB(), true)
}

func (slf *masterService) DeleteHepan(ctx context.Context, req *v1.DeleteHepanRequest) error {
	return slf.hepanRecordRepo.DeleteHepanRecord(ctx, req)
}

func (slf *masterService) PageListHepan(ctx context.Context, req *v1.PageListHepanRequest) (*v1.PageListHepanResponseData, error) {
	return slf.hepanRecordRepo.PageListHepanRecord(ctx, req)
}

func (slf *masterService) Hepan(ctx context.Context, req *v1.HepanRequest) (*v1.HepanResponseData, error) {
	mlA, err := slf.userMingliRepo.GetMingliByID(ctx, req.User.UserID, req.MingliIdA)
	mlB, err := slf.userMingliRepo.GetMingliByID(ctx, req.User.UserID, req.MingliIdB)
	if err != nil {
		return nil, err
	}
	if mlA == nil || mlB == nil {
		return nil, v1.ErrUserMingliNotFound
	}
	return slf.hepan(ctx, req.User.UserID, mlA, mlB)
}

func (slf *masterService) hepan(ctx context.Context, userId string, mlA, mlB *model.UserMingli, ignoreRecord ...bool) (*v1.HepanResponseData, error) {
	var (
		resp     v1.HepanResponseData
		suntimeA = mlA.BirthtimeSun.Format("2006-01-02 15:04:05")
		suntimeB = mlB.BirthtimeSun.Format("2006-01-02 15:04:05")
		genderA  = lo.Ternary(mlA.Gender == 1, "男", "女")
		genderB  = lo.Ternary(mlB.Gender == 1, "男", "女")
	)
	allA, err := slf.coronaCli.GetAll(ctx, &corona.GetAllRequest{
		Birthtime: suntimeA,
		Gender:    genderA,
	})
	if err != nil {
		return nil, errors.Wrap(err, "paipan.PaipanAll")
	}
	allB, err := slf.coronaCli.GetAll(ctx, &corona.GetAllRequest{
		Birthtime: suntimeB,
		Gender:    genderB,
	})
	if err != nil {
		return nil, errors.Wrap(err, "paipan.PaipanAll")
	}
	resp.Points = slf.HepanPoints(ctx, allA, allB)
	resp.Wuxing = slf.HepanWuxing(ctx, allA, allB)
	resp.Sizhu, err = slf.HepanSizhu(ctx, mlA, mlB, allA, allB)
	if err != nil {
		return nil, errors.Wrap(err, "HepanSizhu")
	}

	if len(ignoreRecord) == 0 && ignoreRecord[0] {
		return &resp, nil
	}
	if _, err = slf.hepanRecordRepo.CreateHepanRecord(ctx, &model.HepanRecord{
		UserID:          userId,
		MingliIdA:       mlA.ID,
		MingliIdB:       mlB.ID,
		NameA:           mlA.Name,
		GenderA:         mlA.Gender,
		BirthtimeA:      mlA.Birthtime,
		BirthtimeSunA:   mlA.BirthtimeSun,
		BirthtimeLunarA: mlA.BirthtimeLunar,
		BaziA:           mlA.Bazi,
		BirthplaceA:     mlA.Birthplace,
		NameB:           mlB.Name,
		GenderB:         mlB.Gender,
		BirthtimeB:      mlB.Birthtime,
		BirthtimeSunB:   mlB.BirthtimeSun,
		BirthtimeLunarB: mlB.BirthtimeLunar,
		BaziB:           mlB.Bazi,
		BirthplaceB:     mlB.Birthplace,
	}); err != nil {
		return nil, err
	}
	return &resp, nil
}

func (slf *masterService) HepanSizhu(ctx context.Context, mlA, mlB *model.UserMingli, allA, allB *corona.GetAllResponse) (*v1.HepanResponseDataSizhu, error) {
	var res v1.HepanResponseDataSizhu
	var fnTianganRelation = func(tg1, tg2 string) (arr []string) {
		var (
			wx1 = slf.GetWuxingByTiangan(tg1)
			wx2 = slf.GetWuxingByTiangan(tg2)
		)
		if _, ok := slf.IsTianganWuhe(tg1, tg2); ok {
			arr = append(arr, "五合")
		}
		if wx1 == wx2 || wx1 == slf.TblWuxingXiangsheng[wx2] || wx2 == slf.TblWuxingXiangsheng[wx1] {
			arr = append(arr, "相助")
		}
		if slf.IsTianganXiangke(tg1, tg2) {
			arr = append(arr, "相克")
		}
		return arr
	}
	var fnDizhiRelation = func(dz1, dz2 string) (arr []string) {
		var (
			wx1 = slf.GetWuxingByDizhi(dz1)
			wx2 = slf.GetWuxingByDizhi(dz2)
		)
		if _, ok := slf.IsDizhiLiuhe(dz1, dz2); ok {
			arr = append(arr, "六合")
		}
		if ok := slf.InDizhiSanhe(dz1, dz2); ok {
			arr = append(arr, "三合")
		}
		if slf.IsDizhiXiangxing(dz1, dz2) {
			arr = append(arr, "相刑")
		}
		if wx1 == wx2 || wx1 == slf.TblWuxingXiangsheng[wx2] || wx2 == slf.TblWuxingXiangsheng[wx1] {
			arr = append(arr, "相助")
		}
		if slf.TblWuxingXiangke[wx1] == wx2 || slf.TblWuxingXiangke[wx2] == wx1 {
			arr = append(arr, "相克")
		}
		return arr
	}
	var fnFindWX = func(str string) string {
		wuxing := []rune{'金', '木', '水', '火', '土'}
		for _, char := range str {
			for _, w := range wuxing {
				if char == w {
					return string(char)
				}
			}
		}
		return ""
	}
	var fnNayinRelation = func(ny1, ny2 string) string {
		var (
			wx1 = fnFindWX(ny1)
			wx2 = fnFindWX(ny2)
		)
		if wx1 == wx2 {
			return "相同"
		}
		if slf.IsWuxingXiangsheng(wx1, wx2) || slf.IsWuxingXiangsheng(wx2, wx1) {
			return "相生"
		}
		if slf.IsWuxingXiangke(wx1, wx2) || slf.IsWuxingXiangke(wx2, wx1) {
			return "相克"
		}
		return ""
	}
	res.Ganzhi = []*v1.HepanResponseDataSizhuGanzhiItem{
		{A: allA.Tiangan[0], B: allB.Tiangan[0], C: fnTianganRelation(allA.Tiangan[0], allB.Tiangan[0])},
		{A: allA.Dizhi[0], B: allB.Dizhi[0], C: fnDizhiRelation(allA.Dizhi[0], allB.Dizhi[0])},
		{A: allA.Tiangan[1], B: allB.Tiangan[1], C: fnTianganRelation(allA.Tiangan[1], allB.Tiangan[1])},
		{A: allA.Dizhi[1], B: allB.Dizhi[1], C: fnDizhiRelation(allA.Dizhi[1], allB.Dizhi[1])},
		{A: allA.Tiangan[2], B: allB.Tiangan[2], C: fnTianganRelation(allA.Tiangan[2], allB.Tiangan[2])},
		{A: allA.Dizhi[2], B: allB.Dizhi[2], C: fnDizhiRelation(allA.Dizhi[2], allB.Dizhi[2])},
		{A: allA.Tiangan[3], B: allB.Tiangan[3], C: fnTianganRelation(allA.Tiangan[3], allB.Tiangan[3])},
		{A: allA.Dizhi[3], B: allB.Dizhi[3], C: fnDizhiRelation(allA.Dizhi[3], allB.Dizhi[3])},
	}
	res.Nayin = []*v1.HepanResponseDataSizhuNayinItem{
		{A: allA.Nayin[0], B: allB.Nayin[0], C: fnNayinRelation(allA.Nayin[0], allB.Nayin[0])},
		{A: allA.Nayin[2], B: allB.Nayin[2], C: fnNayinRelation(allA.Nayin[2], allB.Nayin[2])},
	}
	ssKeys1, err := slf.hepanShenshaRules(ctx, allA, allB, mlA.Name, mlB.Name, lo.Ternary(mlA.Gender == 1, "男", "女"))
	if err != nil {
		return nil, err
	}
	ssKeys2, err := slf.hepanShenshaRules(ctx, allB, allA, mlB.Name, mlA.Name, lo.Ternary(mlB.Gender == 1, "男", "女"))
	if err != nil {
		return nil, err
	}
	res.Shensha = &v1.HepanResponseDataShensha{
		NianZhuA:  allA.Shensha4[0],
		NianZhuB:  allB.Shensha4[0],
		RiZhuA:    allA.Shensha4[2],
		RiZhuB:    allB.Shensha4[2],
		Relations: array.Merge(ssKeys1, ssKeys2),
	}
	return &res, nil
}

func (slf *masterService) hepanShenshaRules(ctx context.Context, allA, allB *corona.GetAllResponse, nameA, nameB, gender string) ([]string, error) {
	var (
		baziArr1 = allA.Sizhu
		dizhi1   = allA.Dizhi
		baziArr2 = allB.Sizhu
		tiangan2 = allB.Tiangan
		dizhi2   = allB.Dizhi
		keys     = make(map[string]struct{})
		keysFn   = func(arr []string, idx int, tmp map[string]struct{}) {
			for _, str := range arr {
				switch str {
				case "天乙贵人", "天厨贵人", "灾煞", "桃花":
					tmp[fmt.Sprintf("%s-%s", str, dizhi2[idx])] = struct{}{}
				case "月德贵人":
					tmp[fmt.Sprintf("%s-%s", str, tiangan2[idx])] = struct{}{}
				case "天德贵人":
					if array.Has([]string{"寅", "辰", "巳", "未", "申", "戌", "亥", "丑"}, dizhi1[1]) {
						tmp[fmt.Sprintf("%s-%s", str, tiangan2[idx])] = struct{}{}
					}
					if array.Has([]string{"子", "卯", "午", "酉"}, dizhi1[1]) {
						tmp[fmt.Sprintf("%s-%s", str, dizhi2[idx])] = struct{}{}
					}
				}
			}
		}
		ssYuyi = map[string]string{
			"天乙贵人": "催旺贵人运，主贵人多助，荣昌有望，危难隐避",
			"天德贵人": "催旺贵人运，主一生吉利，化险为夷，不犯危难。",
			"月德贵人": "催旺贵人运，主祥和清贵、逢凶化吉，万事呈祥。",
			"天厨贵人": "催旺口福，提助生活幸福指数，平安吉顺。",
			"灾煞":   "主突发冲击，多有障碍。",
			"桃花":   "催旺桃花运，主异性缘旺。",
		}
	)
	ss1, err := slf.coronaCli.Shensha(ctx, &corona.GetShenshaRequest{
		Bazi:   append(baziArr1, baziArr2...),
		Gender: gender,
	})
	if err != nil {
		return nil, errors.Wrap(err, "corona.Shensha")
	}
	for i := 0; i < 4; i++ {
		keysFn(ss1.GetByIndex(4+i), i, keys)
	}
	var res []string
	for s, _ := range keys {
		split := strings.Split(s, "-")
		res = append(res, fmt.Sprintf("【%s】的%s，是【%s】的%s\n%s", nameB, split[1], nameA, split[0], ssYuyi[split[0]]))
	}
	return res, nil
}

func (slf *masterService) HepanWuxing(ctx context.Context, allA, allB *corona.GetAllResponse) *v1.HepanResponseDataWuxing {
	return &v1.HepanResponseDataWuxing{
		A: allA.ShishenPower,
		B: allB.ShishenPower,
	}
}

func (slf *masterService) HepanPoints(ctx context.Context, allA, allB *corona.GetAllResponse) *v1.HepanResponseDataPoints {
	var points v1.HepanResponseDataPoints
	var (
		nianzhiA   = allA.Dizhi[0]
		nianzhiB   = allB.Dizhi[0]
		shengxiaoA = allA.Shengxiao
		shengxiaoB = allB.Shengxiao
		riganA     = allA.Tiangan[2]
		riganB     = allB.Tiangan[2]
		riganWxA   = slf.GetWuxingByTiangan(riganA)
		riganWxB   = slf.GetWuxingByTiangan(riganB)
		rizhiA     = allA.Dizhi[2]
		rizhiB     = allB.Dizhi[2]
		rizhiWxA   = slf.GetWuxingByDizhi(rizhiA)
		rizhiWxB   = slf.GetWuxingByDizhi(rizhiB)
		riyuanA    = allA.Riyuan
		riyuanB    = allB.Riyuan
		gejuA      = allA.Geju
		gejuB      = allB.Geju
		biangeA    = allA.RiyuanBiange
		biangeB    = allB.RiyuanBiange
	)
	// 生肖
	{
		points.Shengxiao = &v1.HepanResponseDataPoint{
			A: []string{fmt.Sprintf("%s%s", nianzhiA, shengxiaoA)},
			B: []string{fmt.Sprintf("%s%s", nianzhiB, shengxiaoB)},
			C: func() string {
				if _, ok := slf.IsDizhiLiuhe(nianzhiA, nianzhiB); ok {
					return "六合"
				}
				if slf.InDizhiSanhe(nianzhiA, nianzhiB) {
					return "三合"
				}
				if ok := slf.IsDizhiXiangchong(nianzhiA, nianzhiB); ok {
					return "相冲"
				}
				return ""
			}(),
		}
	}
	// 日元
	{
		points.Riyuan = &v1.HepanResponseDataPoint{
			A: []string{fmt.Sprintf("%s%s", riganA, riganWxA)},
			B: []string{fmt.Sprintf("%s%s", riganB, riganWxB)},
			C: func() string {
				if riganWxA == riganWxB {
					return "相同"
				}
				if slf.IsWuxingXiangsheng(riganWxA, riganWxB) || slf.IsWuxingXiangsheng(riganWxB, riganWxA) {
					return "相生"
				}
				if slf.IsWuxingXiangke(riganWxA, riganWxB) || slf.IsWuxingXiangke(riganWxB, riganWxA) {
					return "相克"
				}
				return ""
			}(),
		}
	}
	// 夫妻宫
	{
		points.Fuqigong = &v1.HepanResponseDataPoint{
			A: []string{fmt.Sprintf("%s%s", rizhiA, rizhiWxA)},
			B: []string{fmt.Sprintf("%s%s", rizhiB, rizhiWxB)},
			C: func() string {
				if rizhiWxA == rizhiWxB {
					return "相同"
				}
				if slf.IsWuxingXiangsheng(rizhiWxA, rizhiWxB) || slf.IsWuxingXiangsheng(rizhiWxB, rizhiWxA) {
					return "相生"
				}
				if slf.IsWuxingXiangke(rizhiWxA, rizhiWxB) || slf.IsWuxingXiangke(rizhiWxB, rizhiWxA) {
					return "相克"
				}
				return ""
			}(),
		}
	}
	// 旺衰
	{
		points.Wangshuai = &v1.HepanResponseDataPoint{
			A: []string{riyuanA},
			B: []string{riyuanB},
		}
	}
	// 格局
	{
		points.Geju = &v1.HepanResponseDataPoint{
			A: []string{gejuA, biangeA},
			B: []string{gejuB, biangeB},
		}
	}
	// 五行完缺
	{
		fn := func(all *corona.GetAllResponse) (res []string) {
			for _, wx := range []string{"木", "火", "土", "金", "水"} {
				if all.WuxingPowerMap[wx] == 0 {
					res = append(res, fmt.Sprintf("缺%s", wx))
				}
			}
			if len(res) == 0 {
				res = append(res, "俱全")
			}
			return res
		}
		points.WuxingWanque = &v1.HepanResponseDataPoint{
			A: fn(allA),
			B: fn(allB),
		}
	}
	// 五行最旺
	{
		fn := func(all *corona.GetAllResponse) (res []string) {
			var (
				tmp   = make(map[int][]string)
				power int
			)
			for _, wx := range []string{"木", "火", "土", "金", "水"} {
				if all.WuxingPowerMap[wx] > power {
					power = all.WuxingPowerMap[wx]
				}
				tmp[all.WuxingPowerMap[wx]] = append(tmp[all.WuxingPowerMap[wx]], wx)
			}
			for _, wx := range tmp[power] {
				res = append(res, fmt.Sprintf("%s(%s)", wx, all.WuxingShishenMap[wx]))
			}
			return res
		}
		points.WuxingZuiwang = &v1.HepanResponseDataPoint{
			A: fn(allA),
			B: fn(allB),
		}
	}
	// 五行最弱
	{
		fn := func(all *corona.GetAllResponse) (res []string) {
			var (
				tmp   = make(map[int][]string)
				power = all.WuxingPowerMap["木"]
			)
			for _, wx := range []string{"木", "火", "土", "金", "水"} {
				if all.WuxingPowerMap[wx] < power {
					power = all.WuxingPowerMap[wx]
				}
				tmp[all.WuxingPowerMap[wx]] = append(tmp[all.WuxingPowerMap[wx]], wx)
			}
			for _, wx := range tmp[power] {
				res = append(res, fmt.Sprintf("%s(%s)", wx, all.WuxingShishenMap[wx]))
			}
			return res
		}
		points.WuxingZuiruo = &v1.HepanResponseDataPoint{
			A: fn(allA),
			B: fn(allB),
		}
	}
	// 五行喜用
	{
		points.WuxingXiyong = &v1.HepanResponseDataPoint{
			A: []string{
				fmt.Sprintf("%s(%s)", allA.YXCJX[1], allA.WuxingShishenMap[allA.YXCJX[1]]),
				fmt.Sprintf("%s(%s)", allA.YXCJX[0], allA.WuxingShishenMap[allA.YXCJX[0]]),
			},
			B: []string{
				fmt.Sprintf("%s(%s)", allB.YXCJX[1], allB.WuxingShishenMap[allB.YXCJX[1]]),
				fmt.Sprintf("%s(%s)", allB.YXCJX[0], allB.WuxingShishenMap[allB.YXCJX[0]]),
			},
		}
	}
	// 命卦
	{
		points.Mingua = &v1.HepanResponseDataPoint{
			A: []string{allA.Minggua},
			B: []string{allB.Minggua},
			C: func() string {
				var guamatch = map[string]string{
					"坎离": "五星",
					"离坎": "五星",
					"震巽": "五星",
					"巽震": "五星",
					"乾坤": "五星",
					"坤乾": "五星",
					"艮兑": "五星",
					"兑艮": "五星",
					"坎巽": "四星半",
					"巽坎": "四星半",
					"震离": "四星半",
					"离震": "四星半",
					"乾兑": "四星半",
					"兑乾": "四星半",
					"坤艮": "四星半",
					"艮坤": "四星半",
					"坎震": "四星",
					"震坎": "四星",
					"巽离": "四星",
					"离巽": "四星",
					"乾艮": "四星",
					"艮乾": "四星",
					"坤兑": "四星",
					"兑坤": "四星",
					"坎坎": "三星半",
					"坎乾": "三星",
					"乾坎": "三星",
					"震艮": "三星",
					"艮震": "三星",
					"巽兑": "三星",
					"兑巽": "三星",
					"离坤": "三星",
					"坤离": "三星",
					"坎兑": "二星半",
					"兑坎": "二星半",
					"震坤": "二星半",
					"坤震": "二星半",
					"巽乾": "二星半",
					"乾巽": "二星半",
					"离艮": "二星半",
					"艮离": "二星半",
					"坎艮": "二星",
					"巽坤": "二星",
					"坎坤": "一星半",
					"坤坎": "一星半",
					"震兑": "一星半",
					"兑震": "一星半",
					"巽艮": "一星半",
					"艮巽": "一星半",
					"离乾": "一星半",
					"乾离": "一星半",
				}
				guaA := string([]rune(allA.Minggua)[0])
				guaB := string([]rune(allB.Minggua)[0])
				return guamatch[guaA+guaB]
			}(),
		}
	}
	// 星宿
	{
		points.Xingxiu = &v1.HepanResponseDataPoint{
			A: []string{allA.Xingxiu},
			B: []string{allB.Xingxiu},
		}
	}
	return &points
}

func (slf *masterService) LiunianAnalysis(ctx context.Context, req *v1.LiunianAnalysisRequest) (*v1.LiunianAnalysisResponseData, error) {
	if req.Birthplace == nil {
		req.Birthplace = []string{}
	}
	realSunTimeStr, err := slf.realSunTime(ctx, req.Birthtime, req.Birthplace)
	if err != nil {
		return nil, errors.Wrap(err, "realSunTime")
	}
	gender := lo.Ternary(req.Gender == 1, "男", "女")
	// 基础信息
	paipanAll, err := slf.coronaCli.GetAll(ctx, &corona.GetAllRequest{
		Birthtime: realSunTimeStr,
		Gender:    gender,
	})
	if err != nil {
		return nil, errors.Wrap(err, "paipan.PaipanAll")
	}
	// 评分
	paipanLn, err := slf.coronaCli.GetDayunLiunianScore(ctx, &corona.GetDayunLiunianScoreRequest{
		Birthtime: realSunTimeStr,
		Gender:    gender,
	})
	if err != nil {
		return nil, errors.Wrap(err, "paipan.GetDayunLiulianScore")
	}
	// 年龄
	getAge, err := slf.coronaCli.GetAge(ctx, &corona.GetAgeRequest{
		Birthtime: realSunTimeStr,
		Gender:    gender,
		Dayun:     req.Dayun,
		Liunian:   req.Liunian,
	})
	if err != nil {
		return nil, errors.Wrap(err, "paipan.GetAge")
	}
	if req.Dayun == "" {
		var idx int
		for i, s := range paipanAll.XiaoyunLiunianGanzhi {
			if s == req.Liunian {
				idx = i
				break
			}
		}
		req.Dayun = paipanAll.XiaoyunGanzhi[idx]
	}
	cyclesData, err := slf.coronaCli.LifeCyclesMonth(ctx, &corona.LifeCyclesMonthRequest{
		Birthday: req.Birthtime,
		Location: req.Birthplace,
		Gender:   req.Gender,
		DaYun:    req.Dayun,
		LiuNian:  req.Liunian,
	})
	if err != nil {
		return nil, errors.Wrap(err, "paipan.LifeCyclesMonth")
	}
	resp := slf.liunianAnalysis(&LiunianAnalysisPayload{
		BaziYear:          getAge.GetBaziYear(),
		AgeS:              getAge.GetAgeS(),
		AgeX:              getAge.GetAgeX(),
		RetJyDate:         paipanAll.RetJyDate,
		Yangli:            paipanAll.Yangli,
		Gender:            req.Gender,
		GenderStr:         gender,
		Minggong:          paipanAll.Minggong,
		Sizhu:             paipanAll.Sizhu,
		DayunGanzhiList:   paipanAll.DayunGanzhi,
		XiaoyunGanzhiList: paipanAll.XiaoyunGanzhi,
		Tiangan:           cyclesData.Tiangan[:6],
		Dizhi:             cyclesData.Dizhi[:6],
		Ganzhi: func() []string {
			var tmp []string
			for i, tg := range cyclesData.Tiangan {
				tmp = append(tmp, fmt.Sprintf("%s%s", tg, cyclesData.Dizhi[i]))
			}
			return tmp
		}(),
		Xingyun:          cyclesData.XingYun[:6],
		XiaoyunQishi:     paipanAll.XiaoyunQishi,
		XiaoyunJiezhi:    paipanAll.XiaoyunJiezhi,
		DayunQishi:       paipanAll.DayunQishi,
		DayunJiezhi:      paipanAll.DayunJiezhi,
		Riyuan:           paipanAll.Riyuan,
		Zhuxing:          cyclesData.ZhuXing[:6],
		BenqiShishen:     cyclesData.BenqiShiShen[:6],
		ZhongqiShishen:   cyclesData.ZhongqiShiShen[:6],
		YuqiShishen:      cyclesData.YuqiShiShen[:6],
		Yxcjx:            strings.SplitN(paipanAll.Xiyongjichou, ",", 5),
		Shensha4:         cyclesData.ShenShaJiShen[:4],
		Shensha6:         cyclesData.ShenShaJiShen[:6],
		ShenshaDL:        cyclesData.ShenShaJiShen[4:6],
		ShishenPowerMap:  paipanAll.ShishenPowerMap,
		ShishenWuxingMap: paipanAll.ShishenWuxingMap,
	}, paipanLn, comb23)
	return resp, nil
}

func (slf *masterService) DayunAnalysis(ctx context.Context, req *v1.DayunAnalysisRequest) (*v1.DayunAnalysisResponseData, error) {
	if req.Birthplace == nil {
		req.Birthplace = []string{}
	}
	realSunTimeStr, err := slf.realSunTime(ctx, req.Birthtime, req.Birthplace)
	if err != nil {
		return nil, errors.Wrap(err, "realSunTime")
	}
	gender := lo.Ternary(req.Gender == 1, "男", "女")
	// 基础信息
	paipanAll, err := slf.coronaCli.GetAll(ctx, &corona.GetAllRequest{
		Birthtime: realSunTimeStr,
		Gender:    gender,
	})
	if err != nil {
		return nil, errors.Wrap(err, "paipan.PaipanAll")
	}
	// 评分
	paipanLn, err := slf.coronaCli.GetDayunLiunianScore(ctx, &corona.GetDayunLiunianScoreRequest{
		Birthtime: realSunTimeStr,
		Gender:    gender,
	})
	if err != nil {
		return nil, errors.Wrap(err, "paipan.GetDayunLiulianScore")
	}
	dyIndex := 0
	for i, s := range paipanAll.GetShierDayun {
		if s == req.Dayun {
			dyIndex = i
			break
		}
	}
	cyclesData, err := slf.coronaCli.LifeCyclesMonth(ctx, &corona.LifeCyclesMonthRequest{
		Birthday: req.Birthtime,
		Location: req.Birthplace,
		Gender:   req.Gender,
		DaYun:    req.Dayun,
	})
	if err != nil {
		return nil, errors.Wrap(err, "paipan.LifeCyclesMonth")
	}
	resp := slf.dayunAnalysis(&DayunAnalysisPayload{
		DayunIndex:        dyIndex,
		DayunGanzhiList:   paipanAll.DayunGanzhi,
		XiaoyunGanzhiList: paipanAll.XiaoyunGanzhi,
		DayunQishi:        paipanAll.DayunQishi,
		DayunJiezhi:       paipanAll.DayunJiezhi,
		Riyuan:            paipanAll.Riyuan,
		Tiangan:           cyclesData.Tiangan[:5],
		Dizhi:             cyclesData.Dizhi[:5],
		Ganzhi: func() []string {
			var tmp []string
			for i, tg := range cyclesData.Tiangan {
				tmp = append(tmp, fmt.Sprintf("%s%s", tg, cyclesData.Dizhi[i]))
			}
			return tmp
		}()[:5],
		Xingyun:        cyclesData.XingYun[:5],
		Zhuxing:        cyclesData.ZhuXing[:5],
		BenqiShishen:   cyclesData.BenqiShiShen[:5],
		ZhongqiShishen: cyclesData.ZhongqiShiShen[:5],
		YuqiShishen:    cyclesData.YuqiShiShen[:5],
		Yxcjx:          strings.SplitN(paipanAll.Xiyongjichou, ",", 5),
		Shensha5:       cyclesData.ShenShaJiShen[:5],
	}, paipanLn, comb23)
	return resp, nil
}

type (
	DayunAnalysisPayload struct {
		DayunIndex        int
		DayunGanzhiList   []string
		DayunQishi        int
		DayunJiezhi       int
		XiaoyunGanzhiList []string
		Riyuan            string
		Tiangan           []string
		Dizhi             []string
		Ganzhi            []string
		Xingyun           []string
		Zhuxing           []string
		BenqiShishen      []string
		ZhongqiShishen    []string
		YuqiShishen       []string
		Yxcjx             []string
		Shensha5          [][]string
	}
)

func (slf *masterService) DayunLiunian(ctx context.Context, req *v1.DayunliunianRequest) (*v1.DayunliunianResponseData, error) {
	var resp v1.DayunliunianResponseData
	if req.Birthplace == nil {
		req.Birthplace = []string{}
	}
	realSunTimeStr, err := slf.realSunTime(ctx, req.Birthtime, req.Birthplace)
	if err != nil {
		return nil, errors.Wrap(err, "realSunTime")
	}
	gender := lo.Ternary(req.Gender == 1, "男", "女")
	paipanAll, err := slf.coronaCli.GetAll(ctx, &corona.GetAllRequest{
		Birthtime: realSunTimeStr,
		Gender:    gender,
	})
	if err != nil {
		return nil, errors.Wrap(err, "paipan.PaipanAll")
	}
	paipanLn, err := slf.coronaCli.GetDayunLiunianScore(ctx, &corona.GetDayunLiunianScoreRequest{
		Birthtime: realSunTimeStr,
		Gender:    gender,
	})
	if err != nil {
		return nil, errors.Wrap(err, "paipan.GetDayunLiulianScore")
	}

	resp.Birthtime = realSunTimeStr
	resp.BirthtimeLunar = paipanAll.Nongli
	resp.JiaoyunTime = paipanAll.RetJyDate
	resp.XiaoyunYears = paipanAll.SubYearXiaoyun

	resp.XiaoyunQishi = paipanAll.XiaoyunQishi
	resp.XiaoyunJiezhi = paipanAll.XiaoyunJiezhi
	resp.DayunQishi = paipanAll.DayunQishi
	resp.DayunJiezhi = paipanAll.DayunJiezhi

	resp.DayunScoreList = paipanLn.Dyun
	resp.LiunianScoreList = paipanLn.Lnian
	resp.XiaoyunScoreList = paipanLn.Xxian
	resp.ZongheScoreList = paipanLn.Zscore
	resp.DayunGanzhiList = paipanAll.GetShierDayun
	resp.DayunLiunianGanzhiList = paipanAll.DayunLiunianGanzhi
	resp.XiaoyunGanzhiList = paipanAll.XiaoyunGanzhi
	resp.XiaoyunLiunianGanzhiList = paipanAll.XiaoyunLiunianGanzhi

	return &resp, nil
}

func (slf *masterService) XiyongWangshuai(ctx context.Context, req *v1.MasterXiyongWangshuaiRequest) (*v1.MasterXiyongWangshuaiResponseData, error) {
	var (
		result = v1.MasterXiyongWangshuaiResponseData{
			Xiyong:    make(map[string]*v1.MasterXiyongWangshuai4Wuxing),
			Wangshuai: map[string]string{},
		}
		wxXiCount, wxYongCount, wxChouCount, wxJiCount, wxXianCount = make(map[string]int), make(map[string]int), make(map[string]int), make(map[string]int), make(map[string]int)
		wsCount                                                     = make(map[string]int)
	)
	for _, str := range req.Times {
		resp, err := slf.coronaCli.GetSizhuWuxingXiyong(ctx, &corona.GetSizhuWuxingXiyongRequest{
			Birthtime: req.Birthtime + " " + str,
			Gender:    req.Gender,
		})
		if err != nil {
			return nil, err
		}
		wxYongCount[resp.XiyongArr[0]]++
		wxXiCount[resp.XiyongArr[1]]++
		wxChouCount[resp.XiyongArr[2]]++
		wxJiCount[resp.XiyongArr[3]]++
		wxXianCount[resp.XiyongArr[4]]++
		wsCount[resp.Riyuan]++
	}
	result.Xiyong["木"] = &v1.MasterXiyongWangshuai4Wuxing{
		Xiyong: fmt.Sprintf("%.1f", float64(wxYongCount["木"]+wxXiCount["木"])/float64(len(req.Times))*100),
		Chouji: fmt.Sprintf("%.1f", float64(wxChouCount["木"]+wxJiCount["木"])/float64(len(req.Times))*100),
		Xian:   fmt.Sprintf("%.1f", float64(wxXianCount["木"])/float64(len(req.Times))*100),
	}
	result.Xiyong["火"] = &v1.MasterXiyongWangshuai4Wuxing{
		Xiyong: fmt.Sprintf("%.1f", float64(wxYongCount["火"]+wxXiCount["火"])/float64(len(req.Times))*100),
		Chouji: fmt.Sprintf("%.1f", float64(wxChouCount["火"]+wxJiCount["火"])/float64(len(req.Times))*100),
		Xian:   fmt.Sprintf("%.1f", float64(wxXianCount["火"])/float64(len(req.Times))*100),
	}
	result.Xiyong["土"] = &v1.MasterXiyongWangshuai4Wuxing{
		Xiyong: fmt.Sprintf("%.1f", float64(wxYongCount["土"]+wxXiCount["土"])/float64(len(req.Times))*100),
		Chouji: fmt.Sprintf("%.1f", float64(wxChouCount["土"]+wxJiCount["土"])/float64(len(req.Times))*100),
		Xian:   fmt.Sprintf("%.1f", float64(wxXianCount["土"])/float64(len(req.Times))*100),
	}
	result.Xiyong["金"] = &v1.MasterXiyongWangshuai4Wuxing{
		Xiyong: fmt.Sprintf("%.1f", float64(wxYongCount["金"]+wxXiCount["金"])/float64(len(req.Times))*100),
		Chouji: fmt.Sprintf("%.1f", float64(wxChouCount["金"]+wxJiCount["金"])/float64(len(req.Times))*100),
		Xian:   fmt.Sprintf("%.1f", float64(wxXianCount["金"])/float64(len(req.Times))*100),
	}
	result.Xiyong["水"] = &v1.MasterXiyongWangshuai4Wuxing{
		Xiyong: fmt.Sprintf("%.1f", float64(wxYongCount["水"]+wxXiCount["水"])/float64(len(req.Times))*100),
		Chouji: fmt.Sprintf("%.1f", float64(wxChouCount["水"]+wxJiCount["水"])/float64(len(req.Times))*100),
		Xian:   fmt.Sprintf("%.1f", float64(wxXianCount["水"])/float64(len(req.Times))*100),
	}
	result.Wangshuai["从强"] = fmt.Sprintf("%.1f", float64(wsCount["从强"])/float64(len(req.Times))*100)
	result.Wangshuai["身强"] = fmt.Sprintf("%.1f", float64(wsCount["身强"]+wsCount["偏强"])/float64(len(req.Times))*100)
	result.Wangshuai["平和"] = fmt.Sprintf("%.1f", float64(wsCount["平和"])/float64(len(req.Times))*100)
	result.Wangshuai["身弱"] = fmt.Sprintf("%.1f", float64(wsCount["身弱"]+wsCount["偏弱"])/float64(len(req.Times))*100)
	result.Wangshuai["从弱"] = fmt.Sprintf("%.1f", float64(wsCount["从弱"])/float64(len(req.Times))*100)
	return &result, nil
}

func (slf *masterService) GeJuBianGe(ctx context.Context, req *v1.GeJuBianGeRequest) (*v1.GeJuBianGeResponseData, error) {
	if req.Birthplace == nil {
		req.Birthplace = []string{}
	}
	realSunTimeStr, err := slf.realSunTime(ctx, req.Birthtime, req.Birthplace)
	if err != nil {
		return nil, errors.Wrap(err, "realSunTime")
	}
	resp, err := slf.coronaCli.GetSizhuWuxingXiyong(ctx, &corona.GetSizhuWuxingXiyongRequest{
		Birthtime: realSunTimeStr,
		Gender:    req.Gender,
	})
	if err != nil {
		return nil, err
	}

	var result v1.GeJuBianGeResponseData

	var (
		tiangan, dizhi       []string
		tianganStr, dizhiStr string
		ganzhi               []string
		ganzhiStr            string
		benqiShishen         = resp.BenqiShishen
		zhongqiShishen       = resp.ZhongqiShishen
		yuqiShishen          = resp.YuqiShishen
		zhuxing              = resp.Zhuxing
		riyuan               = resp.Riyuan
		rgwx                 = slf.GetWuxingByTiangan(resp.Tiangan[2])
		wxArr                = strings.Split(resp.SaveLiliangWuXingMingzi, ",")
		nlArr                = strings.Split(resp.SaveLiliangNum, ",")
		wxNl                 = func() map[string]int {
			m := make(map[string]int)
			for i, v := range wxArr {
				m[v], _ = strconv.Atoi(nlArr[i])
			}
			return m
		}()
	)
	fmt.Println(tianganStr, ganzhiStr)
	for i := 0; i < 4; i++ {
		tiangan = append(tiangan, resp.Tiangan[i])
		dizhi = append(dizhi, resp.Dizhi[i])
	}
	ganzhi = append(tiangan, dizhi...)
	tianganStr = strings.Join(tiangan, "")
	dizhiStr = strings.Join(dizhi, "")
	ganzhiStr = strings.Join(ganzhi, "")

	// 1.曲直格
	/*
	  - 日干为甲 或 乙
	  - 月支为寅、卯
	  - 原局地支，存在寅卯辰三会，或存在亥卯未三合
	  - 原局天干地支中，不存在庚、辛、申、酉
	*/
	{
		if (tiangan[2] == "甲" || tiangan[2] == "乙") &&
			(dizhi[1] == "寅" || dizhi[1] == "卯") &&
			func() bool {
				if _, exist1 := strs.AllCharsExists(dizhiStr, "寅卯辰"); exist1 {
					return true
				}
				_, exist2 := strs.AllCharsExists(dizhiStr, "亥卯未")
				return exist2
			}() &&
			func() bool {
				for _, s := range ganzhi {
					switch s {
					case "庚", "辛", "申", "酉":
						return false
					}
				}
				return true
			}() {
			result = append(result, &v1.GeJuBianGeResponseDataItem{
				Name: "曲直格",
				Xiyong: []string{
					"木", "水", "金", "土", "火",
				},
			})
		}
	}
	// 2.炎上格
	/*
	  - 日干为丙 或 丁
	  - 月支为巳、午
	  - 原局地支，存在巳午未三会，或存在寅午戌三合
	  - 原局天干地支中，不存在壬、癸、子、亥
	*/
	{
		if (tiangan[2] == "丙" || tiangan[2] == "丁") &&
			(dizhi[1] == "巳" || dizhi[1] == "午") &&
			func() bool {
				if _, exist1 := strs.AllCharsExists(dizhiStr, "巳午未"); exist1 {
					return true
				}
				_, exist2 := strs.AllCharsExists(dizhiStr, "寅午戌")
				return exist2
			}() &&
			func() bool {
				for _, s := range ganzhi {
					switch s {
					case "壬", "癸", "子", "亥":
						return false
					}
				}
				return true
			}() {
			result = append(result, &v1.GeJuBianGeResponseDataItem{
				Name: "炎上格",
				Xiyong: []string{
					"火", "木", "水", "金", "土",
				},
			})
		}
	}
	// 3.稼穑格
	/*
	  - 日干为戊或 己
	  - 月支为丑、辰、未、戌
	  - 原局地支
	    - 存在丑、 辰、未、戌均存在
	    - 三个及三个以上地支，为丑、辰、未、戌之一
	  - 原局天干地支中，不存在壬、癸、子、亥
	*/
	{
		if (tiangan[2] == "戊" || tiangan[2] == "己") &&
			func() bool {
				count, exist := strs.AllCharsExists(dizhiStr, "丑辰未戌")
				total := 0
				for _, i := range count {
					total += i
				}
				if exist || total >= 3 {
					return true
				}
				return false
			}() {
			result = append(result, &v1.GeJuBianGeResponseDataItem{
				Name: "稼穑格",
				Xiyong: []string{
					"土", "火", "木", "水", "金",
				},
			})
		}
	}
	// 4.从革格
	/*
	  - 日干为庚、辛
	  - 月支为申、酉
	  - 原局地支存在巳酉丑三合，或者申酉戌三会
	  - 原局天干地支中，不存在丙、丁、巳、午
	*/
	{
		if (tiangan[2] == "庚" || tiangan[2] == "辛") &&
			(dizhi[1] == "申" || dizhi[1] == "酉") &&
			func() bool {
				if _, exist1 := strs.AllCharsExists(dizhiStr, "巳酉丑"); exist1 {
					return true
				}
				_, exist2 := strs.AllCharsExists(dizhiStr, "申酉戌")
				return exist2
			}() &&
			func() bool {
				for _, s := range ganzhi {
					switch s {
					case "丙", "丁", "巳", "午":
						return false
					}
				}
				return true
			}() {
			result = append(result, &v1.GeJuBianGeResponseDataItem{
				Name: "从革格",
				Xiyong: []string{
					"金", "土", "火", "木", "水",
				},
			})
		}
	}
	// 5.润下格
	/*
	  - 日干为壬癸
	  - 月支为亥、子
	  - 原局地支存在申子辰三合或亥子丑三会
	  - 原局天干地支中，不存在戊、己、辰、巳、丑、未
	*/
	{
		if (tiangan[2] == "壬" || tiangan[2] == "癸") &&
			(dizhi[1] == "亥" || dizhi[1] == "子") &&
			func() bool {
				if _, exist1 := strs.AllCharsExists(dizhiStr, "申子辰"); exist1 {
					return true
				}
				_, exist2 := strs.AllCharsExists(dizhiStr, "亥子丑")
				return exist2
			}() &&
			func() bool {
				for _, s := range ganzhi {
					switch s {
					case "戊", "己", "辰", "巳", "丑", "未":
						return false
					}
				}
				return true
			}() {
			result = append(result, &v1.GeJuBianGeResponseDataItem{
				Name: "润下格",
				Xiyong: []string{
					"水", "金", "土", "火", "木",
				},
			})
		}
	}
	// 6.从财格
	/*
	  - 月支的藏干本气十神为正财或偏财
	  - 以下条件满足一个即可
	    - 原局四柱地支发生三会：三会五行为日干的财才（日干五行克制三会的五行）
	    - 原局四柱地支发生三合：三合五行为日干的财才
	    - 原局四柱天干十神、地支藏干十神，正财和偏财数量之和大于等于4
	  - 年柱、月柱、时柱天干十神存在正财或偏财
	  - 年柱、月柱、时柱天干十神、藏干本气十神不存在比肩、劫财、正印、偏印
	  - 原局命理命主旺衰为从弱
	*/
	{
		if (benqiShishen[1] == "正财" || benqiShishen[1] == "偏财") &&
			(func() bool {
				var (
					sanhui, wuxing string
				)
				for k, v := range slf.TblDizhiSanhui {
					split := strings.Split(k, ",")
					if array.SubArrayExist(dizhi, split) {
						sanhui = k
						wuxing = v
						break
					}
				}
				if sanhui == "" {
					return false
				}
				return slf.CaiCai(rgwx) == wuxing
			}() || func() bool {
				var (
					sanhe, wuxing string
				)
				for k, v := range slf.TblDizhiSanhe {
					split := strings.Split(k, ",")
					if array.SubArrayExist(dizhi, split) {
						sanhe = k
						wuxing = v
						break
					}
				}
				if sanhe == "" {
					return false
				}
				return slf.CaiCai(rgwx) == wuxing
			}() || func() bool {
				target := append(zhuxing, benqiShishen...)
				target = append(target, zhongqiShishen...)
				target = append(target, yuqiShishen...)
				count := 0
				for _, s := range target {
					if s == "正财" || s == "偏财" {
						count++
					}
				}
				return count >= 4
			}()) &&
			func() bool {
				target := []string{zhuxing[0], zhuxing[1], zhuxing[3]}
				return array.SubArrayExist(target, []string{"正财"}) ||
					array.SubArrayExist(target, []string{"偏财"})
			}() &&
			func() bool {
				target := []string{zhuxing[0], zhuxing[1], zhuxing[3], benqiShishen[0], benqiShishen[1], benqiShishen[3]}
				for _, s := range target {
					switch s {
					case "比肩", "劫财", "正印", "偏印":
						return false
					default:
						continue
					}
				}
				return true
			}() &&
			riyuan == "从弱" {
			/*
			   - 用：食伤对应的五行，即日干对应五行生的五行
			   - 喜：财才对应的五行，即日干对应五行克制的五行
			   - 忌：比劫对应的五行，即日干对应五行
			   - 仇：印枭对应的五行，即 生 日干对应五行 的五行
			   - 闲：官杀对应的五行，即克制 日干对应五行 的五行
			*/
			result = append(result, &v1.GeJuBianGeResponseDataItem{
				Name: "从财格",
				Xiyong: []string{
					slf.ShiShang(rgwx),
					slf.CaiCai(rgwx),
					slf.BiJie(rgwx),
					slf.YinXiao(rgwx),
					slf.GuanSha(rgwx),
				},
			})
		}
	}
	// 7.从儿格
	/*
	  - 月支的藏干本气十神为食神或伤官
	  - 以下条件发生一条即可
	    - 原局四柱地支发生三会：三会五行为日干的食伤
	    - 原局四柱地支发生三合：三合五行为日干的食伤
	    - 原局四柱天干十神、地支藏干十神，食神和伤官数量之和大于等于4
	  - 年柱、月柱、时柱天干十神存在食神或伤官
	  - 年柱、月柱、时柱天干十神、藏干本气不存在印枭、官杀
	  - 原句命理命主为从弱
	*/
	{
		if (benqiShishen[1] == "食神" || benqiShishen[1] == "伤官") &&
			(func() bool {
				var (
					sanhui, wuxing string
				)
				for k, v := range slf.TblDizhiSanhui {
					split := strings.Split(k, ",")
					if array.SubArrayExist(dizhi, split) {
						sanhui = k
						wuxing = v
						break
					}
				}
				if sanhui == "" {
					return false
				}
				return slf.ShiShang(rgwx) == wuxing
			}() || func() bool {
				var (
					sanhe, wuxing string
				)
				for k, v := range slf.TblDizhiSanhe {
					split := strings.Split(k, ",")
					if array.SubArrayExist(dizhi, split) {
						sanhe = k
						wuxing = v
						break
					}
				}
				if sanhe == "" {
					return false
				}
				return slf.ShiShang(rgwx) == wuxing
			}() || func() bool {
				target := append(zhuxing, benqiShishen...)
				target = append(target, zhongqiShishen...)
				target = append(target, yuqiShishen...)
				count := 0
				for _, s := range target {
					if s == "食神" || s == "伤官" {
						count++
					}
				}
				return count >= 4
			}()) && func() bool {
			target := []string{zhuxing[0], zhuxing[1], zhuxing[3]}
			return array.SubArrayExist(target, []string{"食神"}) || array.SubArrayExist(target, []string{"伤官"})
		}() && func() bool {
			target := []string{zhuxing[0], zhuxing[1], zhuxing[3], benqiShishen[0], benqiShishen[1], benqiShishen[3]}
			for _, s := range target {
				switch s {
				case "印枭", "官杀":
					return false
				default:
					continue
				}
			}
			return true
		}() && riyuan == "从弱" {
			/*
			 - 食伤：日干五行所生
			 - 财才：日干五行所克
			 - 比劫：日干五行
			 - 印枭：生日干五行
			 - 官杀：克制日干五行
			*/
			/*
			   - 若原局命理八字中，存在财才对应的天干或地支：（即
			     - 对应五行的天干或地支）
			     - 用：食伤对应的五行
			     - 喜：财才对应的五行
			     - 忌：印枭对应的五行
			     - 仇：官杀对应的五行
			     - 闲：比劫对应的五行
			   - 若原局命理八字中，不存在财才对应的天干或地支
			     - 用：食伤对应的五行
			     - 喜：比劫对应的五行
			     - 忌：印枭对应的五行
			     - 仇：官杀对应的五行
			     - 闲：财才对应的五行
			*/
			item := &v1.GeJuBianGeResponseDataItem{
				Name: "从儿格",
			}
			if func() bool {
				for _, v := range append(zhuxing, benqiShishen...) {
					switch v {
					case "正财", "偏财":
						return true
					}
				}
				return false
			}() {
				item.Xiyong = []string{
					slf.ShiShang(rgwx),
					slf.CaiCai(rgwx),
					slf.YinXiao(rgwx),
					slf.GuanSha(rgwx),
					slf.BiJie(rgwx),
				}
			} else {
				item.Xiyong = []string{
					slf.ShiShang(rgwx),
					slf.BiJie(rgwx),
					slf.YinXiao(rgwx),
					slf.GuanSha(rgwx),
					slf.CaiCai(rgwx),
				}
			}
			result = append(result, item)
		}
	}
	// 8.从杀格
	/*
	  - 月支的藏干本气十神为食神或伤官
	  - 以下条件发生一条即可
	    - 原局四柱地支发生三会：三会五行为日干的官杀
	    - 原局四柱地支发生三合：三合五行为日干的官杀
	    - 原局四柱天干十神、地支藏干十神，正官和七杀数量之和大于等于4
	  - 年柱、月柱、时柱天干十神存在正官或七杀
	  - 年柱、月柱、时柱天干十神、藏干本气十神不存在食伤、比劫
	  - 原句命理命主为从弱
	*/
	{
		if (benqiShishen[1] == "食神" || benqiShishen[1] == "伤官") &&
			(func() bool {
				var (
					sanhui, wuxing string
				)
				for k, v := range slf.TblDizhiSanhui {
					split := strings.Split(k, ",")
					if array.SubArrayExist(dizhi, split) {
						sanhui = k
						wuxing = v
						break
					}
				}
				if sanhui == "" {
					return false
				}
				return slf.GuanSha(rgwx) == wuxing
			}() ||
				func() bool {
					var (
						sanhe, wuxing string
					)
					for k, v := range slf.TblDizhiSanhe {
						split := strings.Split(k, ",")
						if array.SubArrayExist(dizhi, split) {
							sanhe = k
							wuxing = v
							break
						}
					}
					if sanhe == "" {
						return false
					}
					return slf.GuanSha(rgwx) == wuxing
				}() ||
				func() bool {
					target := append(zhuxing, benqiShishen...)
					target = append(target, zhongqiShishen...)
					target = append(target, yuqiShishen...)
					count := 0
					for _, s := range target {
						if s == "正官" || s == "七杀" {
							count++
						}
					}
					return count >= 4
				}()) &&
			func() bool {
				target := []string{zhuxing[0], zhuxing[1], zhuxing[3]}
				return array.SubArrayExist(target, []string{"正官"}) || array.SubArrayExist(target, []string{"七杀"})
			}() &&
			func() bool {
				target := []string{zhuxing[0], zhuxing[1], zhuxing[3], benqiShishen[0], benqiShishen[1], benqiShishen[3]}
				for _, s := range target {
					switch s {
					case "食神", "伤官", "比肩", "劫财":
						return false
					default:
						continue
					}
				}
				return true
			}() && riyuan == "从弱" {
			/*
			   - 用：官杀对应的五行
			   - 喜：财才对应的五行
			   - 忌：比劫对应的五行
			   - 仇：印枭对应的五行
			   - 闲：食伤对应的五行
			*/
			result = append(result, &v1.GeJuBianGeResponseDataItem{
				Name: "从杀格",
				Xiyong: []string{
					slf.GuanSha(rgwx),
					slf.CaiCai(rgwx),
					slf.BiJie(rgwx),
					slf.YinXiao(rgwx),
					slf.ShiShang(rgwx),
				},
			})
		}
	}
	// 9.从强格
	/*
	  - 原局不存在曲直格、炎上格、稼穑格、从革格、润下格、从财格、从儿格、从杀格
	  - 月支对应的五行，与日干五行一致，或生日干五行
	  - 原局命理中，命主旺衰为从强
	*/
	{
		if len(result) == 0 &&
			(func() bool {
				yzwx := slf.GetWuxingByDizhi(dizhi[1])
				return yzwx == rgwx || yzwx == slf.YinXiao(rgwx)
			}()) && riyuan == "从强" {
			/*
			   - 用：比劫对应的五行
			   - 喜：印枭对应的五行
			   - 忌：官杀对应的五行
			   - 仇：财才对应的五行
			   - 闲：食伤对应的五行
			*/
			result = append(result, &v1.GeJuBianGeResponseDataItem{
				Name: "从强格",
				Xiyong: []string{
					slf.BiJie(rgwx),
					slf.YinXiao(rgwx),
					slf.GuanSha(rgwx),
					slf.CaiCai(rgwx),
					slf.ShiShang(rgwx),
				},
			})
		}
	}
	// 10.从弱格
	/*
	  - 原局不存在曲直格、炎上格、稼穑格、从革格、润下格、从财格、从儿格、从杀格、从强格
	  - 原句命理中，命主旺衰为从弱
	*/
	{
		if len(result) == 0 && riyuan == "从弱" {
			/*
			   - 用神：原局命理五行能量中，五星能量最强的能量五行
			   - 喜神：原局命理五行能量中，生五星能量最强的能量五行的五行（印枭）
			   - 忌神：原局命理五行能量中，克制能量最强的五行的五行（官杀）
			   - 仇神：原局命理五行能力中，五行能量最强五行的克制的五行（财才）
			   - 闲神：用、喜、忌、仇之外的五行
			*/

			wx := func() string {
				val := 0
				idx := 0
				for i, v := range nlArr {
					num, _ := strconv.Atoi(v)
					if num > val {
						val = num
						idx = i
					}
				}
				return wxArr[idx]
			}()
			y, x, c, j := wx, slf.YinXiao(wx), slf.GuanSha(wx), slf.CaiCai(wx)
			result = append(result, &v1.GeJuBianGeResponseDataItem{
				Name: "从弱格",
				Xiyong: []string{
					y, x, c, j, func() string {
						for _, v := range []string{"金", "木", "水", "火", "土"} {
							if v != y && v != x && v != c && v != j {
								return v
							}
						}
						return ""
					}(),
				},
			})
		}
	}
	// 11.半璧格
	/*
		- 若原局命理中，四柱干支中，天干对应的五行、地支本气对应的五行，只有两种，且每种对应每种五行的天干、地支数量都是4个
	*/
	{
		ganzhiWxCount := func() map[string]int {
			m := make(map[string]int)
			for _, s := range tiangan {
				wx := slf.GetWuxingByTiangan(s)
				m[wx]++
			}
			for _, s := range dizhi {
				wx := slf.GetWuxingByDizhi(s)
				m[wx]++
			}
			return m
		}()
		if len(ganzhiWxCount) == 2 &&
			func() bool {
				for _, v := range ganzhiWxCount {
					if v != 4 {
						return false
					}
				}
				return true
			}() {
			// 干支组合
			ganzhiMixCount := func() map[string]int {
				m := make(map[string]int)
				for i := 0; i < 4; i++ {
					m[tiangan[i]+dizhi[i]]++
				}
				return m
			}()
			// 两种五行
			var wx12 []string
			for k, _ := range ganzhiWxCount {
				wx12 = append(wx12, k)
			}
			wx1, wx2 := wx12[0], wx12[1]
			s12, s21 := slf.IsWuxingXiangsheng(wx1, wx2), slf.IsWuxingXiangsheng(wx2, wx1)
			k12, k21 := slf.IsWuxingXiangke(wx1, wx2), slf.IsWuxingXiangke(wx2, wx1)
			//   - 若两种五行为相生五行
			if s12 || s21 {
				item := &v1.GeJuBianGeResponseDataItem{
					Name: func() string {
						/*
						   - 若原局中只有两种干支组合，每种干支组合各有两柱
						     - 两天干对应五行相同，格局为：相类两干不杂格
						     - 两天干分别为木火，格局为：木火相生格
						     - 两天干分别为火土，格局为：火土相生格
						     - 两天干分别为土金，格局为：土金相生格
						     - 两天干分别为金水，格局为：金水相生格
						     - 两天干分别为水木，格局为：水木相生格
						*/
						if len(ganzhiMixCount) == 2 &&
							func() bool {
								for _, v := range ganzhiMixCount {
									if v != 2 {
										return false
									}
								}
								return true
							}() {
							var wx12 []string
							for k, _ := range ganzhiWxCount {
								wx12 = append(wx12, k)
							}
							wx1, wx2 := wx12[0], wx12[1]
							if wx1 == wx2 {
								return "相类两干不杂格"
							} else if wx1 == "木" && wx2 == "火" || wx1 == "火" && wx2 == "木" {
								return "木火相生格"
							} else if wx1 == "火" && wx2 == "土" || wx1 == "土" && wx2 == "火" {
								return "火土相生格"
							} else if wx1 == "土" && wx2 == "金" || wx1 == "金" && wx2 == "土" {
								return "土金相生格"
							} else if wx1 == "金" && wx2 == "水" || wx1 == "水" && wx2 == "金" {
								return "金水相生格"
							} else if wx1 == "水" && wx2 == "木" || wx1 == "木" && wx2 == "水" {
								return "水木相生格"
							}
						}
						return "相生半壁格"
					}(),
					Xiyong: func() []string {
						ln1, ln2 := wxNl[wx1], wxNl[wx2]
						/*
						   - 用神：命局两种五行中，取能量相对较弱的为用神
						   - 喜神：命局两种五行中，取能量相对较强的为用神
						   - 忌神：取命局中两种五行所生的五行，且不是命局中的存在的两种五行，为忌神
						   - 仇神：取忌神所生五行
						   - 闲神：取仇神所生五行
						*/
						yong := lo.Ternary(ln1 < ln2, wx1, wx2)
						xi := lo.Ternary(ln1 < ln2, wx2, wx1)
						ji := lo.Ternary(s12, slf.TblWuxingXiangsheng[wx2], slf.TblWuxingXiangsheng[wx1])
						chou := slf.TblWuxingXiangsheng[ji]
						xian := slf.TblWuxingXiangsheng[chou]
						return []string{
							yong, xi, ji, chou, xian,
						}
					}(),
				}
				result = append([]*v1.GeJuBianGeResponseDataItem{item}, result...)
			} else if k12 || k21 {
				// 转圈圈：输入的五行相克（圈中最小间距一定为1），返回最小间距中间的五行
				fn := func(w1, w2 string) string {
					arr := []string{"木", "火", "土", "金", "水"}
					idxFn := func(w string) int {
						for i, s := range arr {
							if s == w {
								return i
							}
						}
						return -1
					}
					idx1, idx2 := idxFn(w1), idxFn(w2)
					if idx1 == -1 || idx2 == -1 {
						return "never happen"
					}
					if idx1 > idx2 {
						idx1, idx2 = idx2, idx1
					}
					n := len(arr)
					l1 := (idx2 - idx1) % n
					l2 := n - l1
					if l1 <= l2 {
						idx := (idx1 + l1/2) % n
						return arr[idx]
					} else {
						idx := (idx2 + l2/2) % n
						return arr[idx]
					}
				}

				item := &v1.GeJuBianGeResponseDataItem{
					Name: func() string {
						/*
						   - 两天干分别为木土，格局为：木土相成格
						   - 两天干分别为土水，格局为：土水相成格
						   - 两天干分别为水火，格局为：水火相成格
						   - 两天干分别为火金，格局为：火金相成格
						   - 两天干分别为金木，格局为：金木相成格
						*/
						if tiangan[0] == "木" && tiangan[1] == "土" || tiangan[0] == "土" && tiangan[1] == "木" {
							return "木土相成格"
						} else if tiangan[0] == "土" && tiangan[1] == "水" || tiangan[0] == "水" && tiangan[1] == "土" {
							return "土水相成格"
						} else if tiangan[0] == "水" && tiangan[1] == "火" || tiangan[0] == "火" && tiangan[1] == "水" {
							return "水火相成格"
						} else if tiangan[0] == "火" && tiangan[1] == "金" || tiangan[0] == "金" && tiangan[1] == "火" {
							return "火金相成格"
						} else if tiangan[0] == "金" && tiangan[1] == "木" || tiangan[0] == "木" && tiangan[1] == "金" {
							return "金木相成格"
						}
						return "相克半壁格"
					}(),
					Xiyong: func() []string {
						/*
							- 用神：按照木火土金水的循环顺序，取两个五行相隔较近的那个五行
							- 喜神：取两五行中，被克制五行为喜神
							- 忌神：找出两五行中，克制另外一个五行的五行，取克制该五行的五行为忌神
							- 仇神：克制喜神的五行
							- 闲神：两五行中，克制另外一个五行的五行
						*/
						yong := fn(wx1, wx2)
						xi := lo.Ternary(k12, wx2, wx1)
						tmp := lo.Ternary(k12, func() string {
							for k, v := range slf.TblWuxingXiangke {
								if v == wx1 {
									return k
								}
							}
							return "never happen"
						}(), func() string {
							for k, v := range slf.TblWuxingXiangke {
								if v == wx2 {
									return k
								}
							}
							return "never happen"
						}())
						ji := func() string {
							for k, v := range slf.TblWuxingXiangke {
								if v == tmp {
									return k
								}
							}
							return "never happen"
						}()
						chou := func() string {
							for k, v := range slf.TblWuxingXiangke {
								if v == xi {
									return k
								}
							}
							return "never happen"
						}()
						xian := tmp
						return []string{
							xi,
							yong,
							ji,
							chou,
							xian,
						}
					}(),
				}
				result = append([]*v1.GeJuBianGeResponseDataItem{item}, result...)
			}
		}
	}
	return &result, nil
}

func (slf *masterService) Master(ctx context.Context, req *v1.MasterRequest) (*v1.MasterResponseData, error) {
	if req.Birthplace == nil {
		req.Birthplace = []string{}
	}
	genderNum := lo.Ternary(req.Gender == "男", 1, 2)
	birthtime, err := time.Parse("2006-01-02 15:04:05", req.Birthtime)
	if err != nil {
		return nil, errors.Wrap(err, "time.Parse")
	}
	result := &v1.MasterResponseData{}
	realSunTimeStr, err := slf.realSunTime(ctx, req.Birthtime, req.Birthplace)
	if err != nil {
		return nil, errors.Wrap(err, "realSunTime")
	}
	realSunTime, err := time.Parse("2006-01-02 15:04:05", realSunTimeStr)
	if err != nil {
		return nil, err
	}
	//currentTime := time.Now().Format("2006-01-02 15:04:05")
	paipanAll, err := slf.coronaCli.GetAll(ctx, &corona.GetAllRequest{
		Birthtime: realSunTimeStr,
		Gender:    req.Gender,
	})
	if err != nil {
		return nil, errors.Wrap(err, "paipan.PaipanAll")
	}
	paipanBaziMap, err := slf.coronaCli.PaipanBaziMap(ctx, &corona.GetPaipanBaziMapRequest{
		Birthday: realSunTimeStr,
		Gender:   genderNum,
		IsLunar:  false,
		Location: req.Birthplace,
		Name:     req.Name,
	})
	// 1.命主信息
	result.Mingzhu = &v1.MasterMinzhu{
		Name:           req.Name,
		Birthtime:      realSunTimeStr,
		BirthtimeLunar: strs.Digit2ZhUpper(paipanAll.Nongli),
		Birthplace:     req.Birthplace,
		Gender:         req.Gender,
		Bazi:           paipanAll.Sizhu,
		Wuxing:         strings.SplitN(paipanAll.Xiyongjichou, ",", 5),
		Zodiac:         paipanAll.Shengxiao,
	}

	// 一、生辰分析
	{
		res := &v1.MasterBirthAnalysis{}
		result.BirthAnalysis = res
		res.Year, err = slf.masterRepo.FetchBirthYearFortune(ctx, paipanAll.Sizhu[0])
		if err != nil {
			return nil, err
		}
		res.Month, err = slf.masterRepo.FetchBirthMonthFortune(ctx, string([]rune(paipanAll.Sizhu[1])[1])+"月")
		if err != nil {
			return nil, err
		}
		day, hour := func() (string, string) {
			str := paipanAll.Nongli
			if strings.Contains(str, "闰") {
				str = strings.ReplaceAll(str, "闰", "")
			}
			nongli := strings.TrimSpace(str)
			pattern := `月(.*?)\s(.*)`
			re := regexp.MustCompile(pattern)
			matches := re.FindStringSubmatch(nongli)
			if len(matches) > 2 {
				return matches[1], matches[2]
			}
			return "", ""
		}()
		res.Day, err = slf.masterRepo.FetchBirthDayFortune(ctx, day)
		if err != nil {
			return nil, err
		}
		res.Hour, err = slf.masterRepo.FetchBirthHourFortune(ctx, strings.TrimSuffix(hour, "时"))
		if err != nil {
			return nil, err
		}
	}
	// 二、调候用神
	{
		result.TiaoHouYongShen, err = slf.masterRepo.FetchBirthMonthRizhu(ctx, string([]rune(paipanAll.Sizhu[2])[0]), string([]rune(paipanAll.Sizhu[1])[1]))
		if err != nil {
			return nil, err
		}
	}
	// 三、日主生时
	{
		res := &v1.MasterRizhuShengshi{}
		result.RizhuShengshi = res
		res.BirthHourRizhu, err = slf.masterRepo.FetchBirthHourRizhu(ctx, string([]rune(paipanAll.Sizhu[2])[0]), paipanAll.Sizhu[3])
		if err != nil {
			return nil, err
		}
		res.BirthDayHour, err = slf.masterRepo.FetchBirthDayHour(ctx, paipanAll.Sizhu[2], paipanAll.Sizhu[3])
		if err != nil {
			return nil, err
		}
	}
	// 四、命宫寓意
	{
		result.Minggong, err = slf.masterRepo.FetchMinggong(ctx, string([]rune(paipanAll.Minggong)[1])+"宫")
		if err != nil {
			return nil, err
		}
	}
	// 六、职业财运 TODO
	// 七、功名官运
	// 功名官运
	{
		keys := slf.GongMingGuanYun(paipanAll, genderNum, comb23)
		re := regexp.MustCompile(`^\[([A-Z]{2}\d{3})\]`)
		for _, s := range keys {
			match := re.FindStringSubmatch(s)
			if len(match) > 1 {
				result.GongmingGuanyun = append(result.GongmingGuanyun, corona.GongmingGuanyunCodes[match[1]])
			} else {
				result.GongmingGuanyun = append(result.GongmingGuanyun, s)
			}
		}
	}
	// 身体健康
	{
		keys := slf.ShentiJiankang(paipanAll, genderNum, comb23)
		re := regexp.MustCompile(`^\[([A-Z]{2}\d+)\]`)
		for _, s := range keys {
			match := re.FindStringSubmatch(s)
			if len(match) > 1 {
				result.ShentiJiankang = append(result.ShentiJiankang, corona.ShentiJiankangCodes[match[1]])
			} else {
				result.ShentiJiankang = append(result.ShentiJiankang, s)
			}
		}
	}
	// 性格
	{
		result.Xinge = slf.MasterXinge(paipanAll, genderNum, comb23)
	}

	// 祖业遗产
	{
		keys := slf.ZuYeYiChan(paipanAll, genderNum, comb23)
		re := regexp.MustCompile(`^\[([A-Z]{2}\d{3})\]`)
		for _, s := range keys {
			match := re.FindStringSubmatch(s)
			if len(match) > 1 {
				result.ZuyeYichan = append(result.ZuyeYichan, corona.ZuyeyichanCodes[match[1]])
			} else {
				result.ZuyeYichan = append(result.ZuyeYichan, s)
			}
		}
	}
	// 大运流年
	{
		//result.DayunAnalysis = slf.dayunAnalysis(paipanAll, paipanLn, comb23)
		//result.LiunianAnalysis = slf.liunianAnalysis(paipanAll, paipanLn, comb23)
	}
	// 2.11 有利选择
	result.FavorableChoice = &v1.MasterFavorableChoice{}
	{
		// 五行
		{
			yong, xi := result.Mingzhu.Wuxing[0], result.Mingzhu.Wuxing[1]
			result.FavorableChoice.Wuxing = append(result.FavorableChoice.Wuxing,
				fmt.Sprintf("以出生地或居住地为中心，您的住屋、办公室、坐位、睡姿头部、求学、就业、求医等朝向%s、%s更为吉利",
					slf.wuxingEffects[yong].Fangwei,
					slf.wuxingEffects[xi].Fangwei,
				))
			result.FavorableChoice.Wuxing = append(result.FavorableChoice.Wuxing,
				fmt.Sprintf("创业时机、出门远行等办事时间选择%s、%s可更为吉顺，事半功倍。",
					slf.wuxingEffects[yong].Jijie,
					slf.wuxingEffects[xi].Jijie,
				))
			result.FavorableChoice.Wuxing = append(result.FavorableChoice.Wuxing,
				fmt.Sprintf("衣着、首饰、家具、装潢等宜采用%s、%s",
					slf.wuxingEffects[yong].Yanse,
					slf.wuxingEffects[xi].Yanse,
				))
			result.FavorableChoice.Wuxing = append(result.FavorableChoice.Wuxing,
				fmt.Sprintf("门牌、车牌、电话号码、抽奖、楼层、房号等选择有数字%s、%s比较幸运",
					slf.wuxingEffects[yong].Shuzi,
					slf.wuxingEffects[xi].Shuzi,
				))
			result.FavorableChoice.Wuxing = append(result.FavorableChoice.Wuxing,
				fmt.Sprintf("居住地宜选择%s、%s。",
					slf.wuxingEffects[yong].Juzhudi,
					slf.wuxingEffects[xi].Juzhudi))
			result.FavorableChoice.Wuxing = append(result.FavorableChoice.Wuxing,
				fmt.Sprintf("饮食方面宜吃%s、%s（结合当前身体实际情况，科学膳食）",
					slf.wuxingEffects[yong].Yinshi,
					slf.wuxingEffects[xi].Yinshi))
			result.FavorableChoice.Wuxing = append(result.FavorableChoice.Wuxing,
				fmt.Sprintf("药物方面宜吃：%s、%s（身体不适请及时到正规医院医治，遵从医嘱）",
					slf.wuxingEffects[yong].Yaowu,
					slf.wuxingEffects[xi].Yaowu))
		}
		// 天乙贵人
		{
			mzRigan := paipanAll.Tiangan[2]
			shengxiao := slf.tianganTianyi[mzRigan].GuirenSheng
			result.FavorableChoice.TianyiGuiren = fmt.Sprintf("朋友上适合选择%s年生的人，因为他是你的天乙贵人。", strings.Join(shengxiao, "、"))
		}
		// 生肖三合、六合
		{
			mzNianzhiName := paipanAll.Dizhi[0]
			nianzhi, err := slf.luncaiRepo.GetDizhi(ctx, mzNianzhiName)
			if err != nil {
				return nil, err
			}
			shengxiao := nianzhi.Shuxiang
			dizhiSanhe, err := slf.luncaiRepo.GetDizhiSanheByOne(ctx, mzNianzhiName)
			if err != nil {
				return nil, err
			}
			var sanheDizhi1, sanheDizhi2 string
			if dizhiSanhe.Dizhi1 == mzNianzhiName {
				sanheDizhi1 = dizhiSanhe.Dizhi2
				sanheDizhi2 = dizhiSanhe.Dizhi3
			} else if dizhiSanhe.Dizhi2 == mzNianzhiName {
				sanheDizhi1 = dizhiSanhe.Dizhi1
				sanheDizhi2 = dizhiSanhe.Dizhi3
			} else {
				sanheDizhi1 = dizhiSanhe.Dizhi1
				sanheDizhi2 = dizhiSanhe.Dizhi2
			}
			dizhiLiuhe, err := slf.luncaiRepo.GetDizhiLiuheByOne(ctx, mzNianzhiName)
			if err != nil {
				return nil, err
			}
			var liuheDizhi string
			if dizhiLiuhe.Dizhi1 == mzNianzhiName {
				liuheDizhi = dizhiLiuhe.Dizhi2
			} else {
				liuheDizhi = dizhiLiuhe.Dizhi1
			}
			result.FavorableChoice.Shengxiao = fmt.Sprintf("属%s，与%s、%s年生者三合，与%s年生者六合，即和这三年生的人相处较为融洽。",
				shengxiao, sanheDizhi1, sanheDizhi2, liuheDizhi,
			)
		}
		// 事业宫
		{
			mzYueZhiName := paipanAll.Dizhi[1]
			mzRiGanName := paipanAll.Tiangan[2] // 日干
			sanhe, err := slf.luncaiRepo.GetDizhiSanheByOne(ctx, mzYueZhiName)
			if err != nil {
				return nil, err
			}
			var sanheDizhi1, sanheDizhi2 string
			if sanhe.Dizhi1 == mzYueZhiName {
				sanheDizhi1 = sanhe.Dizhi2
				sanheDizhi2 = sanhe.Dizhi3
			} else if sanhe.Dizhi2 == mzYueZhiName {
				sanheDizhi1 = sanhe.Dizhi1
				sanheDizhi2 = sanhe.Dizhi3
			} else {
				sanheDizhi1 = sanhe.Dizhi1
				sanheDizhi2 = sanhe.Dizhi2
			}
			liuhe, err := slf.luncaiRepo.GetDizhiLiuheByOne(ctx, mzYueZhiName)
			if err != nil {
				return nil, err
			}
			var liuheDizhi string
			if liuhe.Dizhi1 == mzYueZhiName {
				liuheDizhi = liuhe.Dizhi2
			} else {
				liuheDizhi = liuhe.Dizhi1
			}
			mzYuezhi, err := slf.luncaiRepo.GetDizhi(ctx, mzYueZhiName)
			if err != nil {
				return nil, err
			}
			mzYueZhiShishen, err := slf.luncaiRepo.GetShishenByRiyuan(ctx, mzRiGanName, mzYueZhiName)
			if err != nil {
				return nil, err
			}
			result.FavorableChoice.CareerPalace = &v1.LuncaiCareerPalace{
				Middle:  mzYueZhiName,
				Left:    sanheDizhi1,
				Right:   sanheDizhi2,
				Down:    liuheDizhi,
				Shishen: mzYueZhiShishen.Shishen,
				Yueling: v1.LuncaiCareerPalaceYueling{
					Xiyong: mzYuezhi.Dizhi + mzYuezhi.Wuxing,
					Dizhi: []string{
						sanheDizhi1, sanheDizhi2, liuheDizhi,
					},
				},
			}
		}
	}
	// 2.15 铁口直断
	{
		result.TiekouZhiduan = &v1.MasterTiekouZhiduan{}
		// 伤残断
		mzRizhi, mzShizhi := paipanAll.Dizhi[2], paipanAll.Dizhi[3]
		scDuan := slf.shangcanDuan[mzRizhi+mzShizhi]
		if scDuan != "" {
			result.TiekouZhiduan.ShangcanDuan = fmt.Sprintf("%s日%s时%s", mzRizhi, mzShizhi, scDuan)
		}
		// 干支断
		ganzhiStr := strings.Join(paipanAll.Sizhu, "")
		mCountGanzhiStr := func() map[string]int {
			var res = make(map[string]int)
			for _, s := range ganzhiStr {
				res[string(s)]++
			}
			return res
		}()
		/*
			干支	数量	显示结果
			甲	3	三甲：天上贵（就是说八字天干上有三个甲者，可誉为天上有贵。）
			丙	3	三丙：入火乡，母亲产中亡。
			丁	3	三丁：多恶疾，手足也有伤。
			戊	3	三戊：子同乡或离祖别家乡。
			巳	3	三己：父母别，兄弟各一方。
			庚	3	三庚：是才郎，万里置田庄。
			壬	3	三壬：富不长（八字天干有三个壬，虽富但不久长。
			子	3	三子婚事重。
			丑	3	三丑四妻房。
			寅	3	三寅独孤守。
			寅	3	人骑三个虎，若不当官定受苦。
			卯	3	三卯凶恶多。
			午	3	三午妻有伤。
			未	3	三未定空亡。
			酉	3	三酉守空房。
			亥	3	三亥孤伶仃。
			子	2	二鼠盘仓有吃喝。
			丑	2	牛卧槽婚姻有曲折。
			寅	2	二虎相争必有一伤。
			午	2	二马盘纲富贵命，（不做大官，必有挫折）。
			申	2	二猴赶山忙碌一生。
			戌	2	二犬口舌多
			亥	2	二猪有福无情。
		*/
		if mCountGanzhiStr["甲"] == 3 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "三甲：天上贵")
		}
		if mCountGanzhiStr["丙"] == 3 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "三丙：入火乡，母亲产中亡")
		}
		if mCountGanzhiStr["丁"] == 3 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "三丁：多恶疾，手足也有伤")
		}
		if mCountGanzhiStr["戊"] == 3 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "三戊：子同乡或离祖别家乡")
		}
		if mCountGanzhiStr["巳"] == 3 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "三己：父母别，兄弟各一方")
		}
		if mCountGanzhiStr["庚"] == 3 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "三庚：是才郎，万里置田庄")
		}
		if mCountGanzhiStr["壬"] == 3 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "三壬：富不长")
		}
		if mCountGanzhiStr["子"] == 3 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "三子婚事重")
		}
		if mCountGanzhiStr["丑"] == 3 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "三丑四妻房")
		}
		if mCountGanzhiStr["寅"] == 3 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "三寅独孤守")
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "人骑三个虎，若不当官定受苦")
		}
		if mCountGanzhiStr["卯"] == 3 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "三卯凶恶多")
		}
		if mCountGanzhiStr["午"] == 3 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "三午妻有伤")
		}
		if mCountGanzhiStr["未"] == 3 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "三未定空亡")
		}
		if mCountGanzhiStr["酉"] == 3 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "三酉守空房")
		}
		if mCountGanzhiStr["亥"] == 3 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "三亥孤伶仃")
		}
		if mCountGanzhiStr["子"] == 2 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "二鼠盘仓有吃喝")
		}
		if mCountGanzhiStr["丑"] == 2 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "牛卧槽婚姻有曲折")
		}
		if mCountGanzhiStr["寅"] == 2 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "二虎相争必有一伤")
		}
		if mCountGanzhiStr["午"] == 2 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "二马盘纲富贵命")
		}
		if mCountGanzhiStr["申"] == 2 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "二猴赶山忙碌一生")
		}
		if mCountGanzhiStr["戌"] == 2 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "二犬口舌多")
		}
		if mCountGanzhiStr["亥"] == 2 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "二猪有福无情")
		}
		mCountGanzhiArray := make(map[string]int)
		for _, s := range paipanAll.Sizhu {
			mCountGanzhiArray[s]++
		}
		/*
			己巳	4	己巳全逢命裡排，一生天禄暗催来；人中必显名尊贵，秀夺江山出类才。【己巳】。
			甲戌	4	天干四甲皆逢戍，分夺财官无所益；若还行运到南方，合此伤官些少吉。【甲戌】。
			庚辰	4	金龙变化春三月，四柱全逢掌大权；不入朝廷为宰相，也须名利镇边疆。【庚辰】。
			乙酉	4	阴木生居八月天，重重乙酉喜相连；不分左右皆显贵，更有收成在晚年。【乙酉】。
			辛卯	4	人命如值四卯全，干同辛字又相连；身轻福浅犹閒事，诚恐将来寿不坚。【辛卯】。
			丙申	4	丙申四柱命中全，身杀两停显福元；不是寻常命利客，管教势大夺魁权。【丙申】。
			壬寅	4	四重壬水四重寅，离坎交争旺气生；运至火乡加显贵，往来须忌对提冲。【壬寅】。
			癸卯	4	天干四癸立乾宫，水木相逢作倒冲；名利盈盈须有望，南方行运寿元终。【癸卯】。
			丁未	4	四重丁未命中排，阴木暗生禄刃胎；有分东西成富贵，无情行到水中来。【丁未】。
			戊午	4	戊土重逢午字多，天干一字得中和；英雄特达功名好，得子冲来没奈何。【戊午】。
		*/
		if mCountGanzhiArray["己巳"] == 4 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "己巳全逢命裡排，一生天禄暗催来；人中必显名尊贵，秀夺江山出类才。【己巳】")
		}
		if mCountGanzhiArray["甲戌"] == 4 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "天干四甲皆逢戍，分夺财官无所益；若还行运到南方，合此伤官些少吉。【甲戌】")
		}
		if mCountGanzhiArray["庚辰"] == 4 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "金龙变化春三月，四柱全逢掌大权；不入朝廷为宰相，也须名利镇边疆。【庚辰】")
		}
		if mCountGanzhiArray["乙酉"] == 4 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "阴木生居八月天，重重乙酉喜相连；不分左右皆显贵，更有收成在晚年。【乙酉】")
		}
		if mCountGanzhiArray["辛卯"] == 4 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "人命如值四卯全，干同辛字又相连；身轻福浅犹閒事，诚恐将来寿不坚。【辛卯】")
		}
		if mCountGanzhiArray["丙申"] == 4 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "丙申四柱命中全，身杀两停显福元；不是寻常命利客，管教势大夺魁权。【丙申】")
		}
		if mCountGanzhiArray["壬寅"] == 4 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "四重壬水四重寅，离坎交争旺气生；运至火乡加显贵，往来须忌对提冲。【壬寅】。")
		}
		if mCountGanzhiArray["癸卯"] == 4 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "天干四癸立乾宫，水木相逢作倒冲；名利盈盈须有望，南方行运寿元终。【癸卯】。")
		}
		if mCountGanzhiArray["丁未"] == 4 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "四重丁未命中排，阴木暗生禄刃胎；有分东西成富贵，无情行到水中来。【丁未】。")
		}
		if mCountGanzhiArray["戊午"] == 4 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "戊土重逢午字多，天干一字得中和；英雄特达功名好，得子冲来没奈何。【戊午】。")
		}
		/*
			1. 条件:天干中，同时存在两个“丙”、“庚”，不计算顺序。
			  - 满足则显示“两丙两庚两村乡。”
			2. 条件:天干中，同时存在两个“壬”、“辛”，不计算顺序。
			  - 满足则显示 “两壬两辛两爹娘。”
			3. 条件:命局中，同时存在一个或多个 “庚”、“甲”、“寅”、“申”，不计算顺序。
			  - 满足则显示 “柱有庚甲寅申四字，背井离乡，奔波道路经商之客。”
			4. 条件:命局中，同时存在一个或多个 “庚”、“甲”、“子”、“午”，不计算顺序。
			  - 满足则显示 “柱有庚甲子午四字异地安居。”
			5. 条件:命局中，同时存在一个或多个 “癸”、“乙”、“壬”、“卯”、“酉”，不计算顺序。
			  - 满足则显示 “柱有癸乙壬卯酉五字，多有男女奸情。”
			6. 条件:命局中，同时存在一个或多个 “甲”、“乙”、“壬”、“癸”，不计算顺序。
			  - 满足则显示 “柱有甲乙壬癸四字,家中必有女人为娼。”
			7. 条件:命局中，同时存在一个或多个 “戊”、“己”、“寅”、“卯”，不计算顺序。
			  - 满足则显示 “干有戊己,支有寅卯.家中有人患四肢风病,瘫痪之疾病,长年卧病在床。”
			8. 条件:命局中，同时存在一个或多个 “壬”、“癸”、“戌”、“亥”，不计算顺序。
			  - 满足则显示 “干有壬癸,支有戌亥,家中人出賊.或自己财物经常被偷。”
			9. 条件:命局中，同时存在一个或多个 “壬”、“丙”，不计算顺序。
			  - 满足则显示 “干有丙壬二字,必有兄弟姐妹早年夭折或中年丧亡。”
			10. 条件:命局中，同时存在一个或多个 “壬”、“癸”、“甲”，不计算顺序。
			  - 满足则显示 “干有壬癸甲三字,家中必出奸狡或虚伪之人,或自己奸诈。”
		*/
		ganStr := strings.Join(paipanAll.Tiangan[0:4], "")
		mCountGanStr := func() map[string]int {
			var res = make(map[string]int)
			for _, s := range ganStr {
				res[string(s)]++
			}
			return res
		}()
		if mCountGanStr["丙"] == 2 && mCountGanStr["庚"] == 2 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "两丙两庚两村乡")
		}
		if mCountGanStr["壬"] == 2 && mCountGanStr["辛"] == 2 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "两壬两辛两爹娘")
		}
		if mCountGanzhiArray["庚"] >= 1 && mCountGanzhiArray["甲"] >= 1 && mCountGanzhiArray["寅"] >= 1 && mCountGanzhiArray["申"] >= 1 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "柱有庚甲寅申四字，背井离乡，奔波道路经商之客")
		}
		if mCountGanzhiArray["庚"] >= 1 && mCountGanzhiArray["甲"] >= 1 && mCountGanzhiArray["子"] >= 1 && mCountGanzhiArray["午"] >= 1 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "柱有庚甲子午四字异地安居")
		}
		if mCountGanzhiArray["癸"] >= 1 && mCountGanzhiArray["乙"] >= 1 && mCountGanzhiArray["壬"] >= 1 && mCountGanzhiArray["卯"] >= 1 && mCountGanzhiArray["酉"] >= 1 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "柱有癸乙壬卯酉五字，多有男女奸情")
		}
		if mCountGanzhiArray["甲"] >= 1 && mCountGanzhiArray["乙"] >= 1 && mCountGanzhiArray["壬"] >= 1 && mCountGanzhiArray["癸"] >= 1 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "柱有甲乙壬癸四字,家中必有女人为娼")
		}
		if mCountGanzhiArray["戊"] >= 1 && mCountGanzhiArray["己"] >= 1 && mCountGanzhiArray["寅"] >= 1 && mCountGanzhiArray["卯"] >= 1 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "干有戊己,支有寅卯.家中有人患四肢风病,瘫痪之疾病,长年卧病在床")
		}
		if mCountGanzhiArray["壬"] >= 1 && mCountGanzhiArray["癸"] >= 1 && mCountGanzhiArray["戌"] >= 1 && mCountGanzhiArray["亥"] >= 1 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "干有壬癸,支有戌亥,家中人出賊.或自己财物经常被偷")
		}
		if mCountGanzhiArray["壬"] >= 1 && mCountGanzhiArray["丙"] >= 1 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "干有丙壬二字,必有兄弟姐妹早年夭折或中年丧亡")
		}
		if mCountGanzhiArray["壬"] >= 1 && mCountGanzhiArray["癸"] >= 1 && mCountGanzhiArray["甲"] >= 1 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "干有壬癸甲三字,家中必出奸狡或虚伪之人,或自己奸诈")
		}
		if func() bool {
			var exist = make(map[string]bool)
			for _, s := range paipanAll.Dizhi[0:4] {
				switch s {
				case "卯", "辰", "巳", "午", "未", "申":
					if exist[s] {
						return false
					}
					exist[s] = true
				default:
					return false
				}
			}
			return true
		}() || func() bool {
			var exist = make(map[string]bool)
			for _, s := range paipanAll.Dizhi[0:4] {
				switch s {
				case "酉", "戌", "亥", "子", "丑", "寅":
					if exist[s] {
						return false
					}
					exist[s] = true
				default:
					return false
				}
			}
			return true
		}() {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "支为卯辰巳午未申中的四个，或支为酉戌亥子丑寅中的四字，家破人亡、夫妻离散。")
		}
		if func() bool {
			dizhi := paipanAll.Dizhi[0:4]
			for i := 0; i < len(dizhi)-1; i++ {
				if (dizhi[i]) == "巳" && (dizhi[i+1]) == "亥" {
					return true
				}
				if (dizhi[i]) == "亥" && (dizhi[i+1]) == "巳" {
					return true
				}
			}
			return false
		}() {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "巳冲亥劳碌一生。")
		}
		if func() bool {
			for _, s := range paipanAll.Sizhu {
				switch s {
				case "癸未", "庚申":
					return true
				}
			}
			return false
		}() {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "四柱中有癸未，庚申的人，需防路遇盗贼而伤身、破财或凶亡。")
		}
		gtNum, jjNum := 0, 0
		for _, edge := range paipanBaziMap.Data.MapTransfer.Edges {
			if strings.Contains(edge.Relation, "盖头") {
				gtNum++
			}
			if strings.Contains(edge.Relation, "截脚") {
				jjNum++
			}
		}
		if gtNum == 3 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "四柱干支三上克下，即四柱中有三柱盖头克，兆示命主易遭官司。")
		}
		if jjNum == 3 {
			result.TiekouZhiduan.GanzhiDuan = append(result.TiekouZhiduan.GanzhiDuan, "四柱有三柱地支截脚克，谓三下克上，主受小人之害或祸起小人。")
		}
		// 十排歌
		{
			shishenNum1 := make(map[string]int)
			for i, s := range paipanAll.Tiangan[0:4] {
				if i == 2 {
					continue
				}
				ry, err := slf.luncaiRepo.GetShishenByRiyuan(ctx, paipanAll.Tiangan[2], s)
				if err != nil {
					return nil, err
				}
				shishenNum1[ry.Shishen]++
			}
			for _, s := range paipanAll.Dizhi[0:4] {
				ry, err := slf.luncaiRepo.GetShishenByRiyuan(ctx, paipanAll.Tiangan[2], s)
				if err != nil {
					return nil, err
				}
				shishenNum1[ry.Shishen]++
			}
			for k, v := range shishenNum1 {
				result.TiekouZhiduan.ShishenDuan1 = append(result.TiekouZhiduan.ShishenDuan1, slf.shishenNumMap[k][v-1])
			}
			slf.ShiPaiGeSort(result.TiekouZhiduan.ShishenDuan1)
			shishenNum2 := make(map[string]int)
			for _, item := range paipanBaziMap.Data.ShishenMapPower {
				for i, f := range item.PowerBfbArr {
					if f >= 90 && f <= 100 {
						shishenNum2[item.ShiShenArr[i]] = 7
					} else if f > 80 && f < 90 {
						shishenNum2[item.ShiShenArr[i]] = 6
					} else if f > 65 && f <= 80 {
						shishenNum2[item.ShiShenArr[i]] = 5
					} else if f > 50 && f <= 65 {
						shishenNum2[item.ShiShenArr[i]] = 4
					} else if f > 35 && f <= 50 {
						shishenNum2[item.ShiShenArr[i]] = 3
					} else if f > 21 && f <= 35 {
						shishenNum2[item.ShiShenArr[i]] = 2
					} else if f >= 7 && f <= 21 {
						shishenNum2[item.ShiShenArr[i]] = 1
					}
				}
			}
			for k, v := range shishenNum2 {
				result.TiekouZhiduan.ShishenDuan2 = append(result.TiekouZhiduan.ShishenDuan2, slf.shishenNumMap[k][v-1])
			}
			slf.ShiPaiGeSort(result.TiekouZhiduan.ShishenDuan2)
		}

		// 2.16 一柱论命
		{
			rizhu := paipanAll.Sizhu[2]
			lunmins, err := slf.luncaiRepo.GetYizhuLunmin(ctx, rizhu)
			if err != nil {
				return nil, err
			}
			result.YizhuLunmin = &v1.MasterYizhuLunmin{
				Rizhu: rizhu,
				Lunmin: func() []string {
					var res []string
					for _, lm := range lunmins {
						res = append(res, lm.Lunmin)
					}
					return res
				}(),
			}
		}
		// 十神论断
		{
			rules, err := slf.mingliRuleRepo.GetAllEnabledRules(ctx, 11) // 11-十神论断
			if err != nil {
				return nil, err
			}
			ruleIDsMap := make(map[int64]struct{})
			for _, rule := range rules {
				ruleIDsMap[rule.ID] = struct{}{}
			}
			ruleIDs := make([]int64, 0)
			for id := range ruleIDsMap {
				ruleIDs = append(ruleIDs, id)
			}
			enums, err := func() (*v1.MingliRuleRelatedEnums, error) {
				var (
					err   error
					enums = &v1.MingliRuleRelatedEnums{}
				)
				enums.Zuodui, err = slf.enumsRepo.GetAllZuodui(ctx)
				if err != nil {
					return nil, err
				}
				enums.Wuxing, err = slf.enumsRepo.GetAllWuxing(ctx)
				if err != nil {
					return nil, err
				}
				enums.WuxingNamesMap = make(map[string]*model.Wuxing)
				for _, v := range enums.Wuxing {
					enums.WuxingNamesMap[v.Wuxing] = v
				}
				enums.Tiangan, err = slf.enumsRepo.GetAllTiangan(ctx)
				if err != nil {
					return nil, err
				}
				enums.TianganNamesMap = make(map[string]*model.Tiangan)
				for _, v := range enums.Tiangan {
					enums.TianganNamesMap[v.Tiangan] = v
				}
				enums.Dizhi, err = slf.enumsRepo.GetAllDizhi(ctx)
				if err != nil {
					return nil, err
				}
				enums.DizhiNamesMap = make(map[string]*model.Dizhi)
				for _, v := range enums.Dizhi {
					enums.DizhiNamesMap[v.Dizhi] = v
				}
				enums.Ganzhi, err = slf.enumsRepo.GetAllGanzhi(ctx)
				if err != nil {
					return nil, err
				}
				enums.GanzhiNamesMap = make(map[string]*model.Ganzhi)
				for _, v := range enums.Ganzhi {
					enums.GanzhiNamesMap[v.Name] = v
				}
				enums.GanzhiIDsMap = make(map[string]*model.Ganzhi)
				for _, v := range enums.Ganzhi {
					enums.GanzhiIDsMap[fmt.Sprintf("%d-%d", v.TianganID, v.DizhiID)] = v
				}
				enums.Shishen, err = slf.enumsRepo.GetAllShishen(ctx)
				if err != nil {
					return nil, err
				}
				enums.ShishenNamesMap = make(map[string]*model.Shishen)
				for _, v := range enums.Shishen {
					enums.ShishenNamesMap[v.Name] = v
				}
				enums.Nayin, err = slf.enumsRepo.GetAllNayin(ctx)
				if err != nil {
					return nil, err
				}
				enums.NayinNamesMap = make(map[string]*model.Nayin)
				for _, v := range enums.Nayin {
					enums.NayinNamesMap[v.Nayin] = v
				}
				enums.Xiji, err = slf.enumsRepo.GetAllXiji(ctx)
				if err != nil {
					return nil, err
				}
				enums.XijiNamesMap = make(map[string]*model.Xiji)
				for _, v := range enums.Xiji {
					enums.XijiNamesMap[v.Name] = v
				}
				enums.LunarMonth, err = slf.enumsRepo.GetAllLunarMonth(ctx)
				if err != nil {
					return nil, err
				}
				enums.LunarMonthMap = make(map[string]*model.LunarMonth)
				for _, v := range enums.LunarMonth {
					enums.LunarMonthMap[v.Name] = v
				}
				enums.LunarDate, err = slf.enumsRepo.GetAllLunarDate(ctx)
				if err != nil {
					return nil, err
				}
				enums.LunarDateMap = make(map[string]*model.LunarDate)
				for _, v := range enums.LunarDate {
					enums.LunarDateMap[v.Name] = v
				}
				enums.LunarTime, err = slf.enumsRepo.GetAllLunarTime(ctx)
				if err != nil {
					return nil, err
				}
				enums.LunarTimeMap = make(map[string]*model.LunarTime)
				for _, v := range enums.LunarTime {
					enums.LunarTimeMap[v.Name] = v
				}
				return enums, nil
			}()
			if err != nil {
				return nil, err
			}
			//jiuzhuData := jiuzhu.Data
			//if len(jiuzhuData.Tiangan) != 9 {
			//	return nil, errors.Errorf("tiangan length is not 9")
			//}
			paipanAll.Zhuxing[2] = "" // 日柱主星固定为空

			input := v1.MatchMingliRulesInput{
				Gender:    lo.Ternary(req.Gender == "男", 1, 2), // 1-男，2-女
				Canggan:   make([][]int64, len(paipanAll.Benqi)),
				Fuxing:    make([][]int64, len(paipanAll.BenqiShishen)),
				FuxingStr: make([][]string, len(paipanAll.BenqiShishen)),
				ZuoduiMap: make(map[int64]*model.Zuodui),
			}
			for _, zuodui := range enums.Zuodui {
				input.ZuoduiMap[zuodui.ID] = zuodui
			}
			for _, v := range paipanAll.Zhuxing {
				if v == "" {
					input.Zhuxing = append(input.Zhuxing, 0)
					input.ZhuxingStr = append(input.ZhuxingStr, "")
					continue
				}
				shishen, exist := enums.ShishenNamesMap[v]
				if !exist {
					return nil, errors.Errorf("shishen %s not found", v)
				}
				input.Zhuxing = append(input.Zhuxing, shishen.ID)
				input.ZhuxingStr = append(input.ZhuxingStr, v)
			}
			for _, v := range paipanAll.Tiangan {
				tiangan, exist := enums.TianganNamesMap[v]
				if !exist {
					return nil, errors.Errorf("tiangan %s not found", v)
				}
				input.Tiangan = append(input.Tiangan, tiangan.ID)
			}
			for _, v := range paipanAll.Dizhi {
				dizhi, exist := enums.DizhiNamesMap[v]
				if !exist {
					return nil, errors.Errorf("dizhi %s not found", v)
				}
				input.Dizhi = append(input.Dizhi, dizhi.ID)
			}
			for i, v := range paipanAll.Benqi {
				if v == "" {
					continue
				}
				tiangan, exist := enums.TianganNamesMap[v]
				if !exist {
					return nil, errors.Errorf("tiangan %s not found", v)
				}
				input.Canggan[i] = append(input.Canggan[i], tiangan.ID)
			}
			for i, v := range paipanAll.Zhongqi {
				if v == "" {
					continue
				}
				tiangan, exist := enums.TianganNamesMap[v]
				if !exist {
					return nil, errors.Errorf("tiangan %s not found", v)
				}
				input.Canggan[i] = append(input.Canggan[i], tiangan.ID)
			}
			for i, v := range paipanAll.Yuqi {
				if v == "" {
					continue
				}
				tiangan, exist := enums.TianganNamesMap[v]
				if !exist {
					return nil, errors.Errorf("tiangan %s not found", v)
				}
				input.Canggan[i] = append(input.Canggan[i], tiangan.ID)
			}
			for i, v := range paipanAll.BenqiShishen {
				if v == "" {
					continue
				}
				shishen, exist := enums.ShishenNamesMap[v]
				if !exist {
					return nil, errors.Errorf("shishen %s not found", v)
				}
				input.Fuxing[i] = append(input.Fuxing[i], shishen.ID)
				input.FuxingStr[i] = append(input.FuxingStr[i], v)
			}
			for i, v := range paipanAll.ZhongqiShishen {
				if v == "" {
					continue
				}
				shishen, exist := enums.ShishenNamesMap[v]
				if !exist {
					return nil, errors.Errorf("shishen %s not found", v)
				}
				input.Fuxing[i] = append(input.Fuxing[i], shishen.ID)
				input.FuxingStr[i] = append(input.FuxingStr[i], v)
			}
			for i, v := range paipanAll.YuqiShishen {
				if v == "" {
					continue
				}
				shishen, exist := enums.ShishenNamesMap[v]
				if !exist {
					return nil, errors.Errorf("shishen %s not found", v)
				}
				input.Fuxing[i] = append(input.Fuxing[i], shishen.ID)
				input.FuxingStr[i] = append(input.FuxingStr[i], v)
			}
			for _, v := range paipanAll.Nayin {
				nayin, exist := enums.NayinNamesMap[v]
				if !exist {
					return nil, errors.Errorf("nayin %s not found", v)
				}
				input.Nayin = append(input.Nayin, nayin.ID)
			}
			wuxing := strings.SplitN(paipanAll.Xiyongjichou, ",", 5)
			for _, v := range wuxing {
				wx, exist := enums.WuxingNamesMap[v]
				if !exist {
					return nil, errors.Errorf("wuxing %s not found", v)
				}
				input.Wuxing = append(input.Wuxing, wx.ID)
			}
			for i, gan := range input.Tiangan {
				for j, zhi := range input.Dizhi {
					if i == j {
						input.Ganzhi = append(input.Ganzhi, enums.GanzhiIDsMap[fmt.Sprintf("%d-%d", gan, zhi)].ID)
					}
				}
			}
			for i, zhu := range input.Zhuxing {
				for j, fus := range input.Fuxing {
					if i == j {
						if zhu != 0 {
							input.Shishen = append(input.Shishen, zhu)
						}
						input.Shishen = append(input.Shishen, fus...)
					}
				}
			}
			// 通过性别筛选（2-男，3-女）对应的条件
			conditions, err := slf.mingliRuleConditionRepo.GetMingliRuleConditionsByRuleIDsAndGender(ctx, ruleIDs, genderNum+1)
			if err != nil {
				return nil, err
			}
			conditionsMap := make(map[int64][]*model.MingliRuleCondition)
			for _, condition := range conditions {
				if _, exist := conditionsMap[condition.MingliRuleID]; !exist {
					conditionsMap[condition.MingliRuleID] = make([]*model.MingliRuleCondition, 0)
				}
				conditionsMap[condition.MingliRuleID] = append(conditionsMap[condition.MingliRuleID], condition)
			}
			res := &v1.MatchMingliRulesResponseData{}
			result.ShishenLunduan = res
			for _, rule := range rules {
				matched, err := slf.MatchMingliRule(ctx, rule, conditionsMap[rule.ID], &input)
				if err != nil {
					return nil, err
				}
				if matched != nil {
					res.Rules = append(res.Rules, matched)
				}
			}
			calendar, err := slf.enumsRepo.GetCalendarByDate(ctx, birthtime.Format("2006-01-02"))
			if err != nil {
				return nil, err
			}
			if calendar != nil {
				year, exist := enums.GanzhiNamesMap[calendar.Bazi1]
				if !exist {
					res.Birth = append(res.Birth, "")
				}
				res.Birth = append(res.Birth, year.Yiju)
				_, monthName, dayName := func(input string) (string, string, string) {
					input = strings.Replace(input, "闰", "", -1)
					if !strings.HasSuffix(input, "日") {
						input += "日"
					}
					re := regexp.MustCompile(`(.*?)月(.*?)日`)
					matches := re.FindStringSubmatch(input)
					if len(matches) < 3 {
						return input, "", ""
					}
					return input, matches[1] + "月", matches[2] + "日"
				}(calendar.LunarDate)
				month, exist := enums.LunarMonthMap[monthName]
				if !exist {
					res.Birth = append(res.Birth, "")
				}
				res.Birth = append(res.Birth, month.Result)
				day, exist := enums.LunarDateMap[dayName]
				if !exist {
					res.Birth = append(res.Birth, "")
				}
				res.Birth = append(res.Birth, day.Result)
				hourName := func(hour int) string {
					if hour >= 23 || hour < 1 {
						return "子"
					} else if hour < 3 {
						return "丑"
					} else if hour < 5 {
						return "寅"
					} else if hour < 7 {
						return "卯"
					} else if hour < 9 {
						return "辰"
					} else if hour < 11 {
						return "巳"
					} else if hour < 13 {
						return "午"
					} else if hour < 15 {
						return "未"
					} else if hour < 17 {
						return "申"
					} else if hour < 19 {
						return "酉"
					} else if hour < 21 {
						return "戌"
					} else {
						return "亥"
					}
				}(birthtime.Hour())
				hour, exist := enums.LunarTimeMap[hourName]
				if !exist {
					res.Birth = append(res.Birth, "")
				}
				res.Birth = append(res.Birth, hour.Result)
			}
		}

		/// 分割线============================
		// 创建排盘记录
		if !req.IgnoreRecord {
			ret, err := slf.irs.Query(req.IP)
			if err != nil {
				slf.logger.WithContext(ctx).Error("irs.Query", zap.Error(err))
			}
			result.ID, err = slf.paipanRecordRepo.CreatePaipanRecord(ctx, &model.PaipanRecord{
				UserID: func() string {
					if req.User != nil {
						return req.User.UserID
					} else {
						return ""
					}
				}(),
				Name:           req.Name,
				Gender:         lo.Ternary(req.Gender == "男", 1, 2),
				Birthtime:      birthtime,
				BirthtimeSun:   realSunTime,
				BirthtimeLunar: result.Mingzhu.BirthtimeLunar,
				Bazi:           result.Mingzhu.Bazi,
				Birthplace:     req.Birthplace,
				UserAgent:      req.UserAgent,
				IP:             ret.IP,
				Region:         ret.Region(),
				AppID:          8, // 专家版（排盘）
				AppPlatformID:  1, // 未知（默认）
				SaveTime:       time.Now(),
				Type:           1,
			})

			if err != nil {
				return nil, err
			}
		}
		return result, nil
	}
}

func (slf *masterService) MonthsScoreList(ctx context.Context, coefficient float32, year int, birthtime, gender string) ([]*v1.YunshiYueScore, error) {
	paipanDayun, err := slf.coronaCli.GetDayun(ctx, &corona.GetDayunRequest{
		Birthtime: birthtime,
		Gender:    gender,
	})
	if err != nil {
		return nil, err
	}
	if paipanDayun.DayunQishi > year {
		return nil, nil
	}
	var (
		timeStrCurrentYear = fmt.Sprintf("%04d-03-01 00:00:00", year)
		timeStrLastYear    = fmt.Sprintf("%04d-03-01 00:00:00", year-1)
	)
	paipanJieqiScoreCurrentYear, err := slf.coronaCli.GetJieqiScore(ctx, &corona.GetJieqiScoreRequest{
		Birthtime:   birthtime,
		Gender:      gender,
		CurrentTime: timeStrCurrentYear,
	})
	if err != nil {
		return nil, err
	}
	paipanJieqiScoreLastYear, err := slf.coronaCli.GetJieqiScore(ctx, &corona.GetJieqiScoreRequest{
		Birthtime:   birthtime,
		Gender:      gender,
		CurrentTime: timeStrLastYear,
	})
	if err != nil {
		return nil, err
	}
	type MonthObj struct {
		Jieqi   string    // 节气
		Score   int       // 分数
		TimeStr string    // 时间字符串
		Time    time.Time // 时间
		Ganzhi  string    // 干支
		LnScore int       // 流年分数
		DyScore int       // 大运分数
	}
	var months []MonthObj
	splitN := strings.SplitN(paipanJieqiScoreLastYear.PaiyueJq[12-1], ": ", 2)
	parse, err := time.Parse("2006-01-02 15:04:05", splitN[1])
	if err != nil {
		return nil, err
	}
	months = append(months, MonthObj{
		Jieqi:   splitN[0],
		Score:   paipanJieqiScoreLastYear.NowMonthFen[12-1],
		TimeStr: splitN[1],
		Time:    parse,
		Ganzhi:  paipanJieqiScoreLastYear.MonthGanzhiList[12-1],
	})
	for i, jieqi := range paipanJieqiScoreCurrentYear.PaiyueJq {
		splitN := strings.SplitN(jieqi, ": ", 2)
		parse, err := time.Parse("2006-01-02 15:04:05", splitN[1])
		if err != nil {
			return nil, err
		}
		months = append(months, MonthObj{
			Jieqi:   splitN[0],
			Score:   paipanJieqiScoreCurrentYear.NowMonthFen[i],
			TimeStr: splitN[1],
			Time:    parse,
			Ganzhi:  paipanJieqiScoreCurrentYear.MonthGanzhiList[i],
		})
	}
	paipanDayunLiulianScore, err := slf.coronaCli.GetDayunLiunianScore(ctx, &corona.GetDayunLiunianScoreRequest{
		Birthtime: birthtime,
		Gender:    gender,
	})
	if err != nil {
		return nil, err
	}
	var (
		lnIndex1 = months[0].Time.Year() - paipanDayun.DayunQishi // 流年索引1（1月取上一年）
		lnIndex2 = months[1].Time.Year() - paipanDayun.DayunQishi // 流年索引2（2月及以后取当前年）
		lnScore1 = paipanDayunLiulianScore.Lnian[lnIndex1]
		lnScore2 = paipanDayunLiulianScore.Lnian[lnIndex2]
		dyScore1 = paipanDayunLiulianScore.Dyun[lnIndex1/5]
		dyScore2 = paipanDayunLiulianScore.Dyun[lnIndex2/5]
	)
	for i, obj := range months {
		if i == 12 {
			continue
		}
		if i == 0 {
			obj.LnScore = lnScore1
			obj.DyScore = dyScore1
		} else {
			obj.LnScore = lnScore2
			obj.DyScore = dyScore2
		}
	}
	var liuyue []*v1.YunshiYueScore
	for i, obj := range months {
		if i == 12 { // 第13个月只返回节气（用于前端显示最佳/谨慎月份时间范围）
			liuyue = append(liuyue, &v1.YunshiYueScore{
				Jieqi:     obj.Jieqi,
				JieqiTime: obj.TimeStr,
			})
			continue
		}
		var (
			lyOverallScore = (float32(obj.DyScore)*coefficient+float32(obj.LnScore)*(1-coefficient))*coefficient + float32(obj.Score)*(1-coefficient)
			lyFinalScore   = lyOverallScore*0.5 + 50
		)
		liuyue = append(liuyue, &v1.YunshiYueScore{
			Jieqi:        obj.Jieqi,
			Ganzhi:       obj.Ganzhi,
			JieqiTime:    obj.TimeStr,
			InitialScore: float32(obj.Score),
			OverallScore: lyOverallScore,
			FinalScore:   lyFinalScore,
		})
	}
	return liuyue, nil
}
