package service

import (
	"strings"
	"zodiacus/pkg/array"
	"zodiacus/third_party/corona"
)

func (slf *masterService) ShentiJiankang(paipanAll *corona.GetAllResponse, genderNum int, comb23 func([]string, ...string) ([][]string, [][]string)) []string {
	var (
		keys    []string
		tiangan = paipanAll.Tiangan[:4] // 天干
		dizhi   = paipanAll.Dizhi[:4]   // 地支
		dizhiWx = []string{
			slf.GetWuxingByDizhi(dizhi[0]),
			slf.GetWuxingByDizhi(dizhi[1]),
			slf.GetWuxingByDizhi(dizhi[2]),
			slf.GetWuxingByDizhi(dizhi[3]),
		}
		ganzhi = []string{
			paipanAll.Tiangan[0] + paipanAll.Dizhi[0],
			paipanAll.Tiangan[1] + paipanAll.Dizhi[1],
			paipanAll.Tiangan[2] + paipanAll.Dizhi[2],
			paipanAll.Tiangan[3] + paipanAll.Dizhi[3],
		}
		benqi, zhongqi, yuqi = paipanAll.Benqi[:4], paipanAll.Zhongqi[:4], paipanAll.Yuqi[:4]
		cangan               = array.Merge(benqi, zhongqi, yuqi)
		rigan                = paipanAll.Tiangan[2]          // 日干
		rgwx                 = slf.GetWuxingByTiangan(rigan) // 日干五行
		riyuan               = paipanAll.Riyuan              // 日元旺衰
		xycjx                = strings.SplitN(paipanAll.Xiyongjichou, ",", 5)
		xiyong               = xycjx[:2]  // 喜用
		chouji               = xycjx[2:4] // 仇忌
		ssg                  = corona.NewShishenGetter(paipanAll.Zhuxing[:4], paipanAll.BenqiShishen[:4], paipanAll.ZhongqiShishen[:4], paipanAll.YuqiShishen[:4])
		shishenPowerMap      = paipanAll.ShishenPowerMap      // 十神能量
		wuxingPowerMap       = paipanAll.WuxingPowerMap       // 五行能量
		shishenNumMap        = paipanAll.ShishenNumMap        // 十天干能量
		xingyun              = paipanAll.GetXingyunListLiuAll // 星运
		nayinWuxing          = paipanAll.NayinWuxing[:4]      // 纳音五行
	)

	/*
		1. 根据原局命理的旺衰，判断
		  - 若值为身强、从强，则显示【ST001】“日主高强，一生身体健康，较少疾病。”
		  - 若值为平和、偏弱、偏强，则显示【ST002】“日主中和，平生身体不很健壮，偶尔有小病，但都没什么事。”
		  - 若值为从弱、身弱，则显示【ST003】“日主衰弱，体质比较差，平常多锻炼身体，可免疾病困扰。”
	*/
	{
		if array.Has([]string{"身强", "从强"}, riyuan) {
			keys = append(keys, "[ST001]")
		} else if array.Has([]string{"平和", "偏弱", "偏强"}, riyuan) {
			keys = append(keys, "[ST002]")
		} else if array.Has([]string{"从弱", "身弱"}, riyuan) {
			keys = append(keys, "[ST003]")
		}
	}
	/*
		2. 条件：满足下述每一条条件
		  - 条件1：原局四柱干十神、藏干十神中，比肩的数量大于等于3个
		  - 条件2：日干对应的五行，为原局命理的喜用五行
		- 则显示：【ST004】“比肩叠叠为喜用，一生少病 。”
	*/
	{
		if ssg.NumMap["比肩"] >= 3 && array.Has(xiyong, rgwx) {
			keys = append(keys, "[ST004]")
		}
	}
	/*
		3. 条件：满足下述每一条条件
		  - 条件1：原局转换十天干能量中，丙、丁的能量值之和大于等于150
		  - 条件2：原局转换十天干能量中，庚、辛的能量值之和小于40
		  - 条件3：原局天干、藏干中庚、辛的数量之和大于0
		- 则显示：【ST005】“  金弱遇火旺，血疾无疑，主酒色成疾。”
	*/
	{
		if func() bool {
			if shishenNumMap["丙"]+shishenNumMap["丁"] < 150 {
				return false
			}
			if shishenNumMap["庚"]+shishenNumMap["辛"] >= 40 {
				return false
			}
			if array.Count(cangan, "庚", "辛") == 0 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST005]")
		}
	}
	/*
		4. 条件：满足下述每一条条件
		  - 条件1：原局转换十天干能量中，甲、乙的能量值之和大于等于150
		  - 条件2：原局转换十天干能量中，戊、己的能量值之和小于等于40
		  - 条件3：原局天干、藏干中戊、己的数量之和大于0
		- 则显示：【ST006】“  土虚木旺之乡，定伤脾胃。”
	*/
	{
		if func() bool {
			if shishenNumMap["甲"]+shishenNumMap["乙"] < 150 {
				return false
			}
			if shishenNumMap["戊"]+shishenNumMap["己"] > 40 {
				return false
			}
			if array.Count(cangan, "戊", "己") == 0 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST006]")
		}
	}
	/*
		5. 条件：满足下述每一条条件
		  - 条件1：原局转换十天干能量中，庚、辛的能量值之和大于等于150
		  - 条件2：原局转换十天干能量中，甲、乙的能量值之和小于等于40
		  - 条件3：原局天干、藏干中甲、乙的数量之和大于0
		- 则显示：【ST007】“  木被金伤，筋骨腰肋疼痛。 。”
	*/
	{
		if func() bool {
			if shishenNumMap["庚"]+shishenNumMap["辛"] < 150 {
				return false
			}
			if shishenNumMap["甲"]+shishenNumMap["乙"] > 40 {
				return false
			}
			if array.Count(cangan, "甲", "乙") == 0 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST007]")
		}
	}
	/*
		6. 条件：满足下述每一条条件
		  - 条件1：原局转换十天干能量中，壬、癸的能量值之和大于等于150
		  - 条件2：原局转换十天干能量中，丙、丁的能量值之和小于等于40
		  - 条件3：原局天干、藏干中丙、丁的数量之和大于0
		- 则显示：【ST008】“ 火遭水克，眼目昏暗。 ”
	*/
	{
		if func() bool {
			if shishenNumMap["壬"]+shishenNumMap["癸"] < 150 {
				return false
			}
			if shishenNumMap["丙"]+shishenNumMap["丁"] > 40 {
				return false
			}
			if array.Count(cangan, "丙", "丁") == 0 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST008]")
		}
	}
	/*
		7. 条件：若日柱星运为长生，则显示【ST009】“日坐长生，身健少病。”
	*/
	{
		if xingyun[2] == "长生" {
			keys = append(keys, "[ST009]")
		}
	}
	/*
		8. 条件：满足下述每一条条件
		  - 条件1：年柱干支对应的纳音的五行为火
		  - 条件2：月柱地支为巳、午、未中的一个
		- 则显示：【ST0010】“  火命夏生，体健聪明。(本处以年柱纳音五行来推算)”
	*/
	{
		if func() bool {
			if nayinWuxing[0] != "火" {
				return false
			}
			if !array.Has([]string{"巳", "午", "未"}, dizhi[1]) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST0010]")
		}
	}
	/*
		9. 条件：满足下述每一条条件
		  - 条件1：年柱干支对应的纳音的五行为土
		  - 条件2：月柱地支为巳、午、未中的一个
		- 则显示：【ST011】“  土命夏生，体健聪明。(本处以年柱纳音五行来推算)”
	*/
	{
		if func() bool {
			if nayinWuxing[0] != "土" {
				return false
			}
			if !array.Has([]string{"巳", "午", "未"}, dizhi[1]) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST011]")
		}
	}
	/*
		5. 条件：满足下述每一条条件
		  - 条件1：年柱干支对应的纳音的五行为金
		  - 条件2：月柱地支为申、酉、戌中的一个
		- 则显示：【ST012】“金命秋生，体健聪明 。(本处以年柱纳音五行来推算)”
	*/
	{
		if func() bool {
			if nayinWuxing[0] != "金" {
				return false
			}
			if !array.Has([]string{"申", "酉", "戌"}, dizhi[1]) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST012]")
		}
	}
	/*
		6. 条件：满足下述每一条条件
		  - 条件1：年柱干支对应的纳音的五行为水
		  - 条件2：月柱地支为亥、子、丑中的一个
		- 则显示：【ST013】“水命冬生，体健聪明。(本处以年柱纳音五行来推算)”
	*/
	{
		if func() bool {
			if nayinWuxing[0] != "水" {
				return false
			}
			if !array.Has([]string{"亥", "子", "丑"}, dizhi[1]) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST013]")
		}
	}
	/*
		7. 条件：满足下述每一条条件
		  - 条件1：年柱干支对应的纳音的五行为木
		  - 条件2：月柱地支为寅、卯、辰中的一个
		- 则显示：【ST014】“  木命春生，体健聪明。(本处以年柱纳音五行来推算)”
	*/
	{
		if func() bool {
			if nayinWuxing[0] != "木" {
				return false
			}
			if !array.Has([]string{"寅", "卯", "辰"}, dizhi[1]) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST014]")
		}
	}
	/*
		8. 条件：满足下述每一条条件
		  - 条件1：月柱地支为亥、子、丑中的一个
		  - 条件2：原局自主能量中，丙、丁的能量之和，小于10
		- 则显示：【ST015】“ 冬生无火，下肢寒冷 。”
	*/
	{
		if func() bool {
			if !array.Has([]string{"亥", "子", "丑"}, dizhi[1]) {
				return false
			}
			if shishenNumMap["丙"]+shishenNumMap["丁"] >= 10 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST015]")
		}
	}
	/*
		9. 条件：满足下述每一条条件
		  - 条件1：原局日柱或时柱存在神煞 金神
		  - 条件2：原局天干或地支藏干，存在壬或癸
		- 则显示：【ST016】“ 金神遇水，贫寒带疾之人。”
	*/
	{
		if func() bool {
			if !array.Has(array.Merge(paipanAll.Shensha4[2:]...), "金神") {
				return false
			}
			if !array.Has(array.Merge(tiangan, cangan), "壬", "癸") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST016]")
		}
	}
	/*
		10. 条件：原局年、月、日、时四柱中任一一柱满足以下条件
		  - 条件1：该柱神煞存在天德贵人或月德贵人
		  - 条件2：该柱地支对应的五行，为原局命理中命主的喜用五行
		- 则显示：【ST017】“ 柱临天月二德且为喜用，终生少病 。”
	*/
	{
		if func() bool {
			for _, idx := range []int{0, 1, 2, 3} {
				if !array.Has(paipanAll.Shensha4[idx], "天德贵人", "月德贵人") {
					continue
				}
				if !array.Has(xiyong, slf.GetWuxingByDizhi(dizhi[idx])) {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[ST017]")
		}
	}
	/*
		11. 条件：原局年、月、日、时四柱中任一一柱满足以下条件
		  - 条件1：该柱神煞存在羊刃
		  - 条件2：该柱天干十神为正印
		- 则显示：【ST018】“  羊刃逢印，终贵有病。”
	*/
	{
		if func() bool {
			for _, idx := range []int{0, 1, 2, 3} {
				if !array.Has(paipanAll.Shensha4[idx], "羊刃") {
					continue
				}
				if ssg.Tg[idx] != "正印" {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[ST018]")
		}
	}
	/*
		12. 条件：满足下述每一条条件
		  - 条件1：性别为女
		  - 条件2：原局年、月、日、时四柱中任一一柱满足以下条件
		    - 该柱神煞存在天德贵人或月德贵人
		    - 该柱天干对应的五行，为原局命理的喜用五行
		- 则显示：【ST019】“ 妇逢天月二德且为喜用，无产厄。”
	*/
	{
		if func() bool {
			if paipanAll.Gender != "女" {
				return false
			}
			for _, idx := range []int{0, 1, 2, 3} {
				if !array.Has(paipanAll.Shensha4[idx], "天德贵人", "月德贵人") {
					continue
				}
				if !array.Has(xiyong, slf.GetWuxingByTiangan(tiangan[idx])) {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[ST019]")
		}
	}
	/*
		13. 条件：满足下述每一条条件
		  - 条件1：年柱、月柱、时柱天干十神，存在偏印
		  - 条件2：原局命理中天干十神、地支藏干十神中，存在的偏印的数量大于等于三个
		  - 条件3：原局命理中偏印对应的五行（即生日干的五行），为仇忌五行
		- 则显示：【ST020】“ 枭神重重为忌，多得肺病。”
	*/
	{
		if func() bool {
			if !array.Has(ssg.TgListByIdx(0, 1, 3), "偏印") {
				return false
			}
			if ssg.NumMap["偏印"] < 3 {
				return false
			}
			if !array.Has(chouji, slf.YinXiao(tiangan[2])) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST020]")
		}
	}
	/*
		14. 条件：满足下述每一条条件
		  - 条件1：月柱天干十神为正印
		  - 条件2：月干天干五行，不为原局命理中仇忌神
		- 则显示：【ST021】“   月干为正印，心地仁慈善良，聪颖健康，一生少病安全。”
	*/
	{
		if func() bool {
			if ssg.Tg[1] != "正印" {
				return false
			}
			if array.Has(chouji, slf.GetWuxingByTiangan(tiangan[1])) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST021]")
		}
	}
	/*
		15. 条件：以下条件年柱、月柱、时柱任意一柱满足即可
		  - 条件1：天干十神为正印
		  - 条件2：该柱神煞存在羊刃
		  - 条件3：该柱地支对应的五行不为原局命理的喜用五行
		- 则显示：【ST022】“ 正印坐羊刃，身心多伤害。”
	*/
	{
		if func() bool {
			for _, idx := range []int{0, 1, 3} {
				if ssg.Tg[idx] != "正印" {
					continue
				}
				if !array.Has(paipanAll.Shensha4[idx], "羊刃") {
					continue
				}
				if array.Has(xiyong, slf.GetWuxingByDizhi(dizhi[idx])) {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[ST022]")
		}
	}
	/*
		16. 条件：满足下述每一条条件
		  - 条件1： 原局命理十神能量中，五行金的能量值小于40
		  - 条件2：原局命理十神能量中，五行水的能量值小于40
		- 则显示：【ST023】“  金水枯伤，肾必虚。”
	*/
	{
		if func() bool {
			if shishenPowerMap["金"] >= 40 {
				return false
			}
			if shishenPowerMap["水"] >= 40 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST023]")
		}
	}
	/*
		17. 条件：满足下述每一条条件
		  - 条件1：年柱天干十神、月柱天干十神、时柱天干十神，均为印枭
		  - 条件2：印枭对应的五行，不为原局命理的仇忌神
		- 则显示：【ST024】“ 年、月、时天干皆印者，寿高无比，正直清白。”
	*/
	{
		if func() bool {
			if !array.Has([]string{"正印", "偏印"}, ssg.TgListByIdx(0, 1, 3)...) {
				return false
			}
			if array.Has(chouji, slf.YinXiao(rgwx)) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST024]")
		}
	}
	/*
		18. 条件：满足下述每一条条件
		  - 条件1：原局命理日柱神煞中，存在羊刃
		  - 条件2：原局命理中日柱地支，发生六冲、相刑（包括三刑）
		  - 条件3：月柱天干十神或地支十神，为比肩
	*/
	{
		if func() bool {
			if !array.Has(paipanAll.Shensha4[2], "羊刃") {
				return false
			}
			if !func() bool {
				two, three := comb23(dizhi, dizhi[2])
				for _, arr := range two {
					if slf.IsDizhiXiangchong(arr[0], arr[1]) {
						return true
					}
					if slf.IsDizhiXiangxing(arr[0], arr[1]) {
						return true
					}
				}
				for _, arr := range three {
					if slf.IsDizhiSanxing(arr[0], arr[1], arr[2]) {
						return true
					}
				}
				return false
			}() {
				return false
			}
			if !array.Has(ssg.TgDzListByIdx(1), "比肩") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST025]")
		}
	}
	/*
		19. 条件：满足下述每一条条件
		  - 条件1：原局命理中，年柱天干十神和地支十神均为伤官
		  - 条件2：伤官对应的五行为原局命理的仇忌神五行
		- 则显示：【ST026】“年干支皆伤官，自己因痼疾而短命，富亦不久。”
	*/
	{
		if func() bool {
			if !array.EqualTarget("伤官", ssg.Tg[0], ssg.Dz[0]) {
				return false
			}
			if !array.Has(chouji, paipanAll.ShishenWuxingMap["伤官"]) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST026]")
		}
	}
	/*
		20. 条件：满足下述每一条条件
		  - 条件1：原局命理四柱天干，存在庚
		  - 条件2：原局命理四柱地支，存在寅
		  - 条件3：原局命理四柱地支，存在卯
		- 则显示：【ST027】“ 天干有庚，地支有寅卯二字，防兽咬，或下肢有病伤。”
	*/
	{
		if func() bool {
			if !array.Has(tiangan, "庚") {
				return false
			}
			if !array.Has(dizhi, "寅", "卯") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST027]")
		}
	}
	/*
		21. 条件：满足下述每一条条件
		  - 条件1：原局命理四柱地支，存在申
		  - 条件2：原局命理四柱地支，存在巳
		- 则显示：【ST028】“ 支有申、巳二字，四肢受过伤或手脚有病，如关节炎、肩周炎之类。”
	*/
	{
		if func() bool {
			if !array.Has(dizhi, "申", "巳") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST028]")
		}
	}
	/*
		22. 条件：满足下述每一条条件
		  - 条件1：原局命理四柱天干，存在庚
		  - 条件2：原局命理四柱天干，存在辛
		  - 条件3：原局命理四柱地支，存在申
		  - 条件4：原局命理四柱地支，存在酉
		- 则显示：【ST029】“ 柱有庚、辛、申、酉四字，有血光杀伤或肢体伤残之祸。”
	*/
	{
		if func() bool {
			if !array.Has(tiangan, "庚", "辛") {
				return false
			}
			if !array.Has(dizhi, "申", "酉") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST029]")
		}
	}
	/*
		23. 条件：满足下述每一条条件
		  - 条件1：原局命理四柱天干，存在庚，或者存在辛
		  - 条件2：原局命理四柱天干，存在丙，或者存在丁
		- 则显示：【ST030】“ 天干有庚或辛，同时又有丙或丁，容易近视。”
	*/
	{
		if func() bool {
			if !array.HasAny(tiangan, "庚", "辛") {
				return false
			}
			if !array.HasAny(tiangan, "丙", "丁") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST030]")
		}
	}
	/*
		24. 条件：满足下述每一条条件件
		  - 条件1：原局命理四柱地支，存在卯
		  - 条件2：原局命理四柱地支，存在辰
		- 则显示：【ST031】“柱有卯辰二字（易遭官非牢狱）。”
	*/
	{
		if func() bool {
			if !array.Has(dizhi, "卯", "辰") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST031]")
		}
	}
	/*
		25. 若原局命理中天干十神、地支藏干十神中，偏印的数量大于等于3
		- 则显示：【ST032】“ 偏印多，肠胃不好。”
	*/
	{
		if func() bool {
			if ssg.NumMap["偏印"] < 3 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST032]")
		}
	}
	/*
		26. 条件：满足下述每一条条件
		  - 条件1：原局命理四柱天干，存在戊
		  - 条件2：原局命理四柱天干，存在己
		  - 条件3：原局命理四柱地支，存在卯
		  - 条件4：原局命理四柱地支，存在寅
		- 则显示：【ST033】“ 干有戊己，支有寅卯，家中有人患四肢之病，瘫痪之疾，长年卧病在床。”
	*/
	{
		if func() bool {
			if !array.Has(tiangan, "戊", "己") {
				return false
			}
			if !array.Has(dizhi, "寅", "卯") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST033]")
		}
	}
	/*
		27. 条件：满足下述每一条条件
		  - 条件1：原局日柱地支十神、时柱天干十神、时柱地支十神，存在正官
		  - 条件2：原局日柱地支十神、时柱天干十神、时柱地支十神，存在七杀
		  - 条件3：官杀对应五行，为原局命理的仇忌神五行
		- 则显示：【ST034】“ 日时官杀杂乱为忌，疾病交加。”
	*/
	{
		if func() bool {
			arr := []string{ssg.Dz[2], ssg.Tg[3], ssg.Dz[3]}
			if !array.Has(arr, "正官", "七杀") {
				return false
			}
			if !array.Has(chouji, paipanAll.ShishenWuxingMap["官杀"]) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST034]")
		}
	}
	/*
		28. 条件：满足下述每一条条件
		  - 条件1：原局命理中，日柱旺衰为从弱，或者身弱
		  - 条件2：原局命理十神能量中，食伤的能量大于等于90
		  - 条件3：原局四柱天干十神、地支藏干十神中，食伤的数量大于等于3
		- 则显示：【ST035】“日弱食伤重，头昏之疾。”
	*/
	{
		if func() bool {
			if !array.Has([]string{"从弱", "身弱"}, riyuan) {
				return false
			}
			if shishenPowerMap["食伤"] < 90 {
				return false
			}
			if ssg.NumMap["食伤"] < 3 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST035]")
		}
	}
	/*
		29. 条件：满足下述每一条条件
		  - 条件1：原局命理中，干支数量中，火行干支数量大于等于3（不计藏干个数）
		  - 条件2：原局命理中，火行为原局命理日主喜用神的仇忌神
		- 则显示：【ST036】“ 四柱火多为忌，少年脓血之疾。”
	*/
	{
		if func() bool {
			if array.Count(array.Merge(tiangan, dizhi), "丙", "丁") < 3 {
				return false
			}
			if !array.Has(chouji, "火") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST036]")
		}
	}
	/*
		30. 若原局命理四柱神煞中，羊刃的数量大于等于3
		- 则显示：【ST037】“  羊刃重重三四，必防盲聋之疾。”
	*/
	{
		if array.Count(array.Merge(paipanAll.Shensha4...), "羊刃") >= 3 {
			keys = append(keys, "[ST037]")
		}
	}
	/*
		31. 条件：满足下述每一条条件
		  - 条件1：原局四柱天干十神、地支藏干十神中，伤官数量大于等于3
		  - 条件2：伤官对应的五行，为原局命理日主的仇忌神五行
		- 则显示：【ST038】“ 伤官重重，防腰脚骨伤残。”
	*/
	{
		if func() bool {
			if ssg.NumMap["伤官"] < 3 {
				return false
			}
			if !array.Has(chouji, paipanAll.ShishenWuxingMap["伤官"]) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST038]")
		}
	}
	/*
		32. 条件：满足下述每一条条件
		  - 条件1：原局命理中，日主性别为女
		  - 条件2：原局命理四柱地支中，存在卯
		  - 条件3：原局命理四柱地支中，存在酉
		- 则显示：【ST039】“ 女犯卯酉主堕胎克子，肋疾血刺。”
	*/
	{
		if func() bool {
			if paipanAll.Gender != "女" {
				return false
			}
			if !array.Has(dizhi, "卯", "酉") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST039]")
		}
	}
	/*
		33. 若 原局命理四柱地支中，子、亥的数量大于等于2
		- 则显示：【ST040】“柱中亥子多者，主疝气。”
	*/
	{
		if array.Count(dizhi, "子", "亥") >= 2 {
			keys = append(keys, "[ST040]")
		}
	}
	/*
		34. 条件：满足下述每一条条件
		  - 条件1：原局命理中，发生了相刑（含三刑和自刑）
		  - 条件2：原局命理中，相刑发生次数大于等于两次
		- 则显示：【ST041】“刑多终有伤残。”
	*/
	{
		if func() bool {
			two, three := comb23(dizhi)
			count := 0
			for _, arr := range two {
				if slf.IsDizhiXiangxing(arr[0], arr[1]) {
					count++
				}
			}
			for _, arr := range three {
				if slf.IsDizhiSanxing(arr[0], arr[1], arr[2]) {
					count++
				}
			}
			if count < 2 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST041]")
		}
	}
	/*
		35. 条件：满足下述每一条条件
		  - 条件1：原局命理中，日主旺衰为身强、偏强、从强
		  - 条件2：原局命理十神能量中，财才的能量大于等于90
		  - 条件3：原局命理十神能量中，官杀的能量大于等于90
		- 则显示：【ST042】“日旺财官盛，折伤之疾。”
	*/
	{
		if func() bool {
			if !array.Has([]string{"身强", "偏强", "从强"}, riyuan) {
				return false
			}
			if shishenPowerMap["财才"] < 90 {
				return false
			}
			if shishenPowerMap["官杀"] < 90 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST042]")
		}
	}
	/*
		36. 条件：以下条件任意一条满足即可
		  - 条件1：日柱地支十神为偏印
		  - 条件2：原局命理十神能量中，偏印的能量大于等于90
		- 则显示：【ST043】“ 日坐枭或枭重者，因食而疾。”
	*/
	{
		if func() bool {
			if ssg.Dz[2] != "偏印" {
				return false
			}
			if shishenPowerMap["偏印"] < 90 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST043]")
		}
	}
	/*
		37. 条件：满足下述每一条条件
		  - 条件1：日柱天干为庚，或者辛
		  - 条件2：原局命理五行能量中，水的能量大于等于150
		- 则显示：【ST044】“ 金遇旺水，伤筋骨之疾。”
	*/
	{
		if func() bool {
			if !array.Has([]string{"庚", "辛"}, tiangan[2]) {
				return false
			}
			if wuxingPowerMap["水"] < 150 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST044]")
		}
	}
	/*
		38. 条件：满足下述每一条条件
		  - 条件1：日柱天干为甲
		  - 条件2：原局四柱天干、地支，五行为火的干支数量大于等于4（不计藏干个数）
		  - 条件3：火行，为原局日主的仇忌五行
		- 则显示：【ST045】“ 甲木遇火多为忌，多犯神经之疾。”
	*/
	{
		if func() bool {
			if tiangan[2] != "甲" {
				return false
			}
			if array.Count(array.Merge(tiangan, dizhi), "丙", "丁") < 4 {
				return false
			}
			if !array.Has(chouji, "火") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST045]")
		}
	}
	/*
		39. 条件：满足下述每一条条件
		  - 条件1：原局命理四柱天干，存在丙，或存在丁
		  - 条件2：原局命理五行能量中，火的能量大于等于150
		  - 条件3：火行，为原局命理日主的仇忌神
		- 则显示：【ST046】“丙丁火旺为忌，疾病难防，聋哑中风。”
	*/
	{
		if func() bool {
			if !array.Has(tiangan, "丙", "丁") {
				return false
			}
			if wuxingPowerMap["火"] < 150 {
				return false
			}
			if !array.Has(chouji, "火") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST046]")
		}
	}
	/*
		40. 条件：满足下述每一条条件
		  - 条件1：原局命理日主性别为女
		  - 条件2：原局四柱神煞中羊刃的数量，大于等于2
		- 则显示：【ST047】“ 羊刃多，有产厄、月经过多之疾，中年后主冷疾。”
	*/
	{
		if func() bool {
			if paipanAll.Gender != "女" {
				return false
			}
			if array.Count(array.Merge(paipanAll.Shensha4...), "羊刃") < 2 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST047]")
		}
	}
	/*
		41. 条件：满足下述每一条条件
		  - 条件1：原局命理四柱干支中，丁的数量与午的数量之后大于等于3
		  - 条件2：原局命理四柱地支中，存在未
		- 则显示：【ST048】“ 丁午多有未者，头疮或疤痕、秃疮之疾。”
	*/
	{
		if func() bool {
			if array.Count(array.Merge(tiangan, dizhi), "丁", "午") < 3 {
				return false
			}
			if !array.Has(dizhi, "未") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST048]")
		}
	}
	/*
		42. 条件：满足下述任一条件
		  - 条件1：原局命理天干十神中，存在偏印
		  - 条件2：原局命理天干十神中，存在食神
		- 则显示：【ST049】“ 枭夺食而有病。”
	*/
	{
		if func() bool {
			if !array.Has(ssg.Tg, "偏印", "食神") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST049]")
		}
	}
	/*
		43. 条件：满足下述每一条条件
		  - 条件1：原局命理天干十神中，存在偏印
		  - 条件2：原局命理天干十神中，存在食神
		  - 条件3：原局日主性别为女
		- 则显示：【ST050】“ 食神逢枭多产厄。”
	*/
	{
		if func() bool {
			if !array.Has(ssg.Tg, "偏印", "食神") {
				return false
			}
			if paipanAll.Gender != "女" {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST050]")
		}
	}
	/*
		44. 条件：满足下述每一条条件
		  - 条件1：原局四柱天干十神中，偏印的数量大于等于2
		  - 条件2：原局命理十神能量中，偏印的能量大于等于90
		- 则显示：“   枭逢枭旺，不病则灾。。”
	*/
	{
		if func() bool {
			if ssg.NumMap["偏印"] < 2 {
				return false
			}
			if shishenPowerMap["偏印"] < 90 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST051]")
		}
	}
	/*
		45. 条件：原局四柱中任意一柱满足以下条件
		  - 条件1：该柱神煞，存在羊刃
		  - 条件2：该柱天干十神，为七杀
		- 则显示：“   犯羊刃杀者，多目疾。”
	*/
	{
		if func() bool {
			for _, idx := range []int{0, 1, 2, 3} {
				if !array.Has(paipanAll.Shensha4[idx], "羊刃") {
					continue
				}
				if ssg.Tg[idx] != "七杀" {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[ST052]")
		}
	}
	/*
		46. 条件：原局四柱中任意一柱满足以下条件
		  - 条件1：该柱神煞，存在羊刃
		  - 条件2：该柱天干十神，为劫财
		- 则显示：“  羊刃劫财，疾病破相。”
	*/
	{
		if func() bool {
			for _, idx := range []int{0, 1, 2, 3} {
				if !array.Has(paipanAll.Shensha4[idx], "羊刃") {
					continue
				}
				if ssg.Tg[idx] != "劫财" {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[ST053]")
		}
	}
	/*
		47. 条件：原局四柱中任意一柱满足以下条件
		  - 条件1：该柱神煞，存在羊刃
		  - 条件2：该柱天干十神，为劫财
		  - 条件3：原局四柱干十神、地支藏干十神均无财才
		- 则显示：“   劫财羊刃柱无财，不贫则残疾在身。”
	*/
	{
		if func() bool {
			for _, idx := range []int{0, 1, 2, 3} {
				if !array.Has(paipanAll.Shensha4[idx], "羊刃") {
					continue
				}
				if ssg.Tg[idx] != "劫财" {
					continue
				}
				if array.Has(ssg.All(), "正财", "偏财") {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[ST054]")
		}
	}
	/*
		48. 若原局四柱地支，为亥、子、巳、午四个，无顺序要求
		- 则显示：“  亥子加巳午，眼疾。 。”
	*/
	{
		if array.Has(dizhi, "亥", "子", "巳", "午") {
			keys = append(keys, "[ST055]")
		}
	}
	/*
		49. 条件：满足下述每一条条件
		  - 条件1：原局命理中，日主性别为女
		  - 条件2：年柱天干十神为伤官
		  - 条件3：伤官对应五行，为原局命理的仇忌神
		- 则显示：“   女年犯伤官，产厄带疾。”
	*/
	{
		if func() bool {
			if paipanAll.Gender != "女" {
				return false
			}
			if ssg.Tg[0] != "伤官" {
				return false
			}
			if !array.Has(chouji, paipanAll.ShishenWuxingMap["伤官"]) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST056]")
		}
	}
	/*
		50. 条件：满足下述每一条条件
		  - 条件1：原局命理中，日主性别为女
		  - 条件2：日柱、时柱神煞存在勾绞煞
		  - 条件3：条件2中，存在勾绞煞的柱中，任意一柱地支为原局命理的仇忌神
		- 则显示：“  日时犯勾绞为忌，多产厄。”
	*/
	{
		if func() bool {
			if paipanAll.Gender != "女" {
				return false
			}
			for _, idx := range []int{2, 3} {
				if !array.Has(paipanAll.Shensha4[idx], "勾绞煞") {
					continue
				}
				if !array.Has(chouji, dizhi[idx]) {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[ST057]")
		}
	}
	/*
		51. 条件：满足下述每一条条件
		  - 条件1：原局命理天干十神中，存在七杀
		  - 条件2：原局命理十神能量中，七杀的能量小于30
		  - 条件3：原局命理十神能量中，伤官的能量大于等于150
		- 则显示：“   偏官制伏太过，防伤残目疾、足跛之疾。”
	*/
	{
		if func() bool {
			if !array.Has(ssg.Tg, "七杀") {
				return false
			}
			if shishenPowerMap["七杀"] >= 30 {
				return false
			}
			if shishenPowerMap["伤官"] < 150 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST058]")
		}
	}
	/*
		52. 条件：满足下述每一条条件
		  - 条件1：原局命理四柱中，发生三刑
		  - 条件2：条件1中的三刑对应的地支，未发生地支半合，且未发生六合
		- 则显示：“   三刑失合，破相伤身。”
	*/
	{
		if func() bool {
			_, three := comb23(dizhi)
			for _, arr := range three {
				if !slf.IsDizhiSanxing(arr[0], arr[1], arr[2]) {
					continue
				}
				huasheng := false
				two, _ := comb23(dizhi, arr...)
				for _, list := range two {
					if _, ok := slf.IsDizhiBanhe(list[0], list[1]); ok {
						huasheng = true
						break
					}
					if _, ok := slf.IsDizhiLiuhe(list[0], list[1]); ok {
						huasheng = true
						break
					}
				}
				if huasheng {
					continue
				}
				return true
			}
			return false
		}() {
			keys = append(keys, "[ST059]")
		}
	}
	/*
		53. 若原局命理中，四个地支为辰戌丑未，不要求顺序
		- 则显示：“   辰戌丑未相冲，防聋哑。”
	*/
	{
		if array.Has(dizhi, "辰", "戌", "丑", "未") {
			keys = append(keys, "[ST060]")
		}
	}
	/*
		54. 条件：满足下述每一条条件
		  - 条件1：若原局命理中四柱地支中，存在卯
		  - 条件2：若原局命理中四柱地支中，存在酉
		- 则显示：“   卯酉冲破，防瘫跛。”
	*/
	{
		if array.Has(dizhi, "卯", "酉") {
			keys = append(keys, "[ST061]")
		}
	}
	/*
		55. 条件：满足下述每一条条件
		  - 条件1：日柱地支十神为伤官
		  - 条件2：日柱地支五行，为原局命理日主的仇忌神
		- 则显示：“   日犯伤官为忌，防残疾。”
	*/
	{
		if func() bool {
			if ssg.Dz[2] != "伤官" {
				return false
			}
			if !array.Has(chouji, slf.GetWuxingByDizhi(dizhi[2])) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST062]")
		}
	}
	/*
		56. 条件：满足下述每一条条件
		  - 条件1：原局命理天干十神存在七杀
		  - 条件2：原局命理天干十神存在正官
		- 则显示：“  上半身容易因各种伤灾、手术开刀、脓肿疮毒等在身体上留下较明显的疤痕。”
	*/
	{
		if array.Has(ssg.Tg, "七杀", "正官") {
			keys = append(keys, "[ST063]")
		}
	}
	/*
		57. 条件：满足下述每一条条件
		  - 条件1：原局命理地支十神存在七杀
		  - 条件2：原局命理地支十神存在正官
		- 则显示：“   下半身容易因各种伤灾、手术开刀、脓肿疮毒等在身体上留下较明显的疤痕。”
	*/
	{
		if array.Has(ssg.Dz, "七杀", "正官") {
			keys = append(keys, "[ST064]")
		}
	}
	/*
		58. 以下条件任意满足一条即可
		  - 条件1：年柱地支为亥卯未之一，时柱地支为子
		  - 条件2：年柱地支为申子辰之一，时柱地支为丑
		  - 条件3：年柱地支为丑巳酉之一，时柱地支为午
		  - 条件4：年柱地支为寅午戌之一，时柱地支为未
		- 则显示：“   声带比较弱，容易有这方面的疾病。”
	*/
	{
		if func() bool {
			for _, arr := range [][]string{
				{"亥", "卯", "未", "子"},
				{"申", "子", "辰", "丑"},
				{"丑", "巳", "酉", "午"},
				{"寅", "午", "戌", "未"},
			} {
				if array.Has(arr[:3], dizhi[0]) && dizhi[3] == arr[3] {
					return true
				}
			}
			return false
		}() {
			keys = append(keys, "[ST065]")
		}
	}
	/*
		59. 条件：满足下述每一条条件
		  - 条件1：原局命理日主性别为女
		  - 条件2：日柱神煞存在羊刃
		- 则显示：“   日坐阳刃，子宫有刀伤之忧。”
	*/
	{
		if func() bool {
			if paipanAll.Gender != "女" {
				return false
			}
			if !array.Has(paipanAll.Shensha4[2], "羊刃") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST066]")
		}
	}
	/*
		60. 条件：满足下述每一条条件
		  - 条件1：原局命理日主性别为女
		  - 条件2：时柱神煞存在羊刃
		- 则显示：“  时坐羊刃，女命大多流过产或做过人流，或者可能有先兆流产。”
	*/
	{
		if func() bool {
			if paipanAll.Gender != "女" {
				return false
			}
			if !array.Has(paipanAll.Shensha4[3], "羊刃") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST067]")
		}
	}
	/*
		61. 条件：满足下述每一条条件
		  - 条件1：原局命理日主性别为女
		  - 条件2：时柱地支与日柱地支，发生六冲
		- 则显示：“  日时逢冲，有流产或堕胎之忧。”
	*/
	{
		if func() bool {
			if paipanAll.Gender != "女" {
				return false
			}
			if !slf.IsDizhiXiangchong(dizhi[2], dizhi[3]) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST068]")
		}
	}
	/*
		62. 条件：满足下述每一条条件
		  - 条件1：原局命理四柱地支中，存在申
		  - 条件2：原局命理四柱地支中，存在巳
		- 则显示：“  申巳双加遇刑，则臂肢有患：柱有申字，巳字，臂肢有病，或受伤，或关节炎、肩周炎之类。”
	*/
	{
		if array.Has(dizhi, "申", "巳") {
			keys = append(keys, "[ST069]")
		}
	}
	/*
		63. 条件：满足下述每一条条件
		  - 条件1：原局命理四柱地支中，存在辰
		  - 条件2：原局命理四柱地支中，存在卯
		- 则显示：“   柱有辰卯二字，主有腰痛脚痛。”
	*/
	{
		if array.Has(dizhi, "辰", "卯") {
			keys = append(keys, "[ST070]")
		}
	}
	/*
		64. 条件：满足下述每一条条件
		  - 条件1：原局四柱地支存在丑
		  - 条件2：原局四柱地支存在戌
		  - 条件3：原局四柱地支存在未
		- 则显示：“ 丑刑戌未犯支刑，肢病难痊。”
	*/
	{
		if array.Has(dizhi, "丑", "戌", "未") {
			keys = append(keys, "[ST071]")
		}
	}
	/*
		65. 条件：满足下述每一条条件
		  - 条件1：原局四柱干支组合，存在一柱为己卯
		  - 条件2：原局四柱干支组合，存在一柱为己酉
		- 则显示：“   四柱中己卯、己酉相见，主此人有腰病或跛足；或家中有腰病、跛足之人。”
	*/
	{
		if array.Has(ganzhi, "己卯", "己酉") {
			keys = append(keys, "[ST072]")
		}
	}
	/*
		66. 条件：满足下述每一条条件
		  - 条件1：原局命理日主性别为女
		  - 条件2：原局四柱神煞中，羊刃的数量大于等于2
		- 则显示：“   羊刃多，有产厄、月经过多之疾，中年后主冷疾。”
	*/
	{
		if func() bool {
			if paipanAll.Gender != "女" {
				return false
			}
			if array.Count(array.Merge(paipanAll.Shensha4...), "羊刃") < 2 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST073]")
		}
	}
	/*
		67. 条件：满足下述每一条条件
		  - 条件1：原局命理四柱干支中，丁的数量与午的数量之和大于等于3
		  - 条件2：原局命理四柱地支中，存在未
		- 则显示：“  丁午多有未者，头疮或疤痕、秃疮之疾。。”
	*/
	{
		if func() bool {
			if array.Count(array.Merge(tiangan, dizhi), "丁", "午") < 3 {
				return false
			}
			if !array.Has(dizhi, "未") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST074]")
		}
	}
	/*
		68. 条件：满足下述每一条条件
		  - 条件1：原局命理四柱天干中，存在癸
		  - 条件2：原局命理四柱天干中，存在丁
		- 则显示：“  四柱天干有癸丁，为水火交战，下克上为贼，主有心血之病或父母有病灾。”
	*/
	{
		if array.Has(tiangan, "癸", "丁") {
			keys = append(keys, "[ST075]")
		}
	}
	/*
		69. 条件：以下条件任意一条满足
		  - 条件1：原局命理四柱地支中，同时存在寅巳申
		  - 条件2：原局命理四柱地支中，同时存在丑戌未
		- 则显示：“    四柱中三刑俱全，主命主有病难愈或身体不佳。”
	*/
	{
		if func() bool {
			for _, arr := range [][]string{
				{"寅", "巳", "申"},
				{"丑", "戌", "未"},
			} {
				if array.Has(dizhi, arr...) {
					return true
				}
			}
			return false
		}() {
			keys = append(keys, "[ST076]")
		}
	}
	/*
		70. 条件：满足下述每一条条件
		  - 条件1：原局年柱天干，为丙或丁
		  - 条件2：原局日柱天干，为壬或癸
		- 则显示：“   丙、丁的岁干遇壬、癸日生，兆示有眼目之疾。”
	*/
	{
		if func() bool {
			if !array.Has([]string{"丙", "丁"}, tiangan[0]) {
				return false
			}
			if !array.Has([]string{"壬", "癸"}, tiangan[2]) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST077]")
		}
	}
	/*
		71. 条件：满足下述每一条条件
		  - 条件1：原局命理四柱地支中，存在子
		  - 条件2：原局命理四柱地支中，存在午
		- 则显示：“   命局中有子与午之人定有脚气。
	*/
	{
		if array.Has(dizhi, "子", "午") {
			keys = append(keys, "[ST078]")
		}
	}
	/*
		72. 条件：满足下述每一条条件
		  - 条件1：原局命理日主性别为女
		  - 条件2：时柱天干十神为偏印
		  - 条件3：时柱神煞存在羊刃
		- 则显示：“   女人四柱时干为枭，时支为羊刃多有流产、开刀之事。”
	*/
	{
		if func() bool {
			if paipanAll.Gender != "女" {
				return false
			}
			if ssg.Tg[3] != "偏印" {
				return false
			}
			if !array.Has(paipanAll.Shensha4[3], "羊刃") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST079]")
		}
	}
	/*
		73. 条件：满足下述每一条条件
		  - 条件1：原局月柱地支五行，为火
		  - 条件2：原局日柱地支五行，为火
		  - 条件3：原局时柱地支五行，为火
		  - 条件4：原局天干五行，存在丙或丁
		  - 条件5：原局天干五行，存在庚或辛
		  - 条件6：原局天干五行，不存在壬，不存在癸
		- 则显示：“   出生在火月、火日、火时，上柱见金火相战，天干无水透，必有糖尿病，逢火年、水年必引发。”
	*/
	{
		if func() bool {
			if !array.EqualTarget("火", slf.GetWuxingByDizhi(dizhi[1]), slf.GetWuxingByDizhi(dizhi[2]), slf.GetWuxingByDizhi(dizhi[3])) {
				return false
			}
			if !array.HasAny([]string{"丙", "丁"}, tiangan...) {
				return false
			}
			if !array.HasAny([]string{"庚", "辛"}, tiangan...) {
				return false
			}
			if array.HasAny([]string{"壬", "癸"}, tiangan...) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST080]")
		}
	}
	/*
		74. 条件：满足下述每一条条件
		  - 条件1：原局命理五行能量中，水行能量大于等于150
		  - 条件2：原局四柱地支，同时存在子，存在卯
		  - 条件3：原局四柱天干中，不存在甲，不存在乙
		- 则显示：“  八字水旺有子卯刑，而木又未透干必有肝病，逢水年必有引发。”
	*/
	{
		if func() bool {
			if wuxingPowerMap["水"] < 150 {
				return false
			}
			if !array.Has(dizhi, "子", "卯") {
				return false
			}
			if array.HasAny([]string{"甲", "乙"}, tiangan...) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST081]")
		}
	}
	/*
		75. 条件：满足下述每一条条件
		  - 条件1：原局命理四柱天干中，存在庚或者辛
		  - 条件2：原局命理四柱天干中，存在癸或者壬
		  - 条件3：原局命理四柱天干中，不存在庚辛壬癸之外的其他天干
		- 则显示：“   地支一片金水，生在冬季或北方，定有寒病。”
	*/
	{
		if func() bool {
			if !array.HasAny([]string{"庚", "辛"}, tiangan...) {
				return false
			}
			if !array.HasAny([]string{"癸", "壬"}, tiangan...) {
				return false
			}
			if array.HasAny([]string{"甲", "乙", "丙", "丁", "戊", "己"}, tiangan...) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST082]")
		}
	}
	/*
		76. 原局命理五行能量中，土行能量大于等于300
		- 则显示：“   命局中土特旺，农村人必有脾胃之疾。”
	*/
	{
		if wuxingPowerMap["土"] >= 300 {
			keys = append(keys, "[ST083]")
		}
	}
	/*
		77. 条件：满足下述每一条条件
		  - 条件1：原局日柱地支十神，为食伤
		  - 条件2：原局日柱地支，发生六冲
		- 则显示：“   日支坐食伤，被冲克，主此人说话结巴或吐词不清。”
	*/
	{
		if func() bool {
			if array.Has([]string{"食神", "伤官"}, ssg.Dz[2]) {
				return false
			}
			two, _ := comb23(dizhi, dizhi[2])
			for _, arr := range two {
				if slf.IsDizhiXiangchong(arr[0], arr[1]) {
					return true
				}
			}
			return false
		}() {
			keys = append(keys, "[ST084]")
		}
	}
	/*
		78. 若原局命理天干十神、地支藏干十神，财才的数量为0
		- 则显示：“   凡八字无财，日主不论为何五行都有肝胆之疾。”
	*/
	{
		if ssg.NumMap["财才"] == 0 {
			keys = append(keys, "[ST085]")
		}
	}
	/*
		79. 条件：满足下述每一条条件
		  - 条件1：原局命理日主性别为女
		  - 条件2：五行水，为原局命理日主的喜用神五行
		  - 条件3：原局命理五行能量中，水行的能量小于等于30
		- 则显示：“   用水而欠水的女命都易有妇科病。”
	*/
	{
		if func() bool {
			if paipanAll.Gender != "女" {
				return false
			}
			if !array.Has(xiyong, "水") {
				return false
			}
			if wuxingPowerMap["水"] > 30 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST086]")
		}
	}
	/*
		80. 条件：满足下述每一条条件
		  - 条件1：原局四柱天干十神，存在正官
		  - 条件2：原局四柱天干十神，存在七杀
		- 则显示：“   官杀同透天干[头面显眼处带伤破相]。”
	*/
	{
		if array.Has(ssg.Tg, "正官", "七杀") {
			keys = append(keys, "[ST087]")
		}
	}
	/*
		81. 条件：以下条件任意一条满足即可
		  - 条件1：以下要求均满足即可
		    - 要求1：原局四柱天干，存在伤官
		    - 要求2：原局四柱天干，存在七杀
		  - 条件2：原局四柱任一一柱，满足以下要求即可
		    - 要求1：该柱地支十神为官杀
		    - 要求2：该柱地支存在三刑（子卯刑、寅午戌三刑、寅巳申三刑），或者六冲
		- 则显示：“  伤官与官或杀在天干、伤官见官、官杀在支被刑冲[四肢肚腹带伤破相]。”
	*/
	{
		if func() bool {
			if array.Has(ssg.Tg, "伤官", "七杀") {
				return true
			}
			for i := 0; i < 4; i++ {
				if ssg.Dz[i] == "官杀" {
					two, three := comb23(dizhi, dizhi[i])
					for _, arr := range two {
						if slf.IsDizhiXiangchong(arr[0], arr[1]) {
							return true
						}
						if array.Has(arr, "子", "卯") {
							return true
						}
					}
					for _, arr := range three {
						if slf.IsDizhiSanxing(arr[0], arr[1], arr[2]) {
							return true
						}
					}
				}
			}
			return false
		}() {
			keys = append(keys, "[ST088]")
		}
	}
	/*
		82. 条件：满足下述每一条条件
		  - 条件1：原局四柱天干中，存在庚
		  - 条件2：原局四柱地支中，存在寅
		  - 条件3：原局四柱地支中，存在卯
		- 则显示：“   干有庚，支有寅卯二字须防兽咬或下肢有病伤。”
	*/
	{
		if array.Has(tiangan, "庚") && array.Has(dizhi, "寅", "卯") {
			keys = append(keys, "[ST089]")
		}
	}
	/*
		83. 条件：满足下述每一条条件
		  - 条件1：原局四柱天干中，存在庚
		  - 条件2：原局四柱天干中，存在辛
		  - 条件3：原局四柱地支中，存在申
		  - 条件4：原局四柱地支中，存在酉
		- 则显示：“   柱有庚辛申酉四字有血光伤或肢体伤残之祸。”
	*/
	{
		if array.Has(tiangan, "庚", "辛") && array.Has(dizhi, "申", "酉") {
			keys = append(keys, "[ST090]")
		}
	}
	/*
		84. 条件：以下条件任意一条满足即可
		  - 条件1：以下要求均满足即可
		    - 要求1：原局四柱天干中，存在庚或者辛
		    - 要求2：原局四柱天干中，存在丙或者丁
		  - 条件2：以下要求均满足即可
		    - 要求1：原局四柱天干、藏干中，存在癸
		    - 要求2：原局四柱五行能量中，五行水的能量 减去五行火的能量，小于等于200
		    - 要求3：原局四柱五行能量中，五行土的能量 减去五行水的能量，小于等于200
		- 则显示：“   八字中有火被水克、火土熬干癸水、干有庚或辛同时又丙或丁，容易近视。”
	*/
	{
		if func() bool {
			if array.HasAny(tiangan, "庚", "辛") && array.HasAny(tiangan, "丙", "丁") {
				return true
			}
			if !array.Has(array.Merge(tiangan, cangan), "癸") {
				return false
			}
			if wuxingPowerMap["水"]-wuxingPowerMap["火"] > 200 {
				return false
			}
			if wuxingPowerMap["土"]-wuxingPowerMap["水"] > 200 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST091]")
		}
	}
	/*
		85. 条件：以下条件任意一条满足即可
		  - 条件1：以下要求均满足即可
		    - 要求1：日柱天干五行为土
		    - 要求2：原局四柱天干、藏干中，丙的数量与丁的数量之和，大于等于3
		    - 要求3：原局五行能量中，火行能量大于等于200
		    - 要求4：原局五行能量中，水行能量小于等于50
		  - 条件2：原局月柱地支，为巳，或者午
		  - 条件3：原局四柱四个地支中，至少有三个的地支五行为土
		- 则显示：“  土日主遇强火弱水、月支为巳午、地支土旺，皮肤不太好，容易过敏。”
	*/
	{
		if func() bool {
			if rgwx == "土" && array.Count(array.Merge(tiangan, cangan), "丙", "丁") >= 3 && wuxingPowerMap["火"] >= 200 && wuxingPowerMap["水"] <= 50 {
				return true
			}
			if !array.Has([]string{"巳", "午"}, dizhi[1]) {
				return false
			}
			if array.Count(dizhiWx, "土") >= 3 {
				return true
			}
			return false
		}() {
			keys = append(keys, "[ST092]")
		}
	}
	/*
		86. 条件：以下条件任意一条满足即可
		  - 条件1：食伤对应五行，为原局日主喜用五行
		  - 条件2：原局十神能量中，食神的能量大于等于150
		  - 条件3：以下要求均满足即可
		    - 要求1：日柱天干发生天干五合
		    - 要求2：原局十神能量中，正印的能量大于等于150
		  - 条件4：以下要求均满足即可
		    - 要求1：原局日主旺衰为偏强、身强、从强
		    - 要求2：原局日柱天干为癸水
		- 则显示：“   此命身体比较肥胖。”
	*/
	{
		if func() bool {
			if paipanAll.ShishenWuxingMap["食伤"] == rgwx {
				return true
			}
			if shishenPowerMap["食伤"] >= 150 {
				return true
			}
			if func() bool {
				two, _ := comb23(tiangan, tiangan[2])
				for _, arr := range two {
					if _, ok := slf.IsTianganWuhe(arr[0], arr[1]); ok {
						return true
					}
				}
				return false
			}() && shishenPowerMap["正印"] >= 150 {
				return true
			}
			if array.Has([]string{"偏强", "身强", "从强"}, riyuan) && array.Has(tiangan, "癸") {
				return true
			}
			return false
		}() {
			keys = append(keys, "[ST093]")
		}
	}
	/*
		87. 条件：以下条件任意一条满足即可
		  - 条件1：原局四柱纳音中，对应五行为水的纳音个数，大于等于2
		  - 条件2：原局四柱纳音中，对应五行为火的纳音个数，大于等于2
		- 则显示：“   四柱纳音水或纳音火有两个就算高壮。”
	*/
	{
		if array.Count(nayinWuxing, "水") >= 2 || array.Count(nayinWuxing, "火") >= 2 {
			keys = append(keys, "[ST094]")
		}
	}
	/*
		88. 条件：以下条件任意一条满足即可
		  - 条件1：原局四柱纳音中，对应五行为土的纳音个数，大于等于2
		  - 条件2：原局四柱纳音中，对应五行为金的纳音个数，大于等于2
		- 则显示：“  四柱纳音土或纳音金有两个的多瘦小。”
	*/
	{
		if array.Count(nayinWuxing, "土") >= 2 || array.Count(nayinWuxing, "金") >= 2 {
			keys = append(keys, "[ST095]")
		}
	}
	/*
		89. 条件：以下条件任意一条满足即可
		  - 条件1：以下要求均满足即可
		    - 要求1：原局日主旺衰为从弱、身弱、偏弱
		    - 要求2：原局命理十神能量中，官杀的能量大于等于250
		    - 要求3：上述三个条件未触发（86、87、88）
		  - 条件2：以下要求均满足即可
		    - 要求1：以下要求均满足即可
		    - 要求2：原局命理十神能量中，印枭的能量
		  - 条件3：以下要求均满足即可
		    - 要求1：原局命理十神能量中，比劫能量大于250，或食伤能量小于50
		    - 要求2：原局日主旺衰为从强、身强、偏强
		    - 要求3：原局命理十神能量中，印枭的能量小于150
		- 则显示：“   此命多瘦小。”
	*/
	{
		if func() bool {
			if !array.Has([]string{"从弱", "身弱", "偏弱"}, riyuan) {
				return false
			}
			if shishenPowerMap["官杀"] < 250 {
				return false
			}
			if array.HasAny(keys, "[ST093]", "[ST094]", "[ST095]") {
				return false
			}
			return true
		}() || func() bool {
			if array.HasAny(keys, "[ST093]", "[ST094]", "[ST095]") {
				return false
			}
			if shishenPowerMap["印枭"] <= 250 {
				return true
			}
			return false
		}() || func() bool {
			if shishenPowerMap["比劫"] <= 250 && shishenPowerMap["食伤"] >= 50 {
				return false
			}
			if !array.Has([]string{"从强", "身强", "偏强"}, riyuan) {
				return false
			}
			if shishenPowerMap["印枭"] >= 150 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST096]")
		}
	}
	/*
		90. 条件：以下条件任意一条满足即可
		  - 条件1：以下要求均满足即可
		    - 要求1：原局四柱地支中，存在酉
		    - 要求2：原局四柱地支中，存在亥，或者存在子
		  - 条件2：原局月柱星运，为沐浴
		  - 条件3：原局四柱中，存在任意一柱，满足以下条件即可
		    - 要求1：该柱神煞存在桃花
		    - 要求2：该柱地支，发生六合、三合或者半三合
		  - 条件4：日柱神煞存在禄神
		- 则显示：“  八字中的信息显示善饮酒。”
	*/
	{
		if func() bool {
			if !array.Has(dizhi, "酉") {
				return false
			}
			if !array.HasAny(dizhi, "亥", "子") {
				return false
			}
			return true
		}() || xingyun[2] == "沐浴" ||
			func() bool {
				for _, idx := range []int{0, 1, 2, 3} {
					if !array.Has(paipanAll.Shensha4[idx], "桃花") {
						continue
					}
					two, three := comb23(dizhi, dizhi[idx])
					for _, arr := range two {
						if _, ok := slf.IsDizhiLiuhe(arr[0], arr[1]); ok {
							return true
						}
						if _, ok := slf.IsDizhiBanhe(arr[0], arr[1]); ok {
							return true
						}
					}
					for _, arr := range three {
						if _, ok := slf.IsDizhiSanhe(arr[0], arr[1], arr[2]); ok {
							return true
						}
					}
					return true
				}
				return false
			}() ||
			func() bool {
				if !array.Has(paipanAll.Shensha4[2], "禄神") {
					return false
				}
				return true
			}() {
			keys = append(keys, "[ST097]")
		}
	}
	/*
		91. 条件：以下条件任意一条满足即可
		  - 条件1：以下要求均满足即可
		    - 要求1：原局命理五行能量中，木的能量大于等于150
		    - 要求2：原局命理五行能量中，水的能量小于等于30
		  - 条件2：以下要求均满足即可
		    - 要求1：原局命理五行能量中，金的能量小于等于30
		    - 要求2：原局命理五行能量中，水的能量大于等于150
		- 则显示：“  木旺水衰土受克，水旺金衰，容易患糖尿病。”
	*/
	{
		if func() bool {
			if wuxingPowerMap["木"] >= 150 && wuxingPowerMap["水"] <= 30 {
				return true
			}
			if wuxingPowerMap["金"] <= 30 && wuxingPowerMap["水"] >= 150 {
				return true
			}
			return false
		}() {
			keys = append(keys, "[ST098]")
		}
	}
	/*
		92. 条件：满足下述每一条条件
		  - 条件1：原局日主性别为女
		  - 条件2：原局命理五行能量中，火行能量小于等于40
		- 则显示：“   女命火弱，不仅消化功能差，而且容易贫血或月经失调。”
	*/
	{
		if func() bool {
			if paipanAll.Gender != "女" {
				return false
			}
			if wuxingPowerMap["火"] > 40 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST099]")
		}
	}
	/*
		93. 条件：满足下述每一条条件
		  - 条件1：原局日柱天干，为壬，或癸
		  - 条件2：原局月柱地支为，亥、子、丑 之一
		  - 条件3：水为原局日柱的仇忌神五行
		  - 条件4：原局日主性别为女
		- 则显示：“   女人八字里水生冬月，又是自己的忌神，有痛经或月经多的情况。”
	*/
	{
		if func() bool {
			if paipanAll.Gender != "女" {
				return false
			}
			if !array.Has([]string{"壬", "癸"}, tiangan[2]) {
				return false
			}
			if !array.Has([]string{"亥", "子", "丑"}, dizhi[1]) {
				return false
			}
			if !array.Has(chouji, "水") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST100]")
		}
	}
	/*
		94. 条件：满足下述每一条条件
		  - 条件1：原局命理中，日主性别为女
		  - 条件2：原局十天干能量中，癸水能量小于等于30
		  - 条件3：原局十天干能量中，丁火能量小于等于30
		- 则显示：“   女命癸水与丁火双弱，月事多迟来。”
	*/
	{
		if func() bool {
			if paipanAll.Gender != "女" {
				return false
			}
			if shishenNumMap["癸"] > 30 {
				return false
			}
			if shishenNumMap["丁"] > 30 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST101]")
		}
	}
	/*
		95. 条件：满足下述每一条条件
		  - 条件1：原局命理中，日主性别为女
		  - 条件2：原局十天干能量中，癸水能量大于等于120
		  - 条件3：原局十天干能量中，丁火能量大于等于120
		- 则显示：“   女命癸水与丁火双强，月事多早来。”
	*/
	{
		if func() bool {
			if paipanAll.Gender != "女" {
				return false
			}
			if shishenNumMap["癸"] < 120 {
				return false
			}
			if shishenNumMap["丁"] < 120 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST102]")
		}
	}
	/*
		96. 条件：满足下述每一条条件
		  - 条件1：原局命理中，日主性别为女
		  - 条件2：原局十天干能量中，丁火能量大于等于120
		  - 条件3：原局十天干能量中，癸水能量小于等于30
		- 则显示：“   女命丁火盛而癸水弱，月事若提前则量多且稀，月事若延后，则量少而污浊或凝块。”
	*/
	{
		if func() bool {
			if paipanAll.Gender != "女" {
				return false
			}
			if shishenNumMap["丁"] < 120 {
				return false
			}
			if shishenNumMap["癸"] > 30 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST103]")
		}
	}
	/*
		97. 条件：满足下述每一条条件
		  - 条件1：原局命理中，日主性别为女
		  - 条件2：原局命理十神能量中，财才的能量小于等于40
		- 则显示：“   女命八字缺财，财为女命的感情线，八字缺财自然是情感冷淡，没有性欲的表现。”
	*/
	{
		if func() bool {
			if paipanAll.Gender != "女" {
				return false
			}
			if shishenPowerMap["财才"] > 40 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST104]")
		}
	}
	/*
		98. 条件：满足下述每一条条件
		  - 条件1：原局命理中，日主性别为女
		  - 条件2：原局命理四柱地支中，存在子，存在未
		  - 条件3：原局命理日柱或时柱地支中，至少有一个地支为子，或者为未
		- 则显示：“   日时有子未六害的女性，大多数患有妇人病。”
	*/
	{
		if func() bool {
			if paipanAll.Gender != "女" {
				return false
			}
			if !array.Has(dizhi, "子", "未") {
				return false
			}
			if !array.HasAny([]string{"子", "未"}, dizhi[2], dizhi[3]) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST105]")
		}
	}
	/*
		99. 条件：满足下述每一条条件
		  - 条件1：原局四柱天干，不存在辛
		  - 条件2：原局四柱地支，存在丑
		- 则显示：“   八字无透辛金，只有丑易患结石。”
	*/
	{
		if !array.Has(tiangan, "辛") && array.Has(dizhi, "丑") {
			keys = append(keys, "[ST106]")
		}
	}
	/*
		100. 条件：满足下述每一条条件
		  - 条件1：原局月柱地支对应五行，为火
		  - 条件2：原局四柱地支，存在午
		  - 条件3：原局四柱天干中，存在甲木
		- 则显示：“   午旺甲木弱，防患精神病。”
	*/
	{
		if func() bool {
			if slf.GetWuxingByDizhi(dizhi[1]) != "火" {
				return false
			}
			if !array.Has(dizhi, "午") {
				return false
			}
			if !array.Has(tiangan, "甲") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST107]")
		}
	}
	/*
		101. 条件：满足下述每一条条件
		  - 条件1：原局命理十天干能量中，壬的能量小于等于20
		  - 条件2：原局命理五行能量中，金行的能量大于等于150
		- 则显示：“   八字壬水弱极，原局遇强金来滞水，多患膀胱结石。”
	*/
	{
		if shishenNumMap["壬"] <= 20 && wuxingPowerMap["金"] >= 150 {
			keys = append(keys, "[ST108]")
		}
	}
	/*
		102. 条件：满足下述每一条条件
		  - 条件1：原局命理十天干能量中，癸的能量小于等于20
		  - 条件2：原局命理五行能量中，金行的能量大于等于150
		- 则显示：“   癸水弱，原局遇强金阻塞水源，多患肾结石。”
	*/
	{
		if shishenNumMap["癸"] <= 20 && wuxingPowerMap["金"] >= 150 {
			keys = append(keys, "[ST109]")
		}
	}
	/*
		103. 条件：满足下述每一条条件
		  - 条件1：原局日柱旺衰为从弱、身弱、偏弱
		  - 条件2：原局命理十神能量中，食伤的能量大于等于150
		- 则显示：“   凡是日主弱，食伤重的人均易得神经衰弱症。”
	*/
	{
		if func() bool {
			if !array.Has([]string{"从弱", "身弱", "偏弱"}, riyuan) {
				return false
			}
			if shishenPowerMap["食伤"] < 150 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST110]")
		}
	}
	/*
		104. 若原局月柱地支十神，为官杀
		- 则显示：“   任何日元，逢遇官杀月，必迟睡、熬夜、睡眠不正常。”
	*/
	{
		if ssg.Dz[1] == "官杀" {
			keys = append(keys, "[ST111]")
		}
	}
	/*
		105. 条件：满足下述每一条条件
		  - 条件1：原局四柱地支中，存在辰
		  - 条件2：原局四柱地支中，存在戌
		- 则显示：“  辰戌相冲的八字，胃部容易发生病变。”
	*/
	{
		if array.Has(dizhi, "辰", "戌") {
			keys = append(keys, "[ST112]")
		}
	}
	/*
		106. 条件：满足下述每一条条件
		  - 条件1：原局命理五行能量中，木行能量大于等于150
		  - 条件2：原局命理五行能量中，土行能量小于等于30
		- 则显示：“   木旺土弱，伤脾胃。”
	*/
	{
		if wuxingPowerMap["木"] >= 150 && wuxingPowerMap["土"] <= 30 {
			keys = append(keys, "[ST113]")
		}
	}
	/*
		107. 条件：满足下述每一条条件
		  - 条件1：原局命理五行能量中，木行能量大于等于150
		  - 条件2：原局命理五行能量中，水行能量大于等于150
		- 则显示：“   柱中水木两旺，伤脾胃。”
	*/
	{
		if wuxingPowerMap["木"] >= 150 && wuxingPowerMap["水"] >= 150 {
			keys = append(keys, "[ST114]")
		}
	}
	/*
		108. 条件：满足下述每一条条件
		  - 条件1：原局四柱天干、地支藏干中，五行为金（庚或辛）的天干数量大于等于3
		  - 条件2：原局四柱天干、地支藏干中，五行为土（戊或己）的天干数量小于等于1
		- 则显示：“   金多土弱，伤脾胃。”
	*/
	{
		if array.Count(array.Merge(tiangan, cangan), "庚", "辛") >= 3 && array.Count(array.Merge(tiangan, cangan), "戊", "己") <= 1 {
			keys = append(keys, "[ST115]")
		}
	}
	/*
		109. 条件：满足下述每一条条件
		  - 条件1：原局四柱天干、地支藏干中，五行为水（壬或癸）的天干数量大于等于3
		  - 条件2：原局月柱地支，为 亥、子、丑 之一
		- 则显示：“   水多土寒，脾胃之疾。”
	*/
	{
		if array.Count(array.Merge(tiangan, cangan), "壬", "癸") >= 3 && array.Has([]string{"亥", "子", "丑"}, dizhi[1]) {
			keys = append(keys, "[ST116]")
		}
	}
	/*
		110. 条件：满足下述每一条条件
		  - 条件1：原局命理五行能量中，木行能量大于等于150
		  - 条件2：原局命理五行能量中，金行能量大于等于150
		  - 条件3：原局命理五行能量中，火行能量小于等于30
		  - 条件3：原局命理五行能量中，土行能量小于等于30
		- 则显示：“   金木同旺，无土无火或火土极弱，主消化系统多病。”
	*/
	{
		if wuxingPowerMap["木"] >= 150 && wuxingPowerMap["金"] >= 150 && wuxingPowerMap["火"] <= 30 && wuxingPowerMap["土"] <= 30 {
			keys = append(keys, "[ST117]")
		}
	}
	/*
		111. 条件：满足下述每一条条件
		  - 条件1：原局年柱、月柱存在天干或地支，其五行为金
		  - 条件2：原局年柱、月柱存在天干或地支，其五行为木
		  - 条件3：以下要求满足一个其中一个即可
		    - 要求1：原局命理五行能量中，木行能量大于等于150
		    - 要求2：原局月柱地支对应五行为水、或者木
		  - 条件4：
		    - 要求1：原局命理五行能量中，金行能量大于等于150
		    - 要求2：原局月柱地支对应五行为土、或者金
		  - 条件5：以下要求满足一个其中一个即可
		    - 要求1：原局日柱、时柱，不存在五行为土的天干或地支
		    - 要求2：原局五行能量中，火的能量小于等于30，且土的能量小于等于30
		- 则显示：“   年月柱见金木旺，日时无从无土或火土极弱，食道多病。”
	*/
	{
		if array.Has([]string{
			slf.GetWuxingByTiangan(tiangan[0]),
			slf.GetWuxingByDizhi(dizhi[0]),
			slf.GetWuxingByTiangan(tiangan[1]),
			slf.GetWuxingByDizhi(dizhi[1])},
			"金",
		) &&
			array.Has([]string{
				slf.GetWuxingByTiangan(tiangan[0]),
				slf.GetWuxingByDizhi(dizhi[0]),
				slf.GetWuxingByTiangan(tiangan[1]),
				slf.GetWuxingByDizhi(dizhi[1])},
				"木",
			) &&
			func() bool {
				if wuxingPowerMap["木"] >= 150 {
					return true
				}
				if array.Has([]string{"水", "木"}, slf.GetWuxingByDizhi(dizhi[1])) {
					return true
				}
				return false
			}() &&
			func() bool {
				if wuxingPowerMap["金"] < 150 {
					return false
				}
				if !array.Has([]string{"土", "金"}, slf.GetWuxingByDizhi(dizhi[1])) {
					return false
				}
				return true
			}() &&
			func() bool {
				if !array.Has([]string{"戊", "己"}, tiangan[2], dizhi[2]) {
					return true
				}
				if !array.Has([]string{"戊", "己"}, tiangan[3], dizhi[3]) {
					return true
				}
				if wuxingPowerMap["火"] <= 30 && wuxingPowerMap["土"] <= 30 {
					return true
				}
				return false
			}() {
			keys = append(keys, "[ST118]")
		}
	}
	/*
		112. 条件：满足下述每一条条件
		  - 条件1：原局命理五行能量中，火行能量大于等于150
		  - 条件2：原局命理五行能量中，土行能量小于等于30
		- 则显示：“   八字火盛土衰或水多流而稀烂，易患皮肤过敏病。”
	*/
	{
		if wuxingPowerMap["火"] >= 150 && wuxingPowerMap["土"] <= 30 {
			keys = append(keys, "[ST119]")
		}
	}
	/*
		113. 条件：满足下述每一条条件
		  - 条件1：土为原局命理的喜用神
		  - 条件2：原局四柱天干，存在甲
		  - 条件3：原局四柱天干，存在己
		- 则显示：“   八字以土为喜用，天干透己土而遭甲木克合，易患脱皮性皮肤病（干癣、赖痢）。”
	*/
	{
		if array.Has(xiyong, "土") && array.Has(tiangan, "甲") && array.Has(tiangan, "己") {
			keys = append(keys, "[ST120]")
		}
	}
	/*
		114. 条件：满足下述每一条条件
		  - 条件1：原局五行能量中，火行能量大于等于150
		  - 条件2：原局五行能量中，木行能量小于等于50
		  - 条件3：原局月柱地支为巳或者午
		- 则显示：“   木火炎炎，而火盛木弱，头发棕赤或有卷发之象，且常因肝炎虚旺而引起皮肤之毒。”
	*/
	{
		if wuxingPowerMap["火"] >= 150 && wuxingPowerMap["木"] <= 50 && array.Has([]string{"巳", "午"}, dizhi[1]) {
			keys = append(keys, "[ST121]")
		}
	}
	/*
		115. 条件：满足下述每一条条件
		  - 条件1：原局五行能量中，火行能量大于等于150
		  - 条件2：原局五行能量中，土行能量小于等于50
		- 则显示：“  火炎土焦而火盛土弱，冬天皮肤易冻裂或易患富贵手等皮肤病。”
	*/
	{
		if wuxingPowerMap["火"] >= 150 && wuxingPowerMap["土"] <= 50 {
			keys = append(keys, "[ST122]")
		}
	}
	/*
		116. 若日主为男性，则根据日柱天干的值，对应显示
		  - 甲：则显示：“   伤官属丁火而盛，生殖器勃起速度慢，但较有耐性。”
		  - 乙：则显示：“   伤官属丙火，体毛棕赤生殖器有疾。”
		  - 丙：则显示：“   伤官属己土，生殖器稍弱，交后仍有力。”
		  - 丁：则显示：“   伤官属戊土，生殖器大小适度，交后尚会膨胀少许。”
		  - 戊：则显示：“   伤官属辛金，生殖器粗而长较耐久。”
		  - 己：则显示：“   伤官属庚金，生殖器粗大而强。”
		  - 庚：则显示：“   伤官属癸水，生殖器肥满而稍带细长。”
		  - 辛：则显示：“   伤官属壬水，生殖器较粗而弹性，颜色亮而圆肥。”
		  - 壬：则显示：“   伤官属乙木，生殖器细长而略曲。”
		  - 癸：则显示：“   伤官属甲木，生殖器粗长而直。”
	*/
	{
		if paipanAll.Gender == "男" {
			switch tiangan[2] {
			case "甲":
				keys = append(keys, "[ST123]")
			case "乙":
				keys = append(keys, "[ST124]")
			case "丙":
				keys = append(keys, "[ST125]")
			case "丁":
				keys = append(keys, "[ST126]")
			case "戊":
				keys = append(keys, "[ST127]")
			case "己":
				keys = append(keys, "[ST128]")
			case "庚":
				keys = append(keys, "[ST129]")
			case "辛":
				keys = append(keys, "[ST130]")
			case "壬":
				keys = append(keys, "[ST131]")
			case "癸":
				keys = append(keys, "[ST132]")
			}
		}
	}
	/*
		117. 条件：以下条件任意一条满足即可
		  - 条件1：原局日柱天干为丙
		  - 条件2：原局日柱天干为丁
		- 则显示：“   原则上八字伤官属土之人（日主属火）较不易感染性病，若感染亦较易医治。”
	*/
	{
		if array.Has([]string{"丙", "丁"}, tiangan[2]) {
			keys = append(keys, "[ST133]")
		}
	}
	/*
		118. 条件：满足下述每一条条件
		  - 条件1：原局五行能量中，火行能量小于等于30
		  - 条件2：原局五行能量中，水行能量大于等于180
		- 则显示：“   丁火代表心脏与血液，若八字遇强水压迫弱火，有高血压或心跳较快之象。”
	*/
	{
		if wuxingPowerMap["火"] <= 30 && wuxingPowerMap["水"] >= 180 {
			keys = append(keys, "[ST134]")
		}
	}
	/*
		119. 条件：满足下述每一条条件
		  - 条件1：原局五行能量中，火行能量小于等于30
		  - 条件2：原局五行能量中，土行能量大于等于180
		- 则显示：“   若八字丁火弱土气盛，则可能患贫血之毛病。因土能泄丁火，致使血气散涣缺乏。”
	*/
	{
		if wuxingPowerMap["火"] <= 30 && wuxingPowerMap["土"] >= 180 {
			keys = append(keys, "[ST135]")
		}
	}
	/*
		120. 条件：满足下述每一条条件
		  - 条件1：原局五行能量中，火行能量小于等于30
		  - 条件2：原局五行能量中，木行能量大于等于180
		- 则显示：“  八字中木多火窒，丁火弱极，易患心肌梗塞。”
	*/
	{
		if wuxingPowerMap["火"] <= 30 && wuxingPowerMap["木"] >= 180 {
			keys = append(keys, "[ST136]")
		}
	}
	/*
		121. 条件：满足下述每一条条件
		  - 条件1：原局五行能量中，火行能量大于等于180
		  - 条件2：原局四柱天干中，存在丁
		  - 条件3：原局五行能量中，土行能量小于等于30
		- 则显示：“   八字中丁火强旺而土气弱极，往往血压较低，心跳脉博较缓，心口结难舒，常喘大气。”
	*/
	{
		if wuxingPowerMap["火"] >= 180 && array.Has(tiangan, "丁") && wuxingPowerMap["土"] <= 30 {
			keys = append(keys, "[ST137]")
		}
	}
	/*
		122. 条件：以下条件任意一条满足即可
		  - 条件1：原局五行能量中，水行能量大于等于180
		  - 条件2：以下要求均满足即可
		    - 要求1：原局五行能量中，火行能量大于等于180
		    - 要求2：原局四柱天干中，存在丁
		- 则显示：“  柱中水旺或丁火太旺，易患心肺之疾。”
	*/
	{
		if func() bool {
			if wuxingPowerMap["水"] >= 180 {
				return true
			}
			if wuxingPowerMap["火"] >= 180 && array.Has(tiangan, "丁") {
				return true
			}
			return false
		}() {
			keys = append(keys, "[ST138]")
		}
	}
	/*
		123. 条件：满足下述每一条条件
		  - 条件1：原局四柱天干、地支藏干中，戊的数量和己的数量之和大于等于3
		  - 条件2：原局五行能量中，土行能量小于等于30
		- 则显示：“  柱中土多火弱，有高血压病。”
	*/
	{
		if array.Count(array.Merge(tiangan, cangan), "戊", "己") >= 3 && wuxingPowerMap["土"] <= 30 {
			keys = append(keys, "[ST139]")
		}
	}
	/*
		124. 条件：满足下述每一条条件
		  - 条件1：原局日柱干支为 庚午
		  - 条件2：原局时柱干支为 辛巳
		- 则显示：“  日柱庚午，时柱辛巳多有心血病。”
	*/
	{
		if ganzhi[2] == "庚午" && ganzhi[3] == "辛巳" {
			keys = append(keys, "[ST140]")
		}
	}
	/*
		125. 条件：满足下述每一条条件
		  - 条件1：原局日柱或时柱干支，任意一柱为庚寅
		  - 条件2：原局日柱或时柱干支，任意一柱为辛卯
		- 则显示：“  日时庚寅与辛卯，易患肺病。”
	*/
	{
		if func() bool {
			if !array.Has(dizhi[2:4], "庚寅") {
				return false
			}
			if !array.Has(dizhi[2:4], "辛卯") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST141]")
		}
	}
	/*
		126. 条件：满足下述每一条条件
		  - 条件1：原局日柱或时柱干支，任意一柱为乙酉
		  - 条件2：原局月柱地支，为巳或午
		- 则显示：“   乙酉日时见休囚，防患肺病。”
	*/
	{
		if func() bool {
			if !array.Has(dizhi[2:4], "乙酉") {
				return false
			}
			if !array.Has([]string{"巳", "午"}, dizhi[1]) {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST142]")
		}
	}
	/*
		127. 条件：满足下述每一条条件
		  - 条件1：原局日柱天干为丙
		  - 条件2：原局日主为旺衰为从弱、身弱、偏弱
		  - 条件3：原局月柱地支，为亥子丑之一
		  - 条件4：原局五行能量中，火行能量小于等于50
		- 则显示：“   丙火极弱生冬月，小肠之疾。”
	*/
	{
		if tiangan[2] == "丙" && array.Has([]string{"从弱", "身弱", "偏弱"}, riyuan) && array.Has([]string{"亥", "子", "丑"}, dizhi[1]) && wuxingPowerMap["火"] <= 50 {
			keys = append(keys, "[ST143]")
		}
	}
	/*
		128. 条件：满足下述每一条条件
		  - 条件1：原局日柱天干为庚
		  - 条件2：原局日主为旺衰为从弱、身弱、偏弱
		  - 条件3：原局月柱地支，为亥子丑之一
		- 则显示：“   庚金弱生冬月，大肠之疾。”
	*/
	{
		if tiangan[2] == "庚" && array.Has([]string{"从弱", "身弱", "偏弱"}, riyuan) && array.Has([]string{"亥", "子", "丑"}, dizhi[1]) {
			keys = append(keys, "[ST144]")
		}
	}
	/*
		129. 条件：满足下述每一条条件
		  - 条件1：原局五行能量中，火行能量大于等于150
		  - 条件2：原局五行能量中，金行能量大于等于150
		  - 条件3：原局月柱地支，为巳午未之一
		  - 条件4：原局四柱天干、地支藏干中，存在丙
		  - 条件5：原局四柱天干、地支藏干中，存在庚
		- 则显示：“   丙庚两旺逢燥火，有便秘之疾。”
	*/
	{
		if wuxingPowerMap["火"] >= 150 && wuxingPowerMap["金"] >= 150 && array.Has([]string{"巳", "午", "未"}, dizhi[1]) && array.Has(array.Merge(tiangan, cangan), "丙") && array.Has(array.Merge(tiangan, cangan), "庚") {
			keys = append(keys, "[ST145]")
		}
	}
	/*
		130. 条件：以下条件任意一条满足即可
		  - 条件1：原局五行能量中，木行能量大于等于250
		  - 条件2：原局五行能量中，木行的能量大于0，小于等于30
		- 则显示：“  甲乙木太过太弱，易得肝胆之疾。”
	*/
	{
		if func() bool {
			if wuxingPowerMap["木"] >= 250 {
				return true
			}
			if wuxingPowerMap["木"] > 0 && wuxingPowerMap["木"] <= 30 {
				return true
			}
			return false
		}() {
			keys = append(keys, "[ST146]")
		}
	}
	/*
		131. 条件：以下条件任意一条满足即可
		  - 条件1：原局日柱或时柱干支，任意一柱为甲申
		  - 条件2：原局日柱或时柱干支，任意一柱为乙酉
		- 则显示：“  甲申乙酉，小儿易得肝胆之疾。”
	*/
	{
		if func() bool {
			if array.Has(ganzhi[2:4], "甲申") {
				return true
			}
			if array.Has(ganzhi[2:4], "乙酉") {
				return true
			}
			return false
		}() {
			keys = append(keys, "[ST147]")
		}
	}
	/*
		132. 条件：以下条件任意一条满足即可
		  - 条件1：原局日柱或时柱干支，任意一柱为辛卯
		  - 条件2：原局日柱或时柱干支，任意一柱为己卯
		- 则显示：“   不论男女，凡日柱或时柱是辛卯、己卯的，均好得腰疼病，断腿上有伤（疤拉）也很应验。”
	*/
	{
		if array.HasAny(ganzhi[2:4], "辛卯", "己卯") {
			keys = append(keys, "[ST148]")
		}
	}
	/*
		133. 条件：满足下述每一条条件
		  - 条件1：原局四柱天干，存在庚
		  - 条件2：原局四柱地支，同时存在寅午戌三个地支
		- 则显示：“   三合火局克庚，损头面及血疾。”
	*/
	{
		if array.Has(tiangan, "庚") && array.Has(dizhi, "寅", "午", "戌") {
			keys = append(keys, "[ST149]")
		}
	}
	/*
		134. 条件：满足下述每一条条件
		  - 条件1：原局四柱天干，存在庚或者辛
		  - 条件2：原局日柱天干，为甲或者乙
		- 则显示：“  甲乙柱见庚辛，必伤头。”
	*/
	{
		if array.Has(tiangan, "庚", "辛") && array.Has([]string{"甲", "乙"}, tiangan[2]) {
			keys = append(keys, "[ST150]")
		}
	}
	/*
		135. 条件：满足下述每一条条件
		  - 条件1：原局日主为旺衰为从弱、身弱、偏弱
		  - 条件2：原局十神能量中，伤官的能量大于等于120
		- 则显示：“   凡日主弱的人多数失眠，伤官重者尤验。凡日主弱的人多数失眠，伤官重者尤验。”
	*/
	{
		if func() bool {
			if !array.Has([]string{"从弱", "身弱", "偏弱"}, riyuan) {
				return false
			}
			if shishenPowerMap["伤官"] < 120 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST151]")
		}
	}
	/*
		136. 若原局四柱神煞中，羊刃的数量大于等于3
		- 则显示：“  柱中三羊刃，不瞎则聋。”
	*/
	{
		if array.Count(array.Merge(paipanAll.Shensha4...), "羊刃") >= 3 {
			keys = append(keys, "[ST152]")
		}
	}
	/*
		137. 条件：满足下述每一条条件
		  - 条件1：原局月柱天干、日柱天干、时柱天干对应的五行，存在克制年柱天干的五行（注意：年柱天干五行，是被克的，不是克其他五行的）
		  - 条件2：原局年柱地支，存在三刑或相刑
		- 则显示：“   年干被克年支被刑，头歪眼斜。”
	*/
	{
		if func() bool {
			nianKe := false
			for _, tg := range tiangan {
				if slf.IsWuxingXiangke(slf.GetWuxingByTiangan(tg), slf.GetWuxingByTiangan(tiangan[0])) {
					nianKe = true
					break
				}
			}
			if !nianKe {
				return false
			}
			two, three := comb23(dizhi, dizhi[0])
			for _, arr := range two {
				if slf.IsDizhiXiangxing(arr[0], arr[1]) {
					return true
				}
			}
			for _, arr := range three {
				if slf.IsDizhiSanxing(arr[0], arr[1], arr[2]) {
					return true
				}
			}
			return false
		}() {
			keys = append(keys, "[ST153]")
		}
	}
	/*
		138. 条件：满足下述每一条条件
		  - 条件1：原局四柱天干十神、地支藏干十神中，七杀的数量为1
		  - 条件2：原局四柱天干十神、地支藏干十神中，财才的数量大于等于3
		  - 条件3：原局四柱天干、地支藏干中，丙的数量及丁的数量之和为2
		- 则显示：“  一杀三才双火防目瞽。”
	*/
	{
		if func() bool {
			if ssg.NumMap["七杀"] != 1 {
				return false
			}
			if ssg.NumMap["财才"] < 3 {
				return false
			}
			if array.Count(array.Merge(tiangan, cangan), "丙", "丁") != 2 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST154]")
		}
	}
	/*
		139. 条件：满足下述每一条条件
		  - 条件1：原局命理十神能量中，官杀的能量大于等于120
		  - 条件2：原局月柱天干十神、日柱地支十神 或时柱天干十神，任意一个为七杀
		- 则显示：“  七杀有力又贴身、自坐七杀，身体方面有破相。”
	*/
	{
		if func() bool {
			if shishenPowerMap["官杀"] < 120 {
				return false
			}
			if !array.HasAny([]string{ssg.Tg[1], ssg.Dz[2], ssg.Tg[3]}, "七杀") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST155]")
		}
	}
	/*
		140. 条件：满足下述每一条条件
		  - 条件1：原局四柱神煞存在羊刃
		  - 条件2：原局四柱天干十神、地支藏干十神中，存在七杀
		  - 条件3：原局四柱天干十神、地支藏干十神中，存在伤官
		- 则显示：“  命局中伤官、七煞、羊刃并显，肢体遭伤残。”
	*/
	{
		if func() bool {
			if !array.HasAny(array.Merge(paipanAll.Shensha4...), "羊刃") {
				return false
			}
			if !array.HasAny(array.Merge(ssg.Tg, ssg.CangGan()), "七杀", "伤官") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST156]")
		}
	}
	/*
		141. 条件：满足下述每一条条件
		  - 条件1：原局四柱天干十神存在伤官
		  - 条件2：原局命理十神能量中，食伤能量大于等于150
		- 则显示：“   命局中伤官透干而气盛，一生必因病，伤留下疤痕（食神多、变伤官亦同）。”
	*/
	{
		if func() bool {
			if !array.Has(ssg.Tg, "伤官") {
				return false
			}
			if shishenPowerMap["食伤"] < 150 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST157]")
		}
	}
	/*
		142. 条件：满足下述每一条条件
		  - 条件1：原局四柱天干十神、地支藏干十神中，不存在印枭
		  - 条件2：原局四柱天干十神，存在伤官
		  - 条件3：原局四柱天干十神，存在七杀
		- 则显示：“  八字中伤官、七煞双显而缺印， 主命主好勇善斗，须防因格斗而致伤残。”
	*/
	{
		if func() bool {
			if array.Has(array.Merge(ssg.Tg, ssg.CangGan()), "印枭") {
				return false
			}
			if !array.Has(ssg.Tg, "伤官", "七杀") {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST158]")
		}
	}
	/*
		143. 原局四柱天干、地支藏干中，丙的数量及丁的数量之和大于等于4
		- 则显示：“   柱中火多必有伤身之苦或火灾。”
	*/
	{
		if array.Count(array.Merge(tiangan, cangan), "丙", "丁") >= 4 {
			keys = append(keys, "[ST159]")
		}
	}
	/*
		144. 条件：满足下述每一条条件
		  - 条件1：原局日柱天干为庚
		  - 条件2：原局月柱地支，为亥子丑之一
		- 则显示：“   庚金生冬月多有伤筋骨。”
	*/
	{
		if tiangan[2] == "庚" && array.Has([]string{"亥", "子", "丑"}, dizhi[1]) {
			keys = append(keys, "[ST160]")
		}
	}
	/*
		145. 条件：满足下述每一条条件
		  - 条件1：原局日柱天干，为壬或癸
		  - 条件2：原局命理十神能量中，官杀能量为大于等于180
		- 则显示：“   日主是水，土太多必生黄肿大肚。”
	*/
	{
		if func() bool {
			if !array.Has([]string{"壬", "癸"}, tiangan[2]) {
				return false
			}
			if shishenPowerMap["官杀"] < 180 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST161]")
		}
	}
	/*
		146. 条件：满足下述每一条条件
		  - 条件1：原局日柱天干，为甲或乙
		  - 条件2：原局命理十神能量中，官杀能量为大于等于180
		- 则显示：“  日主是木，金太多必破相，四肢疼痛折手足之患。”
	*/
	{
		if func() bool {
			if !array.Has([]string{"甲", "乙"}, tiangan[2]) {
				return false
			}
			if shishenPowerMap["官杀"] < 180 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST162]")
		}
	}
	/*
		147. 条件：满足下述每一条条件
		  - 条件1：原局日柱天干，为丙或丁
		  - 条件2：原局命理十神能量中，官杀能量为大于等于180
		- 则显示：“   日主是火，水太多必生耳聋眼盲之疾。”
	*/
	{
		if func() bool {
			if !array.Has([]string{"丙", "丁"}, tiangan[2]) {
				return false
			}
			if shishenPowerMap["官杀"] < 180 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST163]")
		}
	}
	/*
		148. 条件：满足下述每一条条件
		  - 条件1：原局日柱天干，为戊或己
		  - 条件2：原局命理十神能量中，官杀能量为大于等于180
		- 则显示：“   日主是土，木太多必眼盲损身。”
	*/
	{
		if func() bool {
			if !array.Has([]string{"戊", "己"}, tiangan[2]) {
				return false
			}
			if shishenPowerMap["官杀"] < 180 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST164]")
		}
	}
	/*
		149. 条件：满足下述每一条条件
		  - 条件1：原局日柱天干，为庚或辛
		  - 条件2：原局命理十神能量中，官杀能量为大于等于180
		- 则显示：“   日主是金，火局太堪必生咳嗽之疾。”
	*/
	{
		if func() bool {
			if !array.Has([]string{"庚", "辛"}, tiangan[2]) {
				return false
			}
			if shishenPowerMap["官杀"] < 180 {
				return false
			}
			return true
		}() {
			keys = append(keys, "[ST165]")
		}
	}
	return keys
}
