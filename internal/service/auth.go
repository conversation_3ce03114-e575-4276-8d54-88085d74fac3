package service

import (
	"context"
	"fmt"
	"github.com/google/uuid"
	"github.com/lithammer/shortuuid/v4"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"strings"
	"time"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
	"zodiacus/internal/repository"
	"zodiacus/pkg/imgcaptcha"
	"zodiacus/pkg/ocpcb"
	"zodiacus/pkg/randx"
	aliyun_sms "zodiacus/third_party/aliyun/sms"
	"zodiacus/third_party/baidu/ocpc"
	"zodiacus/third_party/submail"
	"zodiacus/third_party/umeng"
	"zodiacus/third_party/uniapp"
	wechat_app "zodiacus/third_party/wechat"
	"zodiacus/third_party/wecom"
)

type AuthService interface {
	GetUserInfo(ctx context.Context, req *v1.GetUserInfoRequest) (*v1.GetUserInfoResponseData, error)
	GenImageCode(ctx context.Context, req *v1.GenImageCodeRequest) (*v1.GenImageCodeResponseData, error)
	SMSAuth(ctx context.Context, req *v1.SMSAuthRequest) (string, error)
	LoginByPhone(ctx context.Context, req *v1.LoginByPhoneRequest) (*v1.LoginByPhoneResponseData, error)
	LoginByPhoneQuickly4Uni(ctx context.Context, req *v1.LoginByPhoneQuickly4UniRequest) (*v1.LoginByPhoneQuickly4UniResponseData, error)
	LoginByWechatMiniProgram(ctx context.Context, req *v1.LoginByWechatMiniProgramRequest) (*v1.LoginByWechatMiniProgramResponseData, error)
	WechatMiniProgramJsCode2Session(ctx context.Context, req *v1.WechatMiniProgramJsCode2SessionRequest) error
	WechatJsCode2Session(ctx context.Context, req *v1.WechatJsCode2SessionRequest) (*v1.WechatJsCode2SessionResponseData, error)
	Deactivate(ctx context.Context, req *v1.DeactivateRequest) error
	AdminLogin(ctx context.Context, req *v1.AdminLoginRequest) (*v1.AdminLoginResponseData, error)
	LoginByUMengMobileInfo(ctx context.Context, req *v1.LoginByUMengMobileInfoRequest) (*v1.LoginByUMengMobileInfoResponseData, error)
	Login(ctx context.Context, req *v1.LoginRequest) (*v1.LoginResponseData, error)
	Logout(ctx context.Context, req *v1.LogoutRequest) error
}

func NewAuthService(
	service *Service,
	userRepo repository.AppUserRepository,
	adminUserRepo repository.AdminUserRepository,
	authRepo repository.AuthRepository,
	orderRepo repository.UserOrderRepository,
	inviteRepo repository.AppUserInvitationRepository,
	doraemon *wecom.Application,
	aliyunSms *aliyun_sms.Client,
	submailer *submail.Client,
	uniappcli *uniapp.Client,
	umCli *umeng.Client,
	wxoa *wechat_app.OfficialAccount,
	wxmp *wechat_app.Miniprogram,
	ocpcli *ocpcb.Ocpcb,
) AuthService {
	return &authService{
		Service:       service,
		userRepo:      userRepo,
		adminUserRepo: adminUserRepo,
		authRepo:      authRepo,
		orderRepo:     orderRepo,
		inviteRepo:    inviteRepo,
		doraemon:      doraemon,
		aliyunSms:     aliyunSms,
		submailer:     submailer,
		uniappcli:     uniappcli,
		umCli:         umCli,
		wxoa:          wxoa,
		wxmp:          wxmp,
		ocpcli:        ocpcli,
	}
}

type authService struct {
	*Service
	userRepo      repository.AppUserRepository
	adminUserRepo repository.AdminUserRepository
	authRepo      repository.AuthRepository
	orderRepo     repository.UserOrderRepository
	inviteRepo    repository.AppUserInvitationRepository
	doraemon      *wecom.Application
	aliyunSms     *aliyun_sms.Client
	uniappcli     *uniapp.Client
	umCli         *umeng.Client
	submailer     *submail.Client
	wxoa          *wechat_app.OfficialAccount
	wxmp          *wechat_app.Miniprogram
	ocpcli        *ocpcb.Ocpcb
}

func (slf *authService) Logout(ctx context.Context, req *v1.LogoutRequest) error {
	return slf.jwthub.DelToken(ctx, req.TokenID, req.UserID)
}

func (slf *authService) Login(ctx context.Context, req *v1.LoginRequest) (*v1.LoginResponseData, error) {
	if !imgcaptcha.VerifyCaptcha(req.CaptchaID, req.CaptchaCode) {
		return nil, v1.ErrBadRequest.CustomMessage("图形验证码错误")
	}
	appUser, err := slf.userRepo.FetchAppUserByUsername(ctx, req.Username)
	if err != nil {
		return nil, err
	}
	if appUser == nil || appUser.Password != req.Password {
		return nil, v1.ErrBadRequest.CustomMessage("用户名或密码错误")
	}
	return slf.LoginResponseToken4AppUser(ctx, appUser, req.AppClient)
}

func (slf *authService) LoginByUMengMobileInfo(ctx context.Context, req *v1.LoginByUMengMobileInfoRequest) (*v1.LoginByUMengMobileInfoResponseData, error) {
	req.App = strings.ToLower(req.App)
	if req.App != "android" && req.App != "ios" {
		return nil, v1.ErrBadRequest.CustomMessage("不支持的应用类型")
	}
	phone, err := slf.umCli.UVerify().MobileInfo(ctx, req.App, req.Token, req.VerifyID)
	if err != nil {
		return nil, err
	}
	appUser, err := slf.CheckUserByPhone(ctx, phone, req.Channel, req.IP, req.OsType)
	if err != nil {
		return nil, err
	}
	return slf.LoginResponseToken4AppUser(ctx, appUser, req.AppClient)
}

func (slf *authService) AdminLogin(ctx context.Context, req *v1.AdminLoginRequest) (*v1.AdminLoginResponseData, error) {
	adminUser, err := slf.adminUserRepo.FetchAdminUserByUserName(ctx, req.UserName)
	if err != nil {
		return nil, err
	}
	if adminUser == nil {
		return nil, v1.ErrBadRequest
	}
	if adminUser.Password != req.Password {
		return nil, v1.ErrBadRequest
	}
	return slf.LoginResponseToken4AdminUser(ctx, adminUser)
}

func (slf *authService) Deactivate(ctx context.Context, req *v1.DeactivateRequest) error {
	if err := slf.jwthub.DelAllTokens(ctx, req.User.UserID); err != nil {
		return err
	}
	if err := slf.userRepo.DeleteAppUser(ctx, req.User.UserID); err != nil {
		return err
	}
	return nil
}

func (slf *authService) WechatJsCode2Session(ctx context.Context, req *v1.WechatJsCode2SessionRequest) (*v1.WechatJsCode2SessionResponseData, error) {
	var res v1.WechatJsCode2SessionResponseData
	switch req.Type {
	case 1: // 小程序
		session, err := slf.wxmp.GetAuth().Code2SessionContext(ctx, req.JsCode)
		if err != nil {
			return nil, err
		}
		if session.ErrCode != 0 {
			return nil, errors.Errorf("获取session失败：code=%d, msg=%s", session.ErrCode, session.ErrMsg)
		}
		res.OpenId = session.OpenID
		res.SessionKey = session.SessionKey
		res.UnionId = session.UnionID
	case 2: // 服务号
		token, err := slf.wxoa.GetOauth().GetUserAccessTokenContext(ctx, req.JsCode)
		if err != nil {
			return nil, err
		}
		if token.ErrCode != 0 {
			return nil, errors.Errorf("获取session失败：code=%d, msg=%s", token.ErrCode, token.ErrMsg)
		}
		res.OpenId = token.OpenID
		res.SessionKey = token.AccessToken
		res.UnionId = token.UnionID
	default:
		return nil, v1.ErrBadRequest.CustomMessage("不支持的应用类型")
	}
	return &res, nil
}

func (slf *authService) WechatMiniProgramJsCode2Session(ctx context.Context, req *v1.WechatMiniProgramJsCode2SessionRequest) error {
	session, err := slf.wxmp.GetAuth().Code2SessionContext(ctx, req.JsCode)
	if err != nil {
		return err
	}
	if session.ErrCode != 0 {
		return errors.Errorf("获取session失败：code=%d, msg=%s", session.ErrCode, session.ErrMsg)
	}
	fmt.Println(session)

	// TODO
	return nil
}

// LoginByWechatMiniProgram 微信小程序登录（获取手机号走登录流程）
func (slf *authService) LoginByWechatMiniProgram(ctx context.Context, req *v1.LoginByWechatMiniProgramRequest) (*v1.LoginByWechatMiniProgramResponseData, error) {
	phoneNumberResp, err := slf.wxmp.GetAuth().GetPhoneNumberContext(ctx, req.Code)
	if err != nil {
		return nil, err
	}
	if phoneNumberResp.ErrCode != 0 {
		return nil, errors.Errorf("获取手机号失败：code=%d, msg=%s", phoneNumberResp.ErrCode, phoneNumberResp.ErrMsg)
	}
	appUser, err := slf.CheckUserByPhone(ctx, phoneNumberResp.PhoneInfo.PhoneNumber, req.Channel, req.IP, req.OsType)
	if err != nil {
		return nil, err
	}
	return slf.LoginResponseToken4AppUser(ctx, appUser, req.AppClient)
}

// LoginByPhoneQuickly4Uni 手机号快速登录/一键登录（uniapp）
func (slf *authService) LoginByPhoneQuickly4Uni(ctx context.Context, req *v1.LoginByPhoneQuickly4UniRequest) (*v1.LoginByPhoneQuickly4UniResponseData, error) {
	uniResp, err := slf.uniappcli.QuicklyLogin(ctx, &uniapp.QuicklyLoginRequest{
		AccessToken: req.AccessToken,
		OpenId:      req.OpenId,
	})
	if err != nil {
		return nil, err
	}
	if !uniResp.Success {
		return nil, v1.ErrBadRequest.CustomMessage("登录失败")
	}
	appUser, err := slf.CheckUserByPhone(ctx, uniResp.PhoneNumber, req.Channel, req.IP, req.OsType)
	if err != nil {
		return nil, err
	}
	return slf.LoginResponseToken4AppUser(ctx, appUser, req.AppClient)
}

func (slf *authService) LoginByPhone(ctx context.Context, req *v1.LoginByPhoneRequest) (*v1.LoginByPhoneResponseData, error) {
	if req.Username == "18600000000" && req.Password == "8585" {
		appUser, err := slf.userRepo.FetchAppUserByPhone(ctx, req.Username)
		if err != nil {
			return nil, err
		}
		if appUser == nil {
			return nil, v1.ErrBadRequest
		}
		return slf.LoginResponseToken4AppUser(ctx, appUser, req.AppClient)
	}
	vc, err := slf.authRepo.FetchNewestVerificationCode(ctx, "phone", "login", req.Username)
	if err != nil {
		return nil, err
	}
	if vc == nil || vc.Code != req.Password {
		return nil, v1.ErrBadRequest.CustomMessage("短信验证码错误")
	}
	if time.Now().After(vc.ExpireTime) {
		return nil, v1.ErrBadRequest.CustomMessage("短信验证码已过期")
	}
	if vc.IsUsed {
		return nil, v1.ErrBadRequest.CustomMessage("短信验证码已使用")
	}
	if err = slf.authRepo.SetVerificationCodeUsed(ctx, vc.ID); err != nil {
		return nil, err
	}
	appUser, err := slf.CheckUserByPhone(ctx, req.Username, req.Channel, req.IP, req.OsType)
	if err != nil {
		return nil, err
	}
	return slf.LoginResponseToken4AppUser(ctx, appUser, req.AppClient)
}

// SMSAuth 发送短信验证码
func (slf *authService) SMSAuth(ctx context.Context, req *v1.SMSAuthRequest) (string, error) {
	if !imgcaptcha.VerifyCaptcha(req.ClientSecret, req.CaptchaToken) {
		return "图形验证码错误", nil
	}
	if req.Dest == "18600000000" {
		return "", nil
	}
	code := randx.RandomCode(4)
	send, err := slf.submailer.SMS().XSend(ctx, &submail.SMSXSendRequest{
		To:      req.Dest,
		Project: "hqdSn3",
		Vars:    map[string]any{"code": code},
	})
	if err != nil {
		return "", err
	}
	fmt.Println(send)
	if _, err = slf.authRepo.InsertVerificationCode(ctx, &model.VerificationCode{
		Code:       code,
		Type:       "phone",
		Scene:      "login",
		Dest:       req.Dest,
		IsUsed:     false,
		ExpireTime: time.Now().Add(time.Minute * 10).Add(time.Second * 20),
	}); err != nil {
		return "", err
	}

	return "", nil
}

// GenImageCode 生成图片验证码
func (slf *authService) GenImageCode(ctx context.Context, req *v1.GenImageCodeRequest) (*v1.GenImageCodeResponseData, error) {
	id, img, err := imgcaptcha.GetCaptcha()
	if err != nil {
		return nil, err
	}
	return &v1.GenImageCodeResponseData{
		CaptchaId:    id,
		CaptchaImage: img,
	}, nil
}

// GetUserInfo 查询用户信息：ID、昵称、会员过期时间、是否付费
func (slf *authService) GetUserInfo(ctx context.Context, req *v1.GetUserInfoRequest) (*v1.GetUserInfoResponseData, error) {
	appUser, err := slf.userRepo.FetchAppUserByUserID(ctx, req.User.UserID)
	if err != nil {
		return nil, err
	}
	if appUser == nil {
		return nil, v1.ErrUnauthorized
	}
	// 会员信息
	premiumInfo, err := slf.userRepo.FetchPremiumInfoByUserID(ctx, req.User.UserID, req.Application)
	if err != nil {
		return nil, err
	}
	var expireAt int64
	if premiumInfo != nil {
		expireAt = premiumInfo.ExpireTime.Unix()
	}
	// 是否付费（todo 是否付费标志应合并到会员信息表中）
	paid, err := slf.orderRepo.IsPaidUser(ctx, req.User.UserID)
	if err != nil {
		return nil, err
	}
	return &v1.GetUserInfoResponseData{
		UserProfileInfo: v1.UserProfileInfo{
			ID:          req.User.UserID,
			DisplayName: appUser.Nickname,
		},
		UserPremiumInfo: v1.UserPremiumInfo{
			IsPaid:     paid,
			ExpireTime: expireAt,
		},
	}, nil
}

func (slf *authService) CheckUserByPhone(ctx context.Context, phone, channel, ip, os string) (*model.AppUser, error) {
	appUser, err := slf.userRepo.FetchAppUserByPhone(ctx, phone)
	if err != nil {
		return nil, err
	}
	if appUser == nil {
		appUser = &model.AppUser{
			UserID:     uuid.New().String(),
			Username:   phone,
			Nickname:   slf.MaskPhoneNumber(phone),
			Avatar:     "",
			Phone:      phone,
			RegisterIP: ip,
		}
		if err = slf.doRegister(ctx, appUser, ip, os); err != nil {
			return nil, err
		}
	}
	return appUser, nil
}

func (slf *authService) LoginResponseToken4AppUser(ctx context.Context, appUser *model.AppUser, appCli v1.AppClient) (*v1.LoginResponseData, error) {
	defer func() {
		// 邀请注册奖励检查（买不起消息队列QAQ，只能如此）
		go func(appUser *model.AppUser) {
			var (
				ctx = context.Background()
			)
			if err := slf.doCheckInvite(ctx, appUser, appCli.IP); err != nil {
				slf.logger.Error("doCheckInvite error", zap.Any("user_id", appUser.UserID), zap.Any("register_ip", appUser.RegisterIP), zap.Error(err))
			}
		}(appUser)
	}()
	var (
		tokenID   = shortuuid.New()
		userID    = appUser.UserID
		username  = appUser.Username
		expiresAt = time.Now().Add(time.Hour * 24 * 30) // a month.
	)
	token, err := slf.jwthub.GenToken(ctx, tokenID, userID, username, expiresAt)
	if err != nil {
		return nil, err
	}
	return &v1.LoginByPhoneResponseData{
		AccessToken:  token,
		RefreshToken: "",
		ExpiresIn:    expiresAt.Unix(),
	}, nil
}

func (slf *authService) LoginResponseToken4AdminUser(ctx context.Context, adminUser *model.AdminUser) (*v1.LoginResponseData, error) {
	var (
		tokenID   = shortuuid.New()
		userID    = adminUser.UserID
		username  = adminUser.Username
		expiresAt = time.Now().Add(time.Hour * 24 * 30) // a month.
	)
	token, err := slf.jwthub.GenToken(ctx, tokenID, userID, username, expiresAt)
	if err != nil {
		return nil, err
	}
	return &v1.LoginByPhoneResponseData{
		AccessToken:  token,
		RefreshToken: "",
		ExpiresIn:    expiresAt.Unix(),
	}, nil
}

func (slf *authService) doRegister(ctx context.Context, appUser *model.AppUser, ip, os string) error {
	if _, err := slf.userRepo.CreateAppUser(ctx, appUser); err != nil {
		return err
	}
	// 回传注册
	go func(appUser *model.AppUser) {
		var (
			ctx = context.Background()
		)
		if err := slf.ocpcli.CbActivate(ctx, &ocpcb.CbActivateRequest{
			CbType:           ocpc.ConversionTypeRegister,
			CbValue:          0,
			AppUserID:        "",
			Imei:             "",
			Oaid:             "",
			AndroidID:        "",
			IP:               ip,
			OsType:           lo.Ternary(os == "Android", "2", "1"),
			OsVersion:        "",
			Idfa:             "",
			Caid:             "",
			Mac:              "",
			CbTime:           time.Now(),
			BindingAppUserID: appUser.UserID,
		}); err != nil {
			slf.logger.Error("ocpcli.CbActivate error", zap.Any("user_id", appUser.UserID), zap.Any("register_ip", appUser.RegisterIP), zap.Error(err))
		}
	}(appUser)

	return nil
}

func (slf *authService) doCheckInvite(ctx context.Context, invitee *model.AppUser, ip string) error {
	if ip == "" {
		return nil
	}
	hasInvited, err := slf.inviteRepo.IsThisPhoneHasInvited(ctx, invitee.Phone)
	if err != nil {
		return err
	}
	if hasInvited {
		return nil
	}
	binding, err := slf.inviteRepo.FetchInviteBindingIPByIP(ctx, ip)
	if err != nil {
		return err
	}
	if binding == nil {
		return err
	}
	if binding.IsExpired() {
		return nil
	}
	code, err := slf.inviteRepo.GetInviteCode(ctx, binding.InviteCode)
	if err != nil {
		return err
	}
	if code == nil {
		return errors.Errorf("邀请码不存在: %s", binding.InviteCode)
	}
	inviter, err := slf.userRepo.FetchAppUserByUserID(ctx, code.UserID)
	if err != nil {
		return err
	}
	if inviter == nil {
		return errors.Errorf("邀请人不存在: %s", code.UserID)
	}
	level, err := slf.inviteRepo.FetchInviteLevel(ctx, code.Level)
	if err != nil {
		return err
	}
	if level == nil {
		return errors.Errorf("邀请码等级不存在: %d", code.Level)
	}
	var (
		inviterGift        = time.Hour * time.Duration(level.InviterGiftVipHours)
		inviteeGift        = time.Hour * time.Duration(level.InviteeGiftVipHours)
		inviterGiftSeconds = int64(inviterGift.Seconds())
		inviteeGiftSeconds = int64(inviteeGift.Seconds())
		record             = &model.InviteRecord{
			ID:                 slf.sid.Int64(),
			InviterID:          code.UserID,
			InviterName:        inviter.Nickname,
			InviterVipDuration: inviterGiftSeconds,
			InviteeID:          invitee.UserID,
			InviteeName:        invitee.Nickname,
			InviteeVipDuration: inviteeGiftSeconds,
			InviteCode:         code.Code,
			InviteTime:         time.Now(),
		}
	)
	// 查询是否会员
	if err = slf.tx.Transaction(ctx, func(ctx context.Context) error {
		role := int64(1) // 排盘会员为1
		if err = slf.inviteRepo.CreateInviteRecord(ctx, record); err != nil {
			return err
		}
		// 被邀请者
		inviteeDuration, err := slf.inviteRepo.GetAppVipDuration(ctx, record.InviteeID, role)
		if err != nil {
			return err
		}
		if inviteeDuration == nil {
			inviteeDuration = &model.MemberDuration{
				ID:         slf.sid.Int64(),
				UserID:     record.InviteeID,
				Role:       role,
				ExpireTime: time.Now().Add(inviteeGift),
			}
			if err = slf.inviteRepo.CreateAppVipDuration(ctx, inviteeDuration); err != nil {
				return err
			}
		} else {
			if inviteeDuration.IsValid() {
				inviteeDuration.ExpireTime = inviteeDuration.ExpireTime.Add(inviteeGift)
			} else {
				inviteeDuration.ExpireTime = time.Now().Add(inviteeGift)
			}
			if err = slf.inviteRepo.UpdateAppVipDuration(ctx, inviteeDuration); err != nil {
				return err
			}
		}
		// 邀请者
		inviterDuration, err := slf.inviteRepo.GetAppVipDuration(ctx, record.InviterID, role)
		if err != nil {
			return err
		}
		if inviterDuration == nil {
			inviterDuration = &model.MemberDuration{
				ID:         slf.sid.Int64(),
				UserID:     record.InviterID,
				Role:       role,
				ExpireTime: time.Now().Add(inviterGift),
			}
			if err = slf.inviteRepo.CreateAppVipDuration(ctx, inviterDuration); err != nil {
				return err
			}
		} else {
			if inviterDuration.IsValid() {
				inviterDuration.ExpireTime = inviterDuration.ExpireTime.Add(inviterGift)
			} else {
				inviterDuration.ExpireTime = time.Now().Add(inviterGift)
			}
			if err = slf.inviteRepo.UpdateAppVipDuration(ctx, inviterDuration); err != nil {
				return err
			}
		}
		return nil
	}); err != nil {
		return err
	}
	return nil
}
