package service

import (
	"context"
	"sort"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
	"zodiacus/internal/repository"
)

type LocationService interface {
	Location(ctx context.Context, req *v1.LocationRequest) (v1.LocationResponseData, error)
}

func NewLocationService(
	service *Service,
	enumsRepo repository.EnumsRepository,
) LocationService {
	return &locationService{
		Service:   service,
		enumsRepo: enumsRepo,
	}
}

type locationService struct {
	*Service
	enumsRepo repository.EnumsRepository
}

func (slf *locationService) Location(ctx context.Context, req *v1.EnumsLocationRequest) ([]*v1.LocationTree, error) {
	if !req.Overseas {
		return slf.location(ctx)
	}
	return slf.locationOverseas(ctx)
}

func (slf *locationService) locationOverseas(ctx context.Context) ([]*v1.LocationTree, error) {
	countries, err := slf.enumsRepo.FetchAllCountry(ctx)
	if err != nil {
		return nil, err
	}
	provinces, err := slf.enumsRepo.FetchAllCountryProvince(ctx)
	if err != nil {
		return nil, err
	}
	return slf.buildLocationOverseasTree(ctx, countries, provinces), nil
}

func (slf *locationService) buildLocationOverseasTree(_ context.Context, countries []*model.AddrCountry, provinces []*model.AddrCountryProvince) []*v1.LocationTree {
	countryMap := make(map[int64]*v1.LocationTree)
	for _, country := range countries {
		countryMap[country.Code] = &v1.LocationTree{
			Code: country.Code,
			Name: country.Name,
		}
	}
	provinceMap := make(map[int64]*v1.LocationTree)
	for _, province := range provinces {
		provinceNode := &v1.LocationTree{
			Code: province.Code,
			Name: province.Name,
		}
		countryNode, ok := countryMap[province.ParentsID]
		if ok {
			countryNode.Children = append(countryNode.Children, provinceNode)
			provinceMap[province.Code] = provinceNode
		}
	}
	var result []*v1.LocationTree
	for _, country := range countryMap {
		result = append(result, country)
	}
	slf.sortLocationTree(result)
	return result
}

func (slf *locationService) location(ctx context.Context) ([]*v1.LocationTree, error) {
	provinces, err := slf.enumsRepo.FetchAllAddrProvince(ctx)
	if err != nil {
		return nil, err
	}
	cities, err := slf.enumsRepo.FetchAllAddrCity(ctx)
	if err != nil {
		return nil, err
	}
	areas, err := slf.enumsRepo.FetchAllArea(ctx)
	if err != nil {
		return nil, err
	}
	return slf.buildLocationTree(ctx, provinces, cities, areas), nil
}

func (slf *locationService) buildLocationTree(_ context.Context, provinces []*model.AddrProvince, cities []*model.AddrCity, areas []*model.AddrArea) []*v1.LocationTree {
	provinceMap := make(map[int64]*v1.LocationTree)
	for _, province := range provinces {
		provinceMap[province.Code] = &v1.LocationTree{
			Code: province.Code,
			Name: province.Name,
		}
	}
	cityMap := make(map[int64]*v1.LocationTree)
	for _, city := range cities {
		cityNode := &v1.LocationTree{
			Code: city.Code,
			Name: city.Name,
		}
		provinceNode, ok := provinceMap[city.ProvinceCode]
		if ok {
			provinceNode.Children = append(provinceNode.Children, cityNode)
			cityMap[city.Code] = cityNode
		}
	}
	for _, area := range areas {
		areaNode := &v1.LocationTree{
			Code: area.Code,
			Name: area.Name,
		}
		cityNode, ok := cityMap[area.CityCode]
		if ok {
			cityNode.Children = append(cityNode.Children, areaNode)
		}
	}
	var result []*v1.LocationTree
	for _, province := range provinceMap {
		result = append(result, province)
	}
	slf.sortLocationTree(result)
	return result
}

func (slf *locationService) sortLocationTree(trees []*v1.LocationTree) {
	sort.Slice(trees, func(i, j int) bool {
		return trees[i].Code < trees[j].Code
	})
	for _, tree := range trees {
		if len(tree.Children) > 0 {
			slf.sortLocationTree(tree.Children)
		}
	}
}
