package service

import (
	"context"
	"go.uber.org/zap"
	"zodiacus/internal/model"
	"zodiacus/internal/repository"
	"zodiacus/third_party/baidu/ocpc"
)

type OcpcService interface {
	Notice(ctx context.Context, req *ocpc.ClickData) error
}

func NewOcpcService(svc *Service, ocpcRepo repository.OcpcRepository) OcpcService {
	return &ocpcService{
		Service:  svc,
		ocpcRepo: ocpcRepo,
	}
}

type ocpcService struct {
	*Service
	ocpcRepo repository.OcpcRepository
}

func (slf *ocpcService) Notice(ctx context.Context, req *ocpc.ClickData) error {
	if _, err := slf.ocpcRepo.CreateOcpcClick(ctx, &model.BaiduOcpcClick{
		Akey:             "NjkyNTE2NDM=",
		Userid:           req.Userid,
		IdeaID:           req.IdeaID,
		PlanID:           req.PlanID,
		UnitID:           req.UnitID,
		Productid:        req.Productid,
		CallbackUrl:      req.CallbackUrl,
		ClickID:          req.ClickID,
		Idfa:             req.Idfa,
		ImeiMd5:          req.ImeiMd5,
		Oaid:             req.Oaid,
		OaidMd5:          req.OaidMd5,
		AndroidIdMd5:     req.AndroidIdMd5,
		Mac:              req.Mac,
		MacMd5:           req.MacMd5,
		Ip:               req.Ip,
		Ipv6:             req.Ipv6,
		IpType:           req.IpType,
		UA:               req.UA,
		OsVersion:        req.OsVersion,
		OsType:           req.OsType,
		Size:             req.Size,
		TS:               req.TS,
		InteractionsType: req.InteractionsType,
		ExtInfo:          req.ExtInfo,
		CallType:         req.CallType,
		Caid:             req.Caid,
	}); err != nil {
		slf.logger.WithContext(ctx).Error("ocpcService.Notice: CreateOcpcClick", zap.Error(err))
		return err
	}
	return nil
}
