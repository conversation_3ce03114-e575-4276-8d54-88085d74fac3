package service

import (
	"context"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
	"zodiacus/internal/repository"
)

type AtlasProductService interface {
	ListAtlasProduct(ctx context.Context, req *v1.AtlasListProductRequest) (*v1.AtlasListProductResponse, error)
	CreateAtlasProduct(ctx context.Context, req *v1.AtlasCreateProductReq) (int64, error)
	DeleteAtlasProduct(ctx context.Context, req int64) error
	UpdateAtlasProduct(ctx context.Context, req *v1.AtlasUpdateProductReq) error
}

func NewAtlasProductService(service *Service, atlasProductRepo repository.AtlasProductRepository) AtlasProductService {
	return &atlasProductService{
		atlasProductRepo: atlasProductRepo,
		Service:          service,
	}
}

type atlasProductService struct {
	atlasProductRepo repository.AtlasProductRepository
	*Service
}

func (at *atlasProductService) ListAtlasProduct(ctx context.Context, req *v1.AtlasListProductRequest) (*v1.AtlasListProductResponse, error) {
	return at.atlasProductRepo.ListAtlasProduct(ctx, req)
}

func (at *atlasProductService) CreateAtlasProduct(ctx context.Context, req *v1.AtlasCreateProductReq) (int64, error) {
	product := &model.Product{
		SkuCode:       at.sid.String(),
		ProductCreate: req.ProductCreate,
	}

	return at.atlasProductRepo.CreateAtlasProduct(ctx, product)
}

func (at *atlasProductService) DeleteAtlasProduct(ctx context.Context, req int64) error {
	return at.atlasProductRepo.DeleteAtlasProduct(ctx, req)
}

func (at *atlasProductService) UpdateAtlasProduct(ctx context.Context, req *v1.AtlasUpdateProductReq) error {
	return at.atlasProductRepo.UpdateAtlasProduct(ctx, req)
}
