package service

import (
	"context"
	"github.com/google/uuid"
	"github.com/lithammer/shortuuid/v4"
	"strings"
	"time"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
	"zodiacus/internal/repository"
	"zodiacus/pkg/randstring"
)

type AppUserPromotionService interface {
	CreateAppUserPromotion(ctx context.Context, req *v1.CreateAppUserPromotionRequest) (*v1.CreateAppUserPromotionResponseData, error)
	UpdateAppUserPromotion(ctx context.Context, req *v1.UpdateAppUserPromotionRequest) error
	PageListAppUserPromotion(ctx context.Context, req *v1.PageListAppUserPromotionRequest) (*v1.PageListAppUserPromotionResponseData, error)
	PersonalPromotion(ctx context.Context, req *v1.PersonalPromotionRequest) (*v1.PersonalPromotionResponseData, error)
}

func NewAppUserPromotionService(
	service *Service,
	appUserPromotionRepo repository.AppUserPromotionRepository,
	invitationRepository repository.AppUserInvitationRepository,
	appUserRepo repository.AppUserRepository,
) AppUserPromotionService {
	return &appUserPromotionService{
		Service:              service,
		appUserPromotionRepo: appUserPromotionRepo,
		invitationRepository: invitationRepository,
		appUserRepo:          appUserRepo,
	}
}

type appUserPromotionService struct {
	*Service
	appUserPromotionRepo repository.AppUserPromotionRepository
	invitationRepository repository.AppUserInvitationRepository
	appUserRepo          repository.AppUserRepository
}

func (slf *appUserPromotionService) PersonalPromotion(ctx context.Context, req *v1.PersonalPromotionRequest) (*v1.PersonalPromotionResponseData, error) {
	promotion, err := slf.appUserPromotionRepo.FetchAppUserPromotionByUserID(ctx, req.User.UserID)
	if err != nil {
		return nil, err
	}
	if promotion == nil {
		return nil, nil
	}
	appUser, err := slf.appUserRepo.FetchAppUserByUserID(ctx, promotion.UserID)
	if err != nil {
		return nil, err
	}
	if appUser == nil {
		return nil, v1.ErrBadRequest
	}
	tree, err := slf.invitationRepository.FetchInviteTree2LevelWithRecharge(ctx, appUser.UserID)
	if err != nil {
		return nil, err
	}
	var (
		level1             []*model.UserInviteTree2LevelWithRecharge
		level2             []*model.UserInviteTree2LevelWithRecharge
		level1UserRecharge int
		level2UserRecharge int
		level1Rebate       int
		level2Rebate       int
		rebateTotal        int
	)
	for _, item := range tree {
		switch item.Level {
		case 1:
			level1 = append(level1, item)
			level1UserRecharge += item.RechargeAmount
		case 2:
			level2 = append(level2, item)
			level2UserRecharge += item.RechargeAmount
		default:
		}
	}
	level1Rebate = int(float64(level1UserRecharge) * float64(promotion.Level1Ratio) / 100)
	level2Rebate = int(float64(level2UserRecharge) * float64(promotion.Level2Ratio) / 100)
	rebateTotal = level1Rebate + level2Rebate
	return &v1.PersonalPromotionResponseData{
		Name:               appUser.Nickname,
		PromotionCode:      promotion.PromotionCode,
		RebateTotal:        rebateTotal,
		RebateWithdrawn:    promotion.RebateWithdrawn,
		RebateAvailable:    rebateTotal - promotion.RebateWithdrawn,
		Level1UserCount:    len(level1),
		Level1UserRecharge: level1UserRecharge,
		Level1Rebate:       level1Rebate,
		Level2UserCount:    len(level2),
		Level2UserRecharge: level2UserRecharge,
		Level2Rebate:       level2Rebate,
		StatTime:           time.Now(),
	}, nil
}

func (slf *appUserPromotionService) CreateAppUserPromotion(ctx context.Context, req *v1.CreateAppUserPromotionRequest) (*v1.CreateAppUserPromotionResponseData, error) {
	var id int64
	if err := slf.tx.Transaction(ctx, func(ctx context.Context) error {
		appUser, err := slf.appUserRepo.FetchAppUserByPhone(ctx, req.Phone)
		if err != nil {
			return err
		}
		var inviteCode *model.InviteCode
		if appUser == nil {
			appUser = &model.AppUser{
				UserID:   uuid.New().String(),
				Username: req.Phone,
				Nickname: req.Name,
				Phone:    req.Phone,
				Password: shortuuid.New(),
			}
			if _, err = slf.appUserRepo.CreateAppUser(ctx, appUser); err != nil {
				return err
			}
			inviteCode = &model.InviteCode{
				ID:     slf.sid.Int64(),
				UserID: appUser.UserID,
			}
			for {
				inviteCode.Code = slf.GenerateInviteCode(appUser.UserID, randstring.RandomStr(4, randstring.CharsetAlphaNumeric), 4)
				ok, err := slf.invitationRepository.CreateInviteCode(ctx, inviteCode)
				if err != nil && !strings.Contains(err.Error(), "Duplicate entry") {
					return err
				}
				if ok {
					break
				}
			}
		} else {
			if appUser.Nickname != req.Name {
				appUser.Nickname = req.Name
				if appUser.Password == "" {
					appUser.Password = shortuuid.New()
				}
				if err = slf.appUserRepo.UpdateAppUser(ctx, appUser); err != nil {
					return err
				}
			}
			inviteCode, err = slf.invitationRepository.GetInviteCodeByUID(ctx, appUser.UserID)
			if err != nil {
				return err
			}
			if inviteCode == nil {
				inviteCode = &model.InviteCode{
					ID:     slf.sid.Int64(),
					UserID: appUser.UserID,
				}
				for {
					inviteCode.Code = slf.GenerateInviteCode(appUser.UserID, randstring.RandomStr(4, randstring.CharsetAlphaNumeric), 4)
					ok, err := slf.invitationRepository.CreateInviteCode(ctx, inviteCode)
					if err != nil && !strings.Contains(err.Error(), "Duplicate entry") {
						return err
					}
					if ok {
						break
					}
				}
			} else {
				// 升级时如果已被邀请，则邀请者更新为系统管理员。
				if inviteCode.Level != 1 {
					inviteCode.Level = 1
					if err = slf.invitationRepository.UpdateInviteCode(ctx, inviteCode); err != nil {
						return err
					}
					inviteRecord, err := slf.invitationRepository.GetInviteRecordByInvitee(ctx, appUser.UserID)
					if err != nil {
						return err
					}
					if inviteRecord != nil {
						inviteRecord.InviterID = "c9ffd8e5-1cd4-4cb0-81ef-503c5108fd87"
						inviteRecord.InviterName = "推广管理员"
						inviteRecord.InviteCode = "UCZQ"
						if err = slf.invitationRepository.UpdateInviteRecord(ctx, inviteRecord); err != nil {
							return err
						}
					} else {
						if err = slf.invitationRepository.CreateInviteRecord(ctx, &model.InviteRecord{
							InviterID:          "c9ffd8e5-1cd4-4cb0-81ef-503c5108fd87",
							InviterName:        "推广管理员",
							InviterVipDuration: 0,
							InviterRead:        true,
							InviteeID:          appUser.UserID,
							InviteeName:        appUser.Nickname,
							InviteeVipDuration: 0,
							InviteeRead:        true,
							InviteCode:         "UCZQ",
							InviteTime:         time.Now(),
						}); err != nil {
							return err
						}
					}
				}
			}
		}
		id, err = slf.appUserPromotionRepo.CreateAppUserPromotion(ctx, &model.AppUserPromotion{
			UserID:        appUser.UserID,
			PromotionCode: inviteCode.Code,
			Level1Ratio:   req.Level1Ratio,
			Level2Ratio:   req.Level2Ratio,
			Remark:        req.Remark,
		})
		if err != nil {
			return err
		}
		return nil
	}); err != nil {
		return nil, err
	}
	return &v1.CreateAppUserPromotionResponseData{ID: id}, nil
}

func (slf *appUserPromotionService) UpdateAppUserPromotion(ctx context.Context, req *v1.UpdateAppUserPromotionRequest) error {
	if err := slf.tx.Transaction(ctx, func(ctx context.Context) error {
		promotion, err := slf.appUserPromotionRepo.FetchAppUserPromotionByID(ctx, req.ID)
		if err != nil {
			return err
		}
		if promotion == nil {
			return v1.ErrBadRequest
		}
		appUser, err := slf.appUserRepo.FetchAppUserByUserID(ctx, promotion.UserID)
		if err != nil {
			return err
		}
		if appUser == nil {
			return v1.ErrBadRequest
		}
		if req.Level1Ratio != nil {
			promotion.Level1Ratio = *req.Level1Ratio
		}
		if req.Level2Ratio != nil {
			promotion.Level2Ratio = *req.Level2Ratio
		}
		if req.Remark != nil {
			promotion.Remark = *req.Remark
		}
		if err = slf.appUserPromotionRepo.UpdateAppUserPromotion(ctx, promotion); err != nil {
			return err
		}
		if req.Name != nil {
			appUser.Nickname = *req.Name
		}
		if req.Password != nil {
			appUser.Password = *req.Password
		}
		if err = slf.appUserRepo.UpdateAppUser(ctx, appUser); err != nil {
			return err
		}
		return nil
	}); err != nil {
		return err
	}
	return nil
}

func (slf *appUserPromotionService) PageListAppUserPromotion(ctx context.Context, req *v1.PageListAppUserPromotionRequest) (*v1.PageListAppUserPromotionResponseData, error) {
	return slf.appUserPromotionRepo.PageListAppUserPromotion(ctx, req)
}
