package service

import (
	"context"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/repository"
)

type FeedbackService interface {
	PageListFeedback(ctx context.Context, req *v1.PageListFeedbackRequest) (*v1.PageListFeedbackResponseData, error)
	ReplyFeedback(ctx context.Context, req *v1.ReplyFeedbackRequest) error
}

func NewFeedbackService(
	service *Service,
	fdRepo repository.FeedbackRepository,
) FeedbackService {
	return &feedbackService{
		Service: service,
		fdRepo:  fdRepo,
	}
}

type feedbackService struct {
	*Service
	fdRepo repository.FeedbackRepository
}

func (slf *feedbackService) PageListFeedback(ctx context.Context, req *v1.PageListFeedbackRequest) (*v1.PageListFeedbackResponseData, error) {
	result, err := slf.fdRepo.PageListFeedback(ctx, req)
	if err != nil {
		return nil, err
	}
	for _, fb := range result.List {
		fb.ImageUrl, err = slf.oss.GetObjectSignUrls(fb.ImageKey)
		if err != nil {
			return nil, err
		}
		fb.CreatedAtStr = fb.CreatedAt.Format("2006-01-02 15:04:05")
		fb.ReplyImageUrl, err = slf.oss.GetObjectSignUrls(fb.ReplyImageKey)
		if !fb.ReplyTime.IsZero() {
			fb.ReplyTimeStr = fb.ReplyTime.Format("2006-01-02 15:04:05")
		}
	}
	return result, nil
}

func (slf *feedbackService) ReplyFeedback(ctx context.Context, req *v1.ReplyFeedbackRequest) error {
	if err := slf.fdRepo.ReplyFeedback(ctx, req); err != nil {
		return err
	}
	return nil
}
