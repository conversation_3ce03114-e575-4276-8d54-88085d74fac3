package service

import (
	"context"
	"slices"
	"strings"
	"time"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
	"zodiacus/pkg/array"
)

// <PERSON><PERSON><PERSON> 食伤
func (slf *masterService) Shi<PERSON><PERSON>(rgwx string) string {
	return slf.TblWuxingXiangsheng[rgwx]
}

// CaiCai 财才
func (slf *masterService) CaiCai(rgwx string) string {
	return slf.TblWuxingXiangke[rgwx]
}

// BiJie 比劫
func (slf *masterService) BiJie(rgwx string) string {
	return rgwx
}

// YinXiao 印枭
func (slf *masterService) YinXiao(rgwx string) string {
	for k, v := range slf.TblWuxingXiangsheng {
		if v == rgwx {
			return k
		}
	}
	return ""
}

// GuanSha 官杀
func (slf *masterService) GuanSha(rgwx string) string {
	for k, v := range slf.TblWuxingXiangke {
		if v == rgwx {
			return k
		}
	}
	return ""
}

func (slf *masterService) HasYinXiao(input []string) bool {
	for _, s := range []string{"正印", "偏印"} {
		if array.Has(input, s) {
			return true
		}
	}
	return false
}

func (slf *masterService) HasGuanSha(input []string) bool {
	for _, s := range []string{"正官", "七杀"} {
		if array.Has(input, s) {
			return true
		}
	}
	return false
}

func (slf *masterService) HasCaiCai(input []string) bool {
	for _, s := range []string{"正财", "偏财"} {
		if array.Has(input, s) {
			return true
		}
	}
	return false
}

func (slf *masterService) HasShiShang(input []string) bool {
	for _, s := range []string{"食神", "伤官"} {
		if array.Has(input, s) {
			return true
		}
	}
	return false
}

func (slf *masterService) HasBijie(input []string) bool {
	for _, s := range []string{"比肩", "劫财"} {
		if array.Has(input, s) {
			return true
		}
	}
	return false
}

// BirthtimeSun 获取真太阳时间
func (slf *masterService) realSunTime(ctx context.Context, birthtime string, location []string) (string, error) {
	if len(location) == 0 {
		return birthtime, nil
	}
	join := strings.Join(location, "")
	if len(location) == 3 {
		join = "中国" + join
	}
	offset, err := slf.masterRepo.GetOffset4TimeByLocation(ctx, join)
	if err != nil {
		return "", err
	}
	parse, err := time.Parse("2006-01-02 15:04:05", birthtime)
	if err != nil {
		return "", err
	}
	return parse.Add(time.Duration(offset) * time.Minute).Format("2006-01-02 15:04:05"), nil
}

func (slf *masterService) MatchMingliRule(ctx context.Context, rule *model.MingliRule, conditions []*model.MingliRuleCondition, param *v1.MatchMingliRulesInput) (*v1.MatchMingliRulesResponseDataRule, error) {
	if len(conditions) == 0 {
		return nil, nil
	}
	matched := make([]*v1.MatchMingliRuleResponseDataCondition, 0)
	for _, condition := range conditions {
		ok, err := slf.MatchMingliRuleCondition(ctx, condition, param)
		if err != nil {
			return nil, err
		}
		if !ok {
			continue
		}
		matched = append(matched, &v1.MatchMingliRuleResponseDataCondition{
			ID:        condition.ID,
			Criterion: condition.Criterion,
		})
	}
	if len(matched) == 0 {
		return nil, nil
	}
	return &v1.MatchMingliRulesResponseDataRule{
		ID:         rule.ID,
		Result:     rule.Result,
		Conditions: matched,
	}, nil
}

func (slf *masterService) MatchMingliRuleCondition(ctx context.Context, condition *model.MingliRuleCondition, param *v1.MatchMingliRulesInput) (bool, error) {
	switch condition.Category {
	case 2:
		return slf.MatchMingliRuleConditionZuodui(ctx, condition, param)
	case 3:
		return slf.MatchMingliRuleConditionXiji(ctx, condition, param)
	default:
		if ok, err := slf.MatchMingliRuleConditionZuodui(ctx, condition, param); err != nil {
			return false, err
		} else if ok {
			return true, nil
		}
		return slf.MatchMingliRuleConditionXiji(ctx, condition, param)
	}
}

func (slf *masterService) MatchMingliRuleConditionZuodui(_ context.Context, condition *model.MingliRuleCondition, param *v1.MatchMingliRulesInput) (bool, error) {
	kvs := array.Filter(condition.Zuodui, func(kv *model.MingliRuleConditionKV) bool {
		return kv.Checked
	})
	typ, exist := param.ZuoduiMap[condition.Type]
	if !exist || typ == nil {
		return false, nil
	}
	zuo := array.Copy(condition.WeizhiZuo, func(i int) int { return i - 1 })
	dui := array.Copy(condition.WeizhiDui, func(i int) int { return i - 1 })
	var (
		keyIDs []int64
	)
	switch typ.Zuo {
	case "干":
		keyIDs = array.SubArray(param.Tiangan, zuo...)
	case "支":
		keyIDs = array.SubArray(param.Dizhi, zuo...)
	case "干支":
		keyIDs = array.SubArray(param.Ganzhi, zuo...)
	case "纳音":
		keyIDs = array.SubArray(param.Nayin, zuo...)
	case "十神":
		keyIDs = array.SubArray(param.Shishen, zuo...)
	case "干十神":
		keyIDs = array.SubArray(param.Zhuxing, zuo...)
	case "支十神":
		array.Range(array.SubArray(param.Fuxing, zuo...), func(ids []int64) {
			keyIDs = append(keyIDs, ids...)
		})
	case "天干自计算":
		for _, kv := range kvs {
			found := false
			for _, tiangan := range param.Tiangan {
				if kv.Key == tiangan {
					found = true
					break
				}
			}
			if !found {
				return false, nil
			}
		}
		return true, nil
	case "地支自计算":
		for _, kv := range kvs {
			found := false
			for _, dizhi := range param.Dizhi {
				if kv.Key == dizhi {
					found = true
					break
				}
			}
			if !found {
				return false, nil
			}
		}
		return true, nil
	case "干支一柱":
		for _, kv := range kvs {
			found := false
			for _, ganzhi := range param.Ganzhi {
				if kv.Key == ganzhi {
					found = true
					break
				}
			}
			if !found {
				return false, nil
			}
		}
		return true, nil
	default:
	}
	keyIDs = array.Unique(keyIDs)
	keyIDs = array.Filter(keyIDs, func(id int64) bool {
		return id > 0
	})
	items := array.Filter(kvs, func(kv *model.MingliRuleConditionKV) bool {
		return array.Exist(keyIDs, func(id int64) bool {
			return kv.Key == id
		})
	})
	var (
		valueIDs []int64
	)
	switch typ.Dui {
	case "干":
		valueIDs = array.SubArray(param.Tiangan, dui...)
	case "支":
		valueIDs = array.SubArray(param.Dizhi, dui...)
	case "干支":
		valueIDs = array.SubArray(param.Ganzhi, dui...)
	case "十神":
		valueIDs = array.SubArray(param.Shishen, dui...)
	case "干十神":
		valueIDs = array.SubArray(param.Zhuxing, dui...)
	case "支十神":
		array.Range(array.SubArray(param.Fuxing, dui...), func(ids []int64) {
			valueIDs = append(valueIDs, ids...)
		})
	case "纳音":
		valueIDs = array.SubArray(param.Nayin, dui...)
	default:
		// do nothing
	}
	valueIDs = array.Unique(valueIDs)
	valueIDs = array.Filter(valueIDs, func(id int64) bool {
		return id > 0
	})
	return array.Exist(items, func(kv *model.MingliRuleConditionKV) bool {
		return array.Exist(valueIDs, func(id int64) bool {
			return array.Exist(kv.Values, func(value model.MingliRuleConditionValue) bool {
				return value.Value == id
			})
		})
	}), nil
}

func (slf *masterService) MatchMingliRuleConditionXiji(_ context.Context, condition *model.MingliRuleCondition, param *v1.MatchMingliRulesInput) (bool, error) {
	return array.Exist(condition.Xiji, func(kv *model.MingliRuleConditionKV) bool {
		if !kv.Checked {
			return false
		}
		for _, value := range kv.Values {
			found := false
			switch value.Type {
			case 4: // 十神
				for _, shishen := range param.Shishen {
					if shishen == 0 {
						continue
					}
					if shishen == value.Value {
						found = true
						break
					}
				}
			case 5: // 五行
				if param.Wuxing[kv.Key-1] == value.Value {
					found = true
				}
			}
			if found {
				return true
			}
		}
		return false
	}), nil
}

// IsYangDizhi 判断地支是否为阳地支
func (slf *masterService) IsYangDizhi(dizhi string) bool {
	yangDizhi := []string{"子", "寅", "辰", "午", "申", "戌"}
	return slices.Contains(yangDizhi, dizhi)
}

// IsNextDizhi 判断第二个地支是否为第一个地支的下一个地支（考虑首尾相接）
func (slf *masterService) IsNextDizhi(first, second string) bool {
	dizhiOrder := []string{"子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"}

	firstIndex := slices.Index(dizhiOrder, first)
	if firstIndex == -1 {
		return false
	}

	nextIndex := (firstIndex + 1) % 12
	return dizhiOrder[nextIndex] == second
}

// IsPrevDizhi 判断第二个地支是否为第一个地支的上一个地支（考虑首尾相接）
func (slf *masterService) IsPrevDizhi(first, second string) bool {
	dizhiOrder := []string{"子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"}

	firstIndex := slices.Index(dizhiOrder, first)
	if firstIndex == -1 {
		return false
	}

	prevIndex := (firstIndex - 1 + 12) % 12
	return dizhiOrder[prevIndex] == second
}
