package middleware

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/duke-git/lancet/v2/cryptor"
	"github.com/duke-git/lancet/v2/random"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"io"
	"strings"
	"time"
	"zodiacus/pkg/log"
)

func RequestLogMiddleware(logger *log.Logger) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		uuid, err := random.UUIdV4()
		if err != nil {
			return
		}
		traceId := cryptor.Md5String(uuid)
		ctx.Set("trace_id", traceId)
		ctx.Set("request_url", ctx.Request.URL.String())
		ctx.Writer.Header().Set("X-Trace-ID", traceId)
		logger.WithValue(ctx, zap.String("trace_id", traceId))
		logger.WithValue(ctx, zap.String("request_url", ctx.Request.URL.String()))
		var (
			requestMethod  = ctx.Request.Method
			requestHeaders = ctx.Request.Header
			requestUrl     = ctx.Request.URL.String()
			requestParams  string
		)
		if strings.HasPrefix(ctx.GetHeader("Content-Type"), "application/json") {
			bodyBytes, _ := ctx.GetRawData()
			ctx.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
			var reqObj map[string]any
			_ = json.Unmarshal(bodyBytes, &reqObj)
			logger.WithContext(ctx).Info("Request", zap.String("request_method", requestMethod), zap.Any("request_headers", requestHeaders), zap.String("request_url", requestUrl), zap.Any("request_params", reqObj))
		} else if strings.HasPrefix(ctx.GetHeader("Content-Type"), "multipart/form-data") {
			if ctx.Request.ParseMultipartForm(32<<20) == nil && ctx.Request.MultipartForm != nil {
				for key, values := range ctx.Request.MultipartForm.Value {
					for _, value := range values {
						requestParams += fmt.Sprintf("%s=%s&", key, value)
					}
				}
				for key := range ctx.Request.MultipartForm.File {
					requestParams += fmt.Sprintf("file: %s&", key)
				}
			}
			logger.WithContext(ctx).Info("Request", zap.String("request_method", requestMethod), zap.Any("request_headers", requestHeaders), zap.String("request_url", requestUrl), zap.String("request_params", requestParams))
		} else {
			if ctx.Request.Body != nil {
				bodyBytes, _ := ctx.GetRawData()
				ctx.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
				requestParams = string(bodyBytes)
			}
			logger.WithContext(ctx).Info("Request", zap.String("request_method", requestMethod), zap.Any("request_headers", requestHeaders), zap.String("request_url", requestUrl), zap.String("request_params", requestParams))
		}
		ctx.Next()
	}
}

func ResponseLogMiddleware(logger *log.Logger) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		blw := &bodyLogWriter{body: bytes.NewBufferString(""), ResponseWriter: ctx.Writer}
		ctx.Writer = blw
		startTime := time.Now()
		ctx.Next()
		duration := time.Since(startTime).String()
		responseHeaders := ctx.Writer.Header()
		contentType := ctx.Writer.Header().Get("Content-Type")
		if strings.HasPrefix(contentType, "application/octet-stream") || strings.HasPrefix(contentType, "multipart/form-data") || contentType == "video/mp4" {
			logger.WithContext(ctx).Info("Response", zap.Any("response_headers", responseHeaders), zap.Any("time", duration))
		} else if strings.HasPrefix(contentType, "application/json") {
			var respObj map[string]any
			_ = json.Unmarshal(blw.body.Bytes(), &respObj)
			logger.WithContext(ctx).Info("Response", zap.Any("response_headers", responseHeaders), zap.Any("response_body", respObj), zap.Any("time", duration))
		} else {
			logger.WithContext(ctx).Info("Response", zap.Any("response_headers", responseHeaders), zap.String("response_body", blw.body.String()), zap.Any("time", duration))
		}
	}
}

type bodyLogWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w bodyLogWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}
