package middleware

import (
	"bytes"
	"github.com/gin-gonic/gin"
	"github.com/liuzl/gocc"
	"io"
	"strings"
	"zodiacus/pkg/log"
)

type responseBodyLangWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w *responseBodyLangWriter) Write(b []byte) (int, error) {
	return w.body.Write(b)
}

func ResponseLanguageConvertMiddleware(logger *log.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		writer := &responseBodyLangWriter{body: bytes.NewBufferString(""), ResponseWriter: c.Writer}
		c.Writer = writer
		c.Next()
		if !strings.Contains(c.Writer.Header().Get("Content-Type"), "application/json") {
			writer.ResponseWriter.Write(writer.body.Bytes())
			return
		}
		lang := c.GetHeader("Accept-Language")
		var converter *gocc.OpenCC
		var err error
		if !strings.Contains(lang, "zh-TW") && !strings.Contains(lang, "zh-HK") {
			writer.ResponseWriter.Write(writer.body.Bytes())
			return
		}
		converter, err = gocc.New("s2t")
		if err != nil {
			writer.ResponseWriter.Write(writer.body.Bytes())
			return
		}
		converted, err := converter.Convert(writer.body.String())
		if err != nil {
			return
		}
		c.Writer.Header().Set("Content-Length", "")
		c.Writer.WriteHeaderNow()
		io.WriteString(c.Writer, converted)
	}
}

//func RequestLanguageConvertMiddleware() gin.HandlerFunc {
//	return func(c *gin.Context) {
//		if !strings.Contains(c.GetHeader("Content-Type"), "application/json") {
//			c.Next()
//			return
//		}
//		lang := c.GetHeader("Accept-Language")
//		if !strings.Contains(lang, "zh-TW") && !strings.Contains(lang, "zh-HK") {
//			c.Next()
//			return
//		}
//		bodyBytes, err := io.ReadAll(c.Request.Body)
//		if err != nil {
//			c.AbortWithStatusJSON(400, gin.H{"error": "bad request"})
//			return
//		}
//		c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
//		converter, err := gocc.New("t2s")
//		if err != nil {
//			c.AbortWithStatusJSON(500, gin.H{"error": "internal server error"})
//			return
//		}
//		convertedStr, err := converter.Convert(string(bodyBytes))
//		if err != nil {
//			c.AbortWithStatusJSON(500, gin.H{"error": "internal server error"})
//			return
//		}
//		c.Request.Body = io.NopCloser(bytes.NewBufferString(convertedStr))
//		c.Next()
//	}
//}
