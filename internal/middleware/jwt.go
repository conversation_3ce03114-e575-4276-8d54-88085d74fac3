package middleware

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"zodiacus/api/v1"
	"zodiacus/pkg/jwthub"
	"zodiacus/pkg/log"
	"zodiacus/third_party/casdoor"
)

// StrictAuth 严格鉴权
func StrictAuth(identity *casdoor.Client, jh *jwthub.Jwthub, logger *log.Logger) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		tokenString := ctx.Request.Header.Get("Authorization")
		if tokenString == "" {
			logger.WithContext(ctx).Warn("No token", zap.Any("data", map[string]any{
				"url":    ctx.Request.URL,
				"params": ctx.Params,
			}))
			v1.HandleError(ctx, v1.ErrUnauthorized)
			ctx.Abort()
			return
		}
		var (
			auth *jwthub.Auth
		)
		cas, err := identity.ParseJwtToken(ctx, tokenString, ctx.Request.RequestURI)
		if err != nil || cas.User.Id == "" {
			// 尝试使用jwthub解析
			claim, err := jh.ParseToken(ctx, tokenString)
			if err != nil {
				logger.WithContext(ctx).Error("claim error", zap.Any("data", map[string]any{
					"url":    ctx.Request.URL,
					"params": ctx.Params,
				}), zap.Error(err))
				v1.HandleError(ctx, v1.ErrUnauthorized)
				ctx.Abort()
				return
			}
			auth = claim.Auth()
			logger.Info("parse jwthub claim", zap.Any("auth", auth))
		} else {
			auth = &jwthub.Auth{
				UserID:   cas.User.Id,
				Username: cas.User.Name,
			}
			logger.Info("parse casdoor token")
		}
		ctx.Set("auth", auth)
		logger.WithValue(ctx, zap.Any("auth", auth))
		ctx.Next()
	}
}

// NoStrictAuth 非严格鉴权
func NoStrictAuth(identity *casdoor.Client, jh *jwthub.Jwthub, logger *log.Logger) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		tokenString := ctx.Request.Header.Get("Authorization")
		if tokenString == "" {
			tokenString, _ = ctx.Cookie("accessToken")
		}
		if tokenString == "" {
			tokenString = ctx.Query("accessToken")
		}
		if tokenString == "" {
			ctx.Next()
			return
		}
		var (
			auth *jwthub.Auth
		)
		cas, err := identity.ParseJwtToken(ctx, tokenString, ctx.Request.RequestURI)
		if err != nil || cas.User.Id == "" {
			// 尝试使用jwthub解析
			claim, err := jh.ParseToken(ctx, tokenString)
			if err != nil {
				ctx.Next()
				return
			}
			auth = claim.Auth()
			logger.Info("parse jwthub claim", zap.Any("auth", auth))
		} else {
			auth = &jwthub.Auth{
				UserID:   cas.User.Id,
				Username: cas.User.Name,
			}
			logger.Info("parse casdoor token")
		}
		ctx.Set("auth", auth)
		logger.WithValue(ctx, zap.Any("auth", auth))
		ctx.Next()
	}
}
