/*
types\app_user.go defines the types used by the user service.
*/

package types

type Tiangan struct {
	Name    string `json:"name" example:"甲"`    // 天干
	Yinyang string `json:"yinyang" example:"阳"` // 阴阳
	Wuxing  string `json:"wuxing" example:"木"`  // 五行
}

type Dizhi struct {
	Name    string `json:"name" example:"子"`    // 地支
	Yinyang string `json:"yinyang" example:"阳"` // 阴阳
	Wuxing  string `json:"wuxing" example:"水"`  // 五行
}

type CailushouxiLocation struct {
	Cai          string   `json:"cai"`          // 财神方位
	Fu           string   `json:"fu"`           // 福神
	Xi           string   `json:"xi"`           // 喜神（桃花）
	Yanggui      string   `json:"yanggui"`      // 阳贵
	Wenchang     string   `json:"wenchang"`     // 文昌
	PianCai      []string `json:"piancai"`      // 偏财位
	PianCaiClock []int    `json:"pianCaiClock"` // 偏财时钟位
}

type WuxingRelationship struct {
	Bijie    string // 比劫：同
	Shishang string // 食伤：所生
	Caicai   string // 财才：所克
	Guansha  string // 官杀：克
	Yinxiao  string // 印枭：生
}

type WuxingEffect struct {
	Wuxing  string
	Fangwei string
	Jijie   string
	Yanse   string
	Shuzi   string
	Juzhudi string
	Yinshi  string
	Yaowu   string
}

type TianyiGuiren struct {
	Tiangan     string   // 日主天干
	GuirenZhi   []string // 天乙贵人对应的地支
	GuirenSheng []string // 天乙贵人对应的生肖
}
