package calendar

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"zodiacus/internal/service"

	v1 "zodiacus/api/v1"
	"zodiacus/internal/handler"
)

type DateHandler struct {
	*handler.BaseHandler
	dateService   service.DateService
	mingliService service.UserMingliService
}

func NewDateHandler(
	baseHandler *handler.BaseHandler,
	dateService service.DateService,
	mingliService service.UserMingliService,
) *DateHandler {
	return &DateHandler{
		BaseHandler:   baseHandler,
		dateService:   dateService,
		mingliService: mingliService,
	}
}

func (h *DateHandler) Handle(required, optional, nameless *handler.AuthRouter) {
	nameless.POST("/calendar/day", h.Day).
		POST("/calendar/month", h.Month).
		POST("/calendar/jiaoyun", h.Jiaoyun)

	required.POST("/calendar/day/vip", h.Day4VIP).
		POST("/calendar/month/vip", h.Month4VIP).
		POST("/calendar/day/dowhat", h.DayDoWhat)
}

// Jiaoyun godoc
// @Summary 交运
// @Schemes
// @Description 交运
// @Tags 日历
// @Accept json
// @Produce json
// @Param request body v1.GetJiaoyunRequest true "params"
// @Success 200 {object} v1.GetJiaoyunResponse
// @Router /calendar/jiaoyun [post]
func (h *DateHandler) Jiaoyun(ctx *gin.Context) {
	var req v1.GetJiaoyunRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		h.Reply(ctx, err)
		return
	}
	res, err := h.dateService.Jiaoyun(ctx, &req)
	if err != nil {
		h.Log(ctx).Error("dateService.Jiaoyun", zap.Error(err))
		h.Reply(ctx, err)
		return
	}
	h.Reply(ctx, res)
}

// Day godoc
// @Summary 获取本日日历
// @Schemes
// @Description 获取本日日历
// @Tags 日历
// @Accept json
// @Produce json
// @Param request body v1.CalendarDayRequest true "params"
// @Success 200 {object} v1.CalendarDayResponse
// @Router /calendar/day [post]
func (h *DateHandler) Day(ctx *gin.Context) {
	var req v1.CalendarDayRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		h.Reply(ctx, err)
		return
	}
	req.AppID = 3 // 万年历
	res, err := h.dateService.Day(ctx, &req)
	if err != nil {
		h.Log(ctx).Error("dateService.Day", zap.Error(err))
		h.Reply(ctx, err)
		return
	}
	h.Reply(ctx, res)
}

// Month godoc
// @Summary 获取本月日历
// @Schemes
// @Description 获取本月日历
// @Tags 日历
// @Accept json
// @Produce json
// @Param request body v1.CalendarMonthRequest true "params"
// @Success 200 {object} v1.CalendarMonthResponse
// @Router /calendar/month [post]
func (h *DateHandler) Month(ctx *gin.Context) {
	var req v1.CalendarMonthRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		h.Reply(ctx, err)
		return
	}
	req.AppID = 3 // 万年历
	res, err := h.dateService.Month(ctx, &req)
	if err != nil {
		h.Log(ctx).Error("dateService.Month", zap.Error(err))
		h.Reply(ctx, err)
		return
	}
	h.Reply(ctx, res)
}

// Day4VIP godoc
// @Summary 获取本日日历
// @Schemes
// @Description 获取本日日历
// @Tags 日历
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.CalendarDayRequest true "params"
// @Success 200 {object} v1.CalendarDayResponse
// @Router /calendar/day/vip [post]
func (h *DateHandler) Day4VIP(ctx *gin.Context) {
	var req v1.CalendarDayRequest
	req.User = h.Auth(ctx)
	if err := ctx.ShouldBindJSON(&req); err != nil {
		h.Reply(ctx, err)
		return
	}
	// TODO：校验用户是否VIP（是否使用中间件实现拦截）
	req.AppID = 3 // 万年历
	res, err := h.dateService.Day4VIP(ctx, &req)
	if err != nil {
		h.Log(ctx).Error("dateService.Day4VIP", zap.Error(err))
		h.Reply(ctx, err)
		return
	}
	h.Reply(ctx, res)
}

// Month4VIP godoc
// @Summary 获取本月日历
// @Schemes
// @Description 获取本月日历
// @Tags 日历
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.CalendarMonthRequest true "params"
// @Success 200 {object} v1.CalendarMonthResponse
// @Router /calendar/month/vip [post]
func (h *DateHandler) Month4VIP(ctx *gin.Context) {
	var req v1.CalendarMonthRequest
	req.User = h.Auth(ctx)
	if err := ctx.ShouldBindJSON(&req); err != nil {
		h.Reply(ctx, err)
		return
	}
	// TODO：校验用户是否VIP（是否使用中间件实现拦截）
	req.AppID = 3 // 万年历
	res, err := h.dateService.Month4VIP(ctx, &req)
	if err != nil {
		h.Log(ctx).Error("dateService.Month4VIP", zap.Error(err))
		h.Reply(ctx, err)
		return
	}
	h.Reply(ctx, res)
}

// DayDoWhat godoc
// @Summary 今天干什么
// @Schemes
// @Description 今天干什么
// @Tags 日历
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.CalendarDayDoWhatRequest true "params"
// @Success 200 {object} v1.CalendarDayDoWhatResponse
// @Router /calendar/day/dowhat [post]
func (h *DateHandler) DayDoWhat(ctx *gin.Context) {
	var req v1.CalendarDayDoWhatRequest
	req.User = h.Auth(ctx)
	if err := ctx.ShouldBindJSON(&req); err != nil {
		h.Reply(ctx, err)
		return
	}
	req.AppID = 3 // 万年历
	// TODO：校验用户是否VIP（是否使用中间件实现拦截）
	res, err := h.dateService.DayDoWhat(ctx, &req)
	if err != nil {
		h.Log(ctx).Error("dateService.DayDoWhat", zap.Error(err))
		h.Reply(ctx, err)
		return
	}
	h.Reply(ctx, res)
}
