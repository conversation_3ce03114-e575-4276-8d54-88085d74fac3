package calendar

import (
	"github.com/gin-gonic/gin"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"
)

type EnumsHandler struct {
	*handler.BaseHandler
	enumsService service.EnumsService
}

func NewEnumsHandler(
	baseHandler *handler.BaseHandler,
	enumsService service.EnumsService,
) *EnumsHandler {
	return &EnumsHandler{
		BaseHandler:  baseHandler,
		enumsService: enumsService,
	}
}

func (slf *EnumsHandler) Handle(required, optional, nameless *handler.AuthRouter) {
	nameless.POST("/enums/jieqi", slf.Jieqi).
		POST("/enums/time", slf.Time).
		POST("/enums/yiji", slf.Yiji).
		POST("/enums/lunar", slf.Lunar).
		POST("/enums/wuxing", slf.Wuxing).
		POST("/enums/zhishen", slf.Zhishen).
		POST("/enums/shierji<PERSON><PERSON>", slf.<PERSON>).
		POST("/enums/nayin", slf.Nayin).
		POST("/enums/jixiong", slf.JiXiong).
		POST("/enums/pengzubaiji", slf.Pengzubaiji).
		POST("/enums/location", slf.Location)
}

// JiXiong godoc
// @Summary 获取吉/凶神列表
// @Schemes
// @Description 获取吉/凶神列表
// @Tags 枚举
// @Accept json
// @Produce json
// @Param request body v1.EnumsJiXiongRequest true "params"
// @Success 200 {object} v1.EnumsJiXiongResponse
// @Router /enums/jixiong [post]
func (slf *EnumsHandler) JiXiong(ctx *gin.Context) {
	res, err := slf.enumsService.JiXiong(ctx)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// Pengzubaiji godoc
// @Summary 获取彭祖百忌列表
// @Schemes
// @Description 获取彭祖百忌列表
// @Tags 枚举
// @Accept json
// @Produce json
// @Param request body v1.EnumsPengzubaijiRequest true "params"
// @Success 200 {object} v1.EnumsPengzubaijiResponse
// @Router /enums/pengzubaiji [post]
func (slf *EnumsHandler) Pengzubaiji(ctx *gin.Context) {
	res, err := slf.enumsService.Pengzubaiji(ctx)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// Nayin godoc
// @Summary 获取纳音列表
// @Schemes
// @Description 获取纳音列表
// @Tags 枚举
// @Accept json
// @Produce json
// @Param request body v1.EnumsNayinRequest true "params"
// @Success 200 {object} v1.EnumsNayinResponse
// @Router /enums/nayin [post]
func (slf *EnumsHandler) Nayin(ctx *gin.Context) {
	res, err := slf.enumsService.Nayin(ctx)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// Shierjianri godoc
// @Summary 获取十二建日列表
// @Schemes
// @Description 获取十二建日列表
// @Tags 枚举
// @Accept json
// @Produce json
// @Param request body v1.EnumsShierjianriRequest true "params"
// @Success 200 {object} v1.EnumsShierjianriResponse
// @Router /enums/shierjianri [post]
func (slf *EnumsHandler) Shierjianri(ctx *gin.Context) {
	res, err := slf.enumsService.Shierjianri(ctx)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// Zhishen godoc
// @Summary 获取值神列表
// @Schemes
// @Description 获取值神列表
// @Tags 枚举
// @Accept json
// @Produce json
// @Param request body v1.EnumsZhishenRequest true "params"
// @Success 200 {object} v1.EnumsZhishenResponse
// @Router /enums/zhishen [post]
func (slf *EnumsHandler) Zhishen(ctx *gin.Context) {
	res, err := slf.enumsService.Zhishen(ctx)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// Wuxing godoc
// @Summary 获取五行列表
// @Schemes
// @Description 获取五行列表
// @Tags 枚举
// @Accept json
// @Produce json
// @Param request body v1.EnumsWuxingRequest true "params"
// @Success 200 {object} v1.EnumsWuxingResponse
// @Router /enums/wuxing [post]
func (slf *EnumsHandler) Wuxing(ctx *gin.Context) {
	res, err := slf.enumsService.Wuxing(ctx)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// Jieqi godoc
// @Summary 获取节气列表
// @Schemes
// @Description 获取节气列表
// @Tags 枚举
// @Accept json
// @Produce json
// @Param request body v1.EnumsJieqiRequest true "params"
// @Success 200 {object} v1.EnumsJieqiResponse
// @Router /enums/jieqi [post]
func (slf *EnumsHandler) Jieqi(ctx *gin.Context) {
	res, err := slf.enumsService.Jieqi(ctx)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// Time godoc
// @Summary 获取时辰列表
// @Schemes
// @Description 获取时辰列表
// @Tags 枚举
// @Accept json
// @Produce json
// @Param request body v1.EnumsTimeRequest true "params"
// @Success 200 {object} v1.EnumsTimeResponse
// @Router /enums/time [post]
func (slf *EnumsHandler) Time(ctx *gin.Context) {
	res, err := slf.enumsService.Time(ctx)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// Yiji godoc
// @Summary 获取宜忌列表
// @Schemes
// @Description 获取宜忌列表
// @Tags 枚举
// @Accept json
// @Produce json
// @Param request body v1.EnumsYijiRequest true "params"
// @Success 200 {object} v1.EnumsYijiResponse
// @Router /enums/yiji [post]
func (slf *EnumsHandler) Yiji(ctx *gin.Context) {
	res, err := slf.enumsService.Yiji(ctx)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// Lunar godoc
// @Summary 获取农历列表
// @Schemes
// @Description 获取农历列表
// @Tags 枚举
// @Accept json
// @Produce json
// @Param request body v1.EnumsLunarRequest true "params"
// @Success 200 {object} v1.EnumsLunarResponse
// @Router /enums/lunar [post]
func (slf *EnumsHandler) Lunar(ctx *gin.Context) {
	var req v1.EnumsLunarRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	res, err := slf.enumsService.Lunar(ctx, req.Year)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// Location godoc
// @Summary 获取地区列表
// @Schemes
// @Description 获取地区列表
// @Tags 枚举
// @Accept json
// @Produce json
// @Param request body v1.EnumsLocationRequest true "params"
// @Success 200 {object} v1.EnumsLocationResponse
// @Router /enums/location [post]
func (slf *EnumsHandler) Location(ctx *gin.Context) {
	var req v1.EnumsLocationRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	res, err := slf.enumsService.Location(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}
