package calendar

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"
)

type HistoryHandler struct {
	*handler.BaseHandler
	historyService service.HistoryTodayService
}

func NewHistoryHandler(
	baseHandler *handler.BaseHandler,
	historyService service.HistoryTodayService,
) *HistoryHandler {
	return &HistoryHandler{
		BaseHandler:    baseHandler,
		historyService: historyService,
	}
}

func (h *HistoryHandler) Handle(required, optional, nameless *handler.AuthRouter) {
	nameless.POST("/historyDayEvents", h.HistoryDayEvent)
}

// HistoryDayEvent godoc
// @Summary 获取历史上的今天
// @Schemes
// @Description 获取历史上的今天
// @Tags 历史上的今天
// @Accept json
// @Produce json
// @Param request body v1.GetHistoryDayEventsRequest true "params"
// @Success 200 {object} v1.GetHistoryDayEventsResponse
// @Router /historyDayEvents [post]
func (h *HistoryHandler) HistoryDayEvent(ctx *gin.Context) {
	var req v1.GetHistoryDayEventsRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		h.Reply(ctx, err)
		return
	}
	res, err := h.historyService.GetHistoryDayEvents(ctx, &req)
	if err != nil {
		h.Log(ctx).Error("historyDayEventService.GetDayActions", zap.Error(err))
		h.Reply(ctx, err)
		return
	}
	h.Reply(ctx, res)
}
