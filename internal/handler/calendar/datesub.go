package calendar

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"
)

type DateSubHandler struct {
	*handler.BaseHandler
	datesubService service.DateSubService
}

func NewDateSubHandler(
	baseHandler *handler.BaseHandler,
	datesubService service.DateSubService,
) *DateSubHandler {
	return &DateSubHandler{
		BaseHandler:    baseHandler,
		datesubService: datesubService,
	}
}

func (h *DateSubHandler) Handle(required, optional, nameless *handler.AuthRouter) {
	nameless.POST("/datesub", h.DateSub).
		POST("/datesub/past", h.DateSubPast).
		POST("/datesub/future", h.DateSubFuture)
}

// DateSub godoc
// @Summary 日期换算
// @Schemes
// @Description 日期换算
// @Tags 日期换算
// @Accept json
// @Produce json
// @Param request body v1.DateSubRequest true "params"
// @Success 200 {object} v1.DateSubResponse
// @Router /datesub [post]
func (h *DateSubHandler) DateSub(ctx *gin.Context) {
	var req v1.DateSubRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		h.Reply(ctx, err)
		return
	}
	res, err := h.datesubService.DateSub(ctx, &req)
	if err != nil {
		h.Log(ctx).Error("historyService.DateSub", zap.Error(err))
		h.Reply(ctx, err)
		return
	}
	h.Reply(ctx, res)
}

// DateSubPast godoc
// @Summary 过去日期
// @Schemes
// @Description 过去日期
// @Tags 日期换算
// @Accept json
// @Produce json
// @Param request body v1.DateSubPastRequest true "params"
// @Success 200 {object} v1.DateSubPastResponse
// @Router /datesub/past [post]
func (h *DateSubHandler) DateSubPast(ctx *gin.Context) {
	var req v1.DateSubPastRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		h.Reply(ctx, err)
		return
	}
	res, err := h.datesubService.DateSubPast(ctx, &req)
	if err != nil {
		h.Log(ctx).Error("historyService.DateSubPast", zap.Error(err))
		h.Reply(ctx, err)
		return
	}
	h.Reply(ctx, res)
}

// DateSubFuture godoc
// @Summary 未来日期
// @Schemes
// @Description 未来日期
// @Tags 日期换算
// @Accept json
// @Produce json
// @Param request body v1.DateSubFutureRequest true "params"
// @Success 200 {object} v1.DateSubFutureResponse
// @Router /datesub/future [post]
func (h *DateSubHandler) DateSubFuture(ctx *gin.Context) {
	var req v1.DateSubFutureRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		h.Reply(ctx, err)
		return
	}
	res, err := h.datesubService.DateSubFuture(ctx, &req)
	if err != nil {
		h.Log(ctx).Error("historyService.DateSubFuture", zap.Error(err))
		h.Reply(ctx, err)
		return
	}
	h.Reply(ctx, res)
}
