package wealth

import (
	"github.com/gin-gonic/gin"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"
)

type EnumsHandler struct {
	*handler.BaseHandler
	enumsService service.EnumsService
}

func NewEnumsHandler(
	baseHandler *handler.BaseHandler,
	enumsService service.EnumsService,
) *EnumsHandler {
	return &EnumsHandler{
		BaseHandler:  baseHandler,
		enumsService: enumsService,
	}
}

func (slf *EnumsHandler) Handle(required, optional, nameless *handler.AuthRouter) {
	nameless.POST("/enums/shishen/property", slf.GetShishenProperty).
		POST("/enums/wuxing", slf.GetWuxing).
		POST("/enums/lunar", slf.Lunar).
		POST("/enums/location", slf.Location).
		POST("/enums/tiangan", slf.GetTiangan).
		POST("/enums/dizhi", slf.GetDizhi)
}

// GetTiangan godoc
// @Summary 获取天干
// @Schemes
// @Description 获取天干
// @Tags 枚举
// @Accept json
// @Produce json
// @Success 200 {object} v1.EnumsTianganResponse
// @Router /enums/tiangan [post]
func (slf *EnumsHandler) GetTiangan(ctx *gin.Context) {
	slf.Reply(ctx, v1.EnumsTianganResponseData{
		{Name: "甲", Tiangan: "甲", Yinyang: "阳", Wuxing: "木"},
		{Name: "乙", Tiangan: "乙", Yinyang: "阴", Wuxing: "木"},
		{Name: "丙", Tiangan: "丙", Yinyang: "阳", Wuxing: "火"},
		{Name: "丁", Tiangan: "丁", Yinyang: "阴", Wuxing: "火"},
		{Name: "戊", Tiangan: "戊", Yinyang: "阳", Wuxing: "土"},
		{Name: "己", Tiangan: "己", Yinyang: "阴", Wuxing: "土"},
		{Name: "庚", Tiangan: "庚", Yinyang: "阳", Wuxing: "金"},
		{Name: "辛", Tiangan: "辛", Yinyang: "阴", Wuxing: "金"},
		{Name: "壬", Tiangan: "壬", Yinyang: "阳", Wuxing: "水"},
		{Name: "癸", Tiangan: "癸", Yinyang: "阴", Wuxing: "水"},
	})
}

// GetDizhi godoc
// @Summary 获取地支
// @Schemes
// @Description 获取地支
// @Tags 枚举
// @Accept json
// @Produce json
// @Success 200 {object} v1.EnumsDizhiResponse
// @Router /enums/dizhi [post]
func (slf *EnumsHandler) GetDizhi(ctx *gin.Context) {
	slf.Reply(ctx, v1.EnumsDizhiResponseData{
		{Name: "子", Dizhi: "子", Yinyang: "阳", Wuxing: "水", Shuxiang: "鼠", Yuefen: "十一月", Jieqi: "大雪-小寒", Zhongqi: "冬至", Shichen: "23-1"},
		{Name: "丑", Dizhi: "丑", Yinyang: "阴", Wuxing: "土", Shuxiang: "牛", Yuefen: "十二月", Jieqi: "小寒-立春", Zhongqi: "大寒", Shichen: "1-3"},
		{Name: "寅", Dizhi: "寅", Yinyang: "阳", Wuxing: "木", Shuxiang: "虎", Yuefen: "正月", Jieqi: "立春-惊蛰", Zhongqi: "雨水", Shichen: "3-5"},
		{Name: "卯", Dizhi: "卯", Yinyang: "阴", Wuxing: "木", Shuxiang: "兔", Yuefen: "二月", Jieqi: "惊蛰-清明", Zhongqi: "春分", Shichen: "5-7"},
		{Name: "辰", Dizhi: "辰", Yinyang: "阳", Wuxing: "土", Shuxiang: "龙", Yuefen: "三月", Jieqi: "清明-立夏", Zhongqi: "谷雨", Shichen: "7-9"},
		{Name: "巳", Dizhi: "巳", Yinyang: "阴", Wuxing: "火", Shuxiang: "蛇", Yuefen: "四月", Jieqi: "立夏-芒种", Zhongqi: "小满", Shichen: "9-11"},
		{Name: "午", Dizhi: "午", Yinyang: "阳", Wuxing: "火", Shuxiang: "马", Yuefen: "五月", Jieqi: "芒种-小暑", Zhongqi: "夏至", Shichen: "11-13"},
		{Name: "未", Dizhi: "未", Yinyang: "阴", Wuxing: "土", Shuxiang: "羊", Yuefen: "六月", Jieqi: "小暑-立秋", Zhongqi: "大暑", Shichen: "13-15"},
		{Name: "申", Dizhi: "申", Yinyang: "阳", Wuxing: "金", Shuxiang: "猴", Yuefen: "七月", Jieqi: "立秋-白露", Zhongqi: "处暑", Shichen: "15-17"},
		{Name: "酉", Dizhi: "酉", Yinyang: "阴", Wuxing: "金", Shuxiang: "鸡", Yuefen: "八月", Jieqi: "白露-寒露", Zhongqi: "秋分", Shichen: "17-19"},
		{Name: "戌", Dizhi: "戌", Yinyang: "阳", Wuxing: "土", Shuxiang: "狗", Yuefen: "九月", Jieqi: "寒露-立冬", Zhongqi: "霜降", Shichen: "19-21"},
		{Name: "亥", Dizhi: "亥", Yinyang: "阴", Wuxing: "水", Shuxiang: "猪", Yuefen: "十月", Jieqi: "立冬-大雪", Zhongqi: "小雪", Shichen: "21-23"},
	})
}

// GetShishenProperty godoc
// @Summary 获取十神特征
// @Schemes
// @Description 获取十神特征
// @Tags 枚举
// @Accept json
// @Produce json
// @Success 200 {object} v1.EnumsShishenPropertyResponse
// @Router /enums/shishen/property [post]
func (slf *EnumsHandler) GetShishenProperty(ctx *gin.Context) {
	slf.Reply(ctx, v1.EnumsShishenPropertyResponseData{
		{
			Category:        "比劫",
			Type:            "竞争型",
			Overview:        "需要强执行力和开拓力的岗位职业",
			Ability:         "进取心较强，有强大的意志力和行动力，能抗下艰巨的任务，并以誓不罢休的精神达成目标，并在此过程中享受竞争带来的动力",
			Superiority:     "能在团队中迅速凝结人气，被团队成员所拥护",
			Profession:      "项目经理，营销管理，创业者、运动员等",
			WealthBy:        "通过体能、劳动、风险投资、创业或合作经营等方式来获取财富，",
			Bias:            "方式较为激进、大胆。",
			Insufficient:    "有时在自信与进取的道路上，您可能会稍显犹豫，面对机遇与挑战时倾向于采取更为观望的态度。这可能导致在推动项目进程中，一些本可把握的机会悄悄溜走，或是错失了事业发展的黄金时期。",
			PreferredRegion: "选择那些经济发展迅速，商业气息浓厚，民间资本蓬勃发展的城市，竞争激烈的城市，如超一线城市、一线城市、新兴经济区、长江中下游、珠三角、东南沿海地区的城市。",
		},
		{
			Category:        "食伤",
			Type:            "技术型",
			Overview:        "需要创新创意与敏捷思维的岗位职业",
			Ability:         "有着强烈的好奇心与求知欲，思维敏捷，口才好。对于爱好有钻研精神，能够迅速切入新领域，并产出具有创意性的成果。同时具备很强的艺术天分。",
			Superiority:     "喜欢新奇和变化的事物，对固有的规章制度和文化较为抗拒，重视自我才华的自由舒展",
			Profession:      "设计师，程序员，摄影师，建筑师，机械工程师，导游，演员，网红，写手等",
			WealthBy:        "兴趣爱好与技术驱动的形式来获取财富，",
			Bias:            "一定程度上受兴趣驱动，强调自我意志的主张。",
			Insufficient:    "在创意探索与个性展现的舞台上，您或许略显内敛，对新鲜趋势的敏感度及主动参与的热情有待提升。执行任务时，您更倾向于遵循既定路线，对于突破常规、勇于创新方面可能稍显保守。",
			PreferredRegion: "选择文化氛围浓厚，创意产业发达、经济活跃、技术与市场需求匹配的城市，如长长江中下游、珠三角、东南沿海地区、北京、长沙等城市。",
		},
		{
			Category:        "财才",
			Type:            "社交型",
			Overview:        "需要社交能力与情绪价值的岗位职业",
			Ability:         "擅长沟通，理解别人的想法，交际广泛，朋友众多能维护复杂的人际关系，非常细心，记忆力和观察力都很强，对信息敏感，并善于整理信息，与他人交换资源、建立合作、互通有无。",
			Superiority:     "善于体察他人需求，对数据、咨询、金钱财务具有相当高的敏锐度，并能快速捕捉市场的动向，在时机把握上有较好的见解。",
			Profession:      "公关，销售，财务，交易，物流，金融，各种咨询师等",
			WealthBy:        "通过对各类资源的运作的形式获取财富，",
			Bias:            "对金钱、数字比较敏感，擅长资源运作、精细耕耘。",
			Insufficient:    "在人际交往与情感理解的领域，您可能偶尔显得不够细腻，对周围人的情绪变化不够敏锐，给人以“共情不足”的印象。同时，在财务管理方面，您更倾向于随性而为，对于投资理财的精细规划及高效运作尚需加强，以期让资金发挥更大价值。",
			PreferredRegion: "选择那些商业氛围浓厚、经济发展活跃的、民营企业蓬勃发展的地区城市，如超一线城市、一线城市、长江中下游、珠三角、东南沿海地区等金融中心或商业重镇；或者本人拥有较好资源的地区。",
		},
		{
			Category:        "官杀",
			Type:            "管理型",
			Overview:        "需要缜密逻辑与分析管理的岗位职业",
			Ability:         "抽象思维能力强，善思考学习，遵守规则，尊重权威，对自我发展有着较高的要求，能不断精进自己，设立长远目标并以结果导向，对产出负责。",
			Superiority:     "善于分析问题，拆解思路，并为事情发展设立中长期发展战略，并在强大理性驱动下，整合组织与团队力量，作出亮眼的成绩。",
			Profession:      "公务员，大型国企，大公司的管理人员，研究员，分析师等",
			WealthBy:        "依靠事业、地位、势力或权力获取。",
			Bias:            "重视权势名望甚于实际利益。",
			Insufficient:    "对于理性规划与目标的把握，您有时会更多地依赖直觉，而缺少对现实状况的全面而冷静的分析。这可能导致在执行过程中，目标设定易于下调，整体策略显得较为随性。",
			PreferredRegion: "选择那些注重规则、秩序，经济发展较为成熟，资源配套比较齐全的中心城市，提供较多职业发展机会的城市，如首都、直辖市、省会、超一线、一线等城市发展。",
		},
		{
			Category:        "印枭",
			Type:            "事物型",
			Overview:        "需要较好毅力、耐心或积累沉淀的岗位职业",
			Ability:         "处事沉稳目标感强，并善于坚持。热爱文化，有耐心，稳重踏实，能坚持长期目标。待人随和，人缘普遍好。具有强大的耐心与韧性，可参与和经营长周期的项目与事业，等待收获的来临",
			Superiority:     "注重团队和谐的氛围，能给团队安心与信任，成为团队中受人信赖的成员和后盾支持。",
			Profession:      "行政，人事，医生，老师，心理咨询师，宗教学者等",
			WealthBy:        "通过自身的信誉、学识或稳定性职业来获取财富。",
			Bias:            "重视名望甚于金钱财富。",
			Insufficient:    "在面对任务时，您可能偶尔会忽略耐心与坚韧的重要性，表现出一定的急躁情绪，期望快速见到成果。在长远规划与持之以恒方面，您或许需要更多地培养自己放眼未来、静待花开的心态。",
			PreferredRegion: "选择那些文化底蕴深厚，教育资源丰富的城市，如:北京、西安、南京、武汉等城市；或者环境较为稳定，工作节奏适中，具有熟悉人脉关系的城市发展。",
		},
	})
}

// GetWuxing godoc
// @Summary 获取五行
// @Schemes
// @Description 获取五行
// @Tags 枚举
// @Accept json
// @Produce json
// @Success 200 {object} v1.EnumsWuxingResponse
// @Router /enums/wuxing [post]
func (slf *EnumsHandler) GetWuxing(ctx *gin.Context) {
	slf.Reply(ctx, v1.EnumsWuxingResponseData{
		{
			Wuxing:     "木",
			Profession: "木曰曲直，核心特质为生长传承、仁慈耐心、教育创新。木属性象征生命成长与文化传承，需温和沟通、创造力及引导力。\n所以适合从事教育文化、农林牧渔、宗教界、红十字会、医疗健康、慈善业、生物技术、木材家具、布匹服装、创意设计、造纸、水果中草药、等。都为木五行所属职业。文字、文学、文艺、教育、作家、写作、学生用品业、学校、书店、出版社。\n木有生命、向上之意，包括数字经济与AI融合赛道、新能源交通、低空经济、智能制造、政策与市场双驱动赛道等朝阳产业也为木得五行所属职业。",
			Position:   "东方",
			Season:     "春季",
		},
		{
			Wuxing:     "火",
			Profession: "火曰炎上，核心特质为为热情传播、光明能量、竞争表现。火属性象征能量与创造力，需激情、表现力及冒险精神。\n故适合从事燃料、石油、天然气、煤炭、电力电网等能源科技、热加工（电、气焊，食品行业如烧烤、烘烤面点）、高新科技、IT互联网、直播、应急急等五行为火的行业职业。火属性与情感、外在形象相关，适合心理学、灯光设计、摄影、衣帽行、服装店、理发美容等职业。火象征热情与表现力，匹配创意与传播需求，如传媒娱乐、影视直播、广告公关。",
			Position:   "南方",
			Season:     "夏季",
		},
		{
			Wuxing:     "土",
			Profession: "土爰稼穑，核心特质为稳定务实、细致耐心、管理执行。土属性象征稳固与承载，需组织能力、长期规划及务实精神。\n故适合从事矿产资源、地质勘探、资源开发、基建规划、建筑房产、水泥砖石、玉石陶瓷、农业畜牧（土的资源与承载特性）、防水筑堤、雨具相关等五行为土的行业职业。\n土有包容承载之性故土又为高级领导干部、行政管理。土为杂气，主零碎的工作、干零活。",
			Position:   "中央",
			Season:     "四季",
		},
		{
			Wuxing:     "金",
			Profession: "金曰从格，核心特质为聚集革新、坚硬锐利、决断权威、操控性强。金属性象征权威与规则，需逻辑严密、善分析，且具决断力与权威性。\n故适合从事军、警界、公检法、屠宰业、机械制造、法律相关、金融相关、珠宝鉴定、汽车工业、外科医疗等五行为金的行业职业。金主寒冷，也适合冷库工作、冷饮厂、卖冷饮、空调、冰箱、制冷设备生产经营维修。",
			Position:   "西方",
			Season:     "秋季",
		},
		{
			Wuxing:     "水",
			Profession: "水曰润下，核心特质为漂流奔波、流动多变不定、智慧应变、沟通创意。水属性象征变化与适应性，需高应变能力、创新思维及人际协调力。\n故适合从事如渔业、物流贸易（航运、进出口、电子商务）、旅游服务（导游、酒店管理）、艺术设计（舞蹈、摄影）、销售及商业与心理咨询（需要沟通，应变不确定的事宜）、魔术马戏、清洁性质行业（如环卫工、洗浴业、洗衣行）等五行为水的职业。\n水可灭火，故消防队、生产经营维修消防器材、卖凉茶（去火气)等均为水五行所属职业。",
			Position:   "北方",
			Season:     "冬季",
		},
	})
}

// Lunar godoc
// @Summary 获取农历列表
// @Schemes
// @Description 获取农历列表
// @Tags 枚举
// @Accept json
// @Produce json
// @Param request body v1.EnumsLunarRequest true "params"
// @Success 200 {object} v1.EnumsLunarResponse
// @Router /enums/lunar [post]
func (slf *EnumsHandler) Lunar(ctx *gin.Context) {
	var req v1.EnumsLunarRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	res, err := slf.enumsService.Lunar(ctx, req.Year)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// Location godoc
// @Summary 获取地区列表
// @Schemes
// @Description 获取地区列表
// @Tags 枚举
// @Accept json
// @Produce json
// @Param request body v1.EnumsLocationRequest true "params"
// @Success 200 {object} v1.EnumsLocationResponse
// @Router /enums/location [post]
func (slf *EnumsHandler) Location(ctx *gin.Context) {
	var req v1.EnumsLocationRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	res, err := slf.enumsService.Location(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}
