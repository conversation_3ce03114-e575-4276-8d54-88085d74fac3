package wealth

import (
	"fmt"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"

	"github.com/gin-gonic/gin"
)

type PaipanRecordHandler struct {
	*handler.BaseHandler
	paipanRecordService service.PaipanRecordService
}

func NewPaipanRecordHandler(
	handler *handler.BaseHandler,
	paipanRecordService service.PaipanRecordService,
) *PaipanRecordHandler {
	return &PaipanRecordHandler{
		BaseHandler:         handler,
		paipanRecordService: paipanRecordService,
	}
}

func (slf *PaipanRecordHandler) Handle(required, optional, nameless *handler.AuthRouter) {
	required.
		POST("/paipanRecord/pageList", slf.PageListPaipanRecord).
		POST("/paipanRecord/delete", slf.DeletePaipanRecord).
		POST("/paipanRecord/own", slf.OwnPaipanRecord)
}

// OwnPaipanRecord godoc
// @Summary 占有排盘记录
// @Schemes
// @Description 占有排盘记录
// @Tags 排盘
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.PaipanRecordOwnRequest true "params"
// @Success 200 {object} v1.PaipanRecordOwnResponse
// @Router /paipanRecord/own [post]
func (slf *PaipanRecordHandler) OwnPaipanRecord(ctx *gin.Context) {
	var req v1.PaipanRecordOwnRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	req.User = slf.Auth(ctx)
	err := slf.paipanRecordService.OwnPaipanRecord(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, nil)
}

// DeletePaipanRecord godoc
// @Summary 删除用户排盘记录
// @Schemes
// @Description 删除用户排盘记录
// @Tags 用户排盘记录
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.DeletePaipanRecordRequest true "params"
// @Success 200 {object} v1.DeletePaipanRecordResponse
// @Router /paipanRecord/delete [post]
func (slf *PaipanRecordHandler) DeletePaipanRecord(ctx *gin.Context) {
	var req v1.DeletePaipanRecordRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	req.User = slf.Auth(ctx)
	err := slf.paipanRecordService.DeletePaipanRecord(ctx, &req)
	if err != nil {
		err = fmt.Errorf("paipanRecordService.DeletePaipanRecord: %w", err)
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, nil)
}

// PageListPaipanRecord godoc
// @Summary 分页查询用户排盘记录
// @Schemes
// @Description 分页查询用户排盘记录
// @Tags 用户排盘记录
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.PageListPaipanRecordRequest true "params"
// @Success 200 {object} v1.PageListPaipanRecordResponse
// @Router /paipanRecord/pageList [post]
func (slf *PaipanRecordHandler) PageListPaipanRecord(ctx *gin.Context) {
	var req v1.PageListPaipanRecordRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	req.Param.User = slf.Auth(ctx)
	//if req.Param.Application == "" {
	//	req.Param.Application = slf.Application(ctx)
	//}

	data, err := slf.paipanRecordService.PageListPaipanRecord(ctx, &req)
	if err != nil {
		err = fmt.Errorf("paipanRecordService.PageListPaipanRecord: %w", err)
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, data)
}
