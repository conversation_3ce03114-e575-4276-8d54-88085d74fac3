package wealth

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"zodiacus/internal/service"

	v1 "zodiacus/api/v1"
	"zodiacus/internal/handler"
)

type DateHandler struct {
	*handler.BaseHandler
	dateService service.DateService
}

func NewDateHandler(
	baseHandler *handler.BaseHandler,
	dateService service.DateService,
) *DateHandler {
	return &DateHandler{
		BaseHandler: baseHandler,
		dateService: dateService,
	}
}

func (h *DateHandler) Handle(required, optional, nameless *handler.AuthRouter) {
	nameless.POST("/date/day", h.Day).
		POST("/date/month", h.Month)
}

// Day godoc
// @Summary 获取本日日历
// @Schemes
// @Description 获取本日日历
// @Tags 日历
// @Accept json
// @Produce json
// @Param request body v1.CalendarDayRequest true "params"
// @Success 200 {object} v1.CalendarDayResponse
// @Router /date/day [post]
func (h *DateHandler) Day(ctx *gin.Context) {
	var req v1.CalendarDayRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		h.Reply(ctx, err)
		return
	}
	req.AppID = 3 // 万年历
	res, err := h.dateService.Day(ctx, &req)
	if err != nil {
		h.Log(ctx).Error("dateService.Day", zap.Error(err))
		h.Reply(ctx, err)
		return
	}
	h.Reply(ctx, res)
}

// Month godoc
// @Summary 获取本月日历
// @Schemes
// @Description 获取本月日历
// @Tags 日历
// @Accept json
// @Produce json
// @Param request body v1.CalendarMonthRequest true "params"
// @Success 200 {object} v1.CalendarMonthResponse
// @Router /date/month [post]
func (h *DateHandler) Month(ctx *gin.Context) {
	var req v1.CalendarMonthRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		h.Reply(ctx, err)
		return
	}
	req.AppID = 3 // 万年历
	res, err := h.dateService.Month(ctx, &req)
	if err != nil {
		h.Log(ctx).Error("dateService.Month", zap.Error(err))
		h.Reply(ctx, err)
		return
	}
	h.Reply(ctx, res)
}
