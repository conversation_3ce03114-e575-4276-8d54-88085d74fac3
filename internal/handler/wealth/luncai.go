package wealth

import (
	"github.com/gin-gonic/gin"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"
)

type LuncaiHandler struct {
	*handler.BaseHandler
	luncaiService service.LuncaiService
}

func NewLuncaiHandler(
	baseHandler *handler.BaseHandler,
	luncaiService service.LuncaiService,
) *LuncaiHandler {
	return &LuncaiHandler{
		BaseHandler:   baseHandler,
		luncaiService: luncaiService,
	}
}

func (slf *LuncaiHandler) Handle(required, optional, nameless *handler.AuthRouter) {
	optional.POST("/luncai/", slf.Luncai).
		POST("/luncai/replay", slf.LuncaiReplay)

	nameless.POST("/luncai/example", slf.LuncaiExample)
}

// LuncaiExample godoc
// @Summary 论财示例
// @Schemes
// @Description 论财示例
// @Tags 论财
// @Accept json
// @Produce json
// @Success 200 {object} v1.LuncaiResponse
// @Router /luncai/example [post]
func (slf *LuncaiHandler) LuncaiExample(ctx *gin.Context) {
	req := &v1.LuncaiRequest{
		Birthtime: "2001-01-01 00:00:00",
		Gender:    "男",
		Name:      "示例",
		IsExample: true,
	}
	data, err := slf.luncaiService.Luncai(ctx, req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, data)
}

// Luncai godoc
// @Summary 论财
// @Schemes
// @Description 论财
// @Tags 论财
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.LuncaiRequest true "params"
// @Success 200 {object} v1.LuncaiResponse
// @Router /luncai/ [post]
func (slf *LuncaiHandler) Luncai(ctx *gin.Context) {
	var req v1.LuncaiRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	req.User = slf.Auth(ctx)
	//req.VIP = slf.VIP(ctx)
	req.Application = slf.Application(ctx)
	req.DeviceID = ctx.GetHeader("Device-ID")
	req.UserAgent = ctx.GetHeader("Auth-Agent")
	req.IP = ctx.ClientIP()
	res, err := slf.luncaiService.Luncai(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// LuncaiReplay godoc
// @Summary 论财结果重放
// @Schemes
// @Description 论财结果重放
// @Tags 论财
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.LuncaiReplayRequest true "params"
// @Success 200 {object} v1.LuncaiReplayResponse
// @Router /luncai/replay [post]
func (slf *LuncaiHandler) LuncaiReplay(ctx *gin.Context) {
	var req v1.LuncaiReplayRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	req.UserID = slf.Auth(ctx)
	res, err := slf.luncaiService.LuncaiReplay(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}
