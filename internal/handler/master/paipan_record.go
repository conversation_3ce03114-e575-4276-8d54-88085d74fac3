package master

import (
	v1 "zodiacus/api/v1"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"

	"github.com/gin-gonic/gin"
)

type PaipanRecordHandler struct {
	*handler.BaseHandler
	paipanRecordService service.PaipanRecordService
}

func NewPaipanRecordHandler(
	handler *handler.BaseHandler,
	paipanRecordService service.PaipanRecordService,
) *PaipanRecordHandler {
	return &PaipanRecordHandler{
		BaseHandler:         handler,
		paipanRecordService: paipanRecordService,
	}
}

func (slf *PaipanRecordHandler) Handle(required, optional, nameless *handler.AuthRouter) {
	required.
		POST("/paipanRecord/own", slf.OwnPaipanRecord)
}

// OwnPaipanRecord godoc
// @Summary 占有排盘记录
// @Schemes
// @Description 占有排盘记录
// @Tags 排盘
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.PaipanRecordOwnRequest true "params"
// @Success 200 {object} v1.PaipanRecordOwnResponse
// @Router /paipanRecord/own [post]
func (slf *PaipanRecordHandler) OwnPaipanRecord(ctx *gin.Context) {
	var req v1.PaipanRecordOwnRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	req.User = slf.Auth(ctx)
	err := slf.paipanRecordService.OwnPaipanRecord(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, nil)
}
