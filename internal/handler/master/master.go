package master

import (
	"github.com/gin-gonic/gin"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"
)

type MasterHandler struct {
	*handler.BaseHandler
	masterService service.MasterService
}

func NewMasterHandler(
	baseHandler *handler.BaseHandler,
	masterService service.MasterService,
) *MasterHandler {
	return &MasterHandler{
		BaseHandler:   baseHandler,
		masterService: masterService,
	}
}

func (slf *MasterHandler) Handle(required, optional, nameless *handler.AuthRouter) {
	optional.POST("/master/", slf.Master)
	optional.POST("/geju/biange", slf.GeJuBianGe)
	optional.POST("/xiyongwangshaui", slf.XiyongWangshuai)
	optional.POST("/dayunliunian", slf.DayunLiunian)
	optional.POST("/dayun", slf.DayunAnalysis)
	optional.POST("/liunian", slf.LiunianAnalysis)
	optional.POST("/gaokao", slf.<PERSON>)
	optional.POST("/gaokao/preference", slf.GaoKaoPreference)
	nameless.GET("/ip", slf.GetIP)

	required.POST("/hepan", slf.Hepan)
	required.POST("/hepan/pageList", slf.PageListHepan)
	required.POST("/hepan/delete", slf.DeleteHepan)
	required.POST("/hepan/view", slf.ViewHepan)
}

// GetIP godoc
// @Summary 获取IP
// @Schemes
// @Description 获取IP
// @Tags 排盘专家版
// @Accept json
// @Produce json
// Security BearerAuth
// @Success 200 {object} map[string]string
// @Router /ip [post]
func (slf *MasterHandler) GetIP(ctx *gin.Context) {
	slf.Reply(ctx, map[string]string{
		"ip":        ctx.ClientIP(),
		"forwarded": ctx.GetHeader("X-Forwarded-For"),
		"real":      ctx.GetHeader("X-Real-IP"),
	})
}

// GeJuBianGe god
// @Summary 格局别格
// @Schemes
// @Description 格局别格
// @Tags 排盘专家版
// @Accept json
// @Produce json
// Security BearerAuth
// @Param request body v1.GeJuBianGeRequest true "params"
// @Success 200 {object} v1.GeJuBianGeResponse
// @Router /geju/biange [post]
func (slf *MasterHandler) GeJuBianGe(ctx *gin.Context) {
	var req v1.GeJuBianGeRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	req.User = slf.Auth(ctx)
	req.UserAgent = ctx.GetHeader("Auth-Agent")
	res, err := slf.masterService.GeJuBianGe(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// XiyongWangshuai godoc
// @Summary 喜用旺衰
// @Schemes
// @Description 喜用旺衰
// @Tags 排盘专家版
// @Accept json
// @Produce json
// Security BearerAuth
// @Param request body v1.MasterXiyongWangshuaiRequest true "params"
// @Success 200 {object} v1.MasterXiyongWangshuaiResponse
// @Router /xiyongwangshaui [post]
func (slf *MasterHandler) XiyongWangshuai(ctx *gin.Context) {
	var req v1.MasterXiyongWangshuaiRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	res, err := slf.masterService.XiyongWangshuai(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// Master godoc
// @Summary 排盘专家版
// @Schemes
// @Description 排盘专家版
// @Tags 排盘专家版
// @Accept json
// @Produce json
// Security BearerAuth
// @Param request body v1.MasterRequest true "params"
// @Success 200 {object} v1.MasterResponse
// @Router /master/ [post]
func (slf *MasterHandler) Master(ctx *gin.Context) {
	var req v1.MasterRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	req.User = slf.Auth(ctx)
	req.UserAgent = ctx.GetHeader("Auth-Agent")
	req.IP = ctx.ClientIP()
	res, err := slf.masterService.Master(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// DayunLiunian godoc
// @Summary 大运流年
// @Schemes
// @Description 大运流年
// @Tags 排盘专家版
// @Accept json
// @Produce json
// @Param request body v1.DayunliunianRequest true "params"
// @Success 200 {object} v1.DayunliunianResponse
// @Router /dayunliunian [post]
func (slf *MasterHandler) DayunLiunian(ctx *gin.Context) {
	var req v1.DayunliunianRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	res, err := slf.masterService.DayunLiunian(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// DayunAnalysis godoc
// @Summary 大运分析
// @Schemes
// @Description 大运分析
// @Tags 排盘专家版
// @Accept json
// @Produce json
// @Param request body v1.DayunAnalysisRequest true "params"
// @Success 200 {object} v1.DayunAnalysisResponse
// @Router /dayun [post]
func (slf *MasterHandler) DayunAnalysis(ctx *gin.Context) {
	var req v1.DayunAnalysisRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	res, err := slf.masterService.DayunAnalysis(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// LiunianAnalysis godoc
// @Summary 流年分析
// @Schemes
// @Description 流年分析
// @Tags 排盘专家版
// @Accept json
// @Produce json
// @Param request body v1.LiunianAnalysisRequest true "params"
// @Success 200 {object} v1.LiunianAnalysisResponse
// @Router /liunian [post]
func (slf *MasterHandler) LiunianAnalysis(ctx *gin.Context) {
	var req v1.LiunianAnalysisRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	res, err := slf.masterService.LiunianAnalysis(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// Hepan godoc
// @Summary 合盘
// @Schemes
// @Description 合盘
// @Tags 合盘
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.HepanRequest true "params"
// @Success 200 {object} v1.HepanResponse
// @Router /hepan [post]
func (slf *MasterHandler) Hepan(ctx *gin.Context) {
	var req v1.HepanRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	req.User = slf.Auth(ctx)
	res, err := slf.masterService.Hepan(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// PageListHepan godoc
// @Summary 分页查询合盘记录
// @Schemes
// @Description 分页查询合盘记录
// @Tags 合盘
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.PageListHepanRequest true "params"
// @Success 200 {object} v1.PageListHepanResponse
// @Router /hepan/pageList [post]
func (slf *MasterHandler) PageListHepan(ctx *gin.Context) {
	var req v1.PageListHepanRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	req.Param.User = slf.Auth(ctx)
	res, err := slf.masterService.PageListHepan(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// DeleteHepan godoc
// @Summary 删除合盘记录
// @Schemes
// @Description 删除合盘记录
// @Tags 合盘
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.DeleteHepanRequest true "params"
// @Success 200 {object} v1.DeleteHepanResponse
// @Router /hepan/delete [post]
func (slf *MasterHandler) DeleteHepan(ctx *gin.Context) {
	var req v1.DeleteHepanRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	req.User = slf.Auth(ctx)
	err := slf.masterService.DeleteHepan(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, nil)
}

// ViewHepan godoc
// @Summary 查看合盘记录
// @Schemes
// @Description 查看合盘记录
// @Tags 合盘
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.ViewHepanRequest true "params"
// @Success 200 {object} v1.ViewHepanResponse
// @Router /hepan/view [post]
func (slf *MasterHandler) ViewHepan(ctx *gin.Context) {
	var req v1.ViewHepanRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	req.User = slf.Auth(ctx)
	res, err := slf.masterService.ViewHepan(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// GaoKao godoc
// @Summary 高考
// @Schemes
// @Description 高考
// @Tags 高考专题
// @Accept json
// @Produce json
// @Param request body v1.GaoKaoRequest true "params"
// @Success 200 {object} v1.GaoKaoResponse
// @Router /gaokao [post]
func (slf *MasterHandler) GaoKao(ctx *gin.Context) {
	var req v1.GaoKaoRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	req.User = slf.Auth(ctx)
	req.UserAgent = ctx.GetHeader("Auth-Agent")
	req.IP = ctx.ClientIP()
	res, err := slf.masterService.GaoKao(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// GaoKaoPreference godoc
// @Summary 高考志愿时间
// @Schemes
// @Description 高考志愿时间
// @Tags 高考专题
// @Accept json
// @Produce json
// @Param request body v1.GaoKaoPreferenceRequest true "params"
// @Success 200 {object} v1.GaoKaoPreferenceResponse
// @Router /gaokao/preference [post]
func (slf *MasterHandler) GaoKaoPreference(ctx *gin.Context) {
	var req v1.GaoKaoPreferenceRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	res, err := slf.masterService.GaoKaoPreference(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}
