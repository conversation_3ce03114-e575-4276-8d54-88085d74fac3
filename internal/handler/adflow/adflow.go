package adflow

import (
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"
)

type AdflowHandler struct {
	*handler.BaseHandler
	adflowService service.AdflowService
}

func NewAdflowHandler(
	baseHandler *handler.BaseHandler,
	adflowService service.AdflowService,
) *AdflowHandler {
	return &AdflowHandler{
		BaseHandler:   baseHandler,
		adflowService: adflowService,
	}
}

func (slf *AdflowHandler) Handle(required, optional, nameless *handler.AuthRouter) {
	nameless.POST("/minglibazi/", slf.MingliBazi)
	nameless.POST("/minglibazi/replay", slf.MingliBaziReplay)
	nameless.POST("/minglibazi/year", slf.MingliBaziYear)
	nameless.POST("/minglibazi/qw", slf.MingliBaziQwLink)
	nameless.POST("/minglibazi/sms", slf.MingliBaziSms)
	nameless.POST("/minglibazi/click", slf.MingliBaziClick)
}

// MingliBaziClick godoc
// @Summary 命理八字点击
// @Schemes
// @Description 命理八字点击
// @Tags 命理八字
// @Accept json
// @Produce json
// @Router /minglibazi/click [post]
func (slf *AdflowHandler) MingliBaziClick(ctx *gin.Context) {
	if err := slf.adflowService.MingliBaziClick(ctx); err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, nil)
}

// MingliBazi godoc
// @Summary 命理八字
// @Schemes
// @Description 命理八字
// @Tags 命理八字
// @Accept json
// @Produce json
// @Param request body v1.MingliBaziRequest true "params"
// @Success 200 {object} v1.MingliBaziResponse
// @Router /minglibazi/ [post]
func (slf *AdflowHandler) MingliBazi(ctx *gin.Context) {
	req := &v1.MingliBaziRequest{}
	if err := ctx.ShouldBindJSON(req); err != nil {
		slf.Reply(ctx, errors.Wrap(err, "AdflowHandler.MingliBazi"))
		return
	}
	req.UserAgent = ctx.GetHeader("Auth-Agent")
	req.IP = ctx.ClientIP()
	data, err := slf.adflowService.MingliBazi(ctx, req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, data)
}

// MingliBaziReplay godoc
// @Summary 命理八字重播
// @Schemes
// @Description 命理八字重播
// @Tags 命理八字
// @Accept json
// @Produce json
// @Param request body v1.MingliBaziReplayRequest true "params"
// @Success 200 {object} v1.MingliBaziReplayResponse
// @Router /minglibazi/replay [post]
func (slf *AdflowHandler) MingliBaziReplay(ctx *gin.Context) {
	req := &v1.MingliBaziReplayRequest{}
	if err := ctx.ShouldBindJSON(req); err != nil {
		slf.Reply(ctx, errors.Wrap(err, "AdflowHandler.MingliBaziReplay"))
		return
	}
	data, err := slf.adflowService.MingliBaziReplay(ctx, req)
	if err != nil {
		slf.Log(ctx).Error("MingliBaziReplay", zap.Error(err))
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, data)
}

// MingliBaziYear godoc
// @Summary 命理八字年运
// @Schemes
// @Description 命理八字年运
// @Tags 命理八字
// @Accept json
// @Produce json
// @Param request body v1.MingliBaziYearRequest true "params"
// @Success 200 {object} v1.MingliBaziYearResponse
// @Router /minglibazi/year [post]
func (slf *AdflowHandler) MingliBaziYear(ctx *gin.Context) {
	req := &v1.MingliBaziYearRequest{}
	if err := ctx.ShouldBindJSON(req); err != nil {
		slf.Reply(ctx, errors.Wrap(err, "AdflowHandler.MingliBaziYear"))
		return
	}
	data, err := slf.adflowService.MingliBaziYear(ctx, req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, data)
}

// MingliBaziQwLink godoc
// @Summary 命理八字测算企微联系我
// @Schemes
// @Description 命理八字测算企微联系我
// @Tags 命理八字
// @Accept json
// @Produce json
// @Param request body v1.MingliBaziQwLinkRequest true "params"
// @Success 200 {object} v1.MingliBaziQwLinkResponse
// @Router /minglibazi/qw [post]
func (slf *AdflowHandler) MingliBaziQwLink(ctx *gin.Context) {
	req := &v1.MingliBaziQwLinkRequest{}
	if err := ctx.ShouldBindJSON(req); err != nil {
		slf.Reply(ctx, errors.Wrap(err, "AdflowHandler.MingliBaziQwLink"))
		return
	}
	data, err := slf.adflowService.MingliBaziQwLink(ctx, req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, data)
}

// MingliBaziSms godoc
// @Summary 命理八字短信
// @Schemes
// @Description 命理八字短信
// @Tags 命理八字
// @Accept json
// @Produce json
// @Param request body v1.MingliBaziSMSRequest true "params"
// @Success 200 {object} v1.MingliBaziSMSResponse
// @Router /minglibazi/sms [post]
func (slf *AdflowHandler) MingliBaziSms(ctx *gin.Context) {
	req := &v1.MingliBaziSMSRequest{}
	if err := ctx.ShouldBindJSON(req); err != nil {
		slf.Reply(ctx, errors.Wrap(err, "AdflowHandler.MingliBaziSms"))
		return
	}
	if err := slf.adflowService.MingliBaziSMS(ctx, req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, nil)
}
