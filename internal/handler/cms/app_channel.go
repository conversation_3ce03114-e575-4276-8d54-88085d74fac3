package cms

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"
)

type AppChannelHandler struct {
	*handler.BaseHandler
	acService service.AppChannelService
}

func NewAppChannelHandler(handler *handler.BaseHandler, acService service.AppChannelService) *AppChannelHandler {
	return &AppChannelHandler{
		BaseHandler: handler,
		acService:   acService,
	}
}

func (slf *AppChannelHandler) Handle(required, optional, nameless *handler.AuthRouter) {
	required.
		POST("/app/channel/create", slf.CreateAppChannel).
		POST("/app/channel/update", slf.UpdateAppChannel).
		POST("/app/channel/pageList", slf.PageListAppChannel)
}

// UpdateAppChannel godoc
// @Summary 更新渠道
// @Description 更新渠道
// @Tags 渠道
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.UpdateAppChannelRequest true "params"
// @Success 200 {object} v1.UpdateAppChannelResponse "更新渠道"
// @Router /app/channel/update [post]
func (slf *AppChannelHandler) UpdateAppChannel(ctx *gin.Context) {
	var req v1.UpdateAppChannelRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	err := slf.acService.UpdateAppChannel(ctx, &req)
	if err != nil {
		slf.Log(ctx).Error("appChannel.UpdateAppChannel", zap.Error(err))
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, nil)
}

// PageListAppChannel godoc
// @Summary 渠道列表
// @Description 渠道列表
// @Tags 渠道
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.PageListAppChannelRequest true "params"
// @Success 200 {object} v1.PageListAppChannelResponse "渠道列表"
// @Router /app/channel/pageList [post]
func (slf *AppChannelHandler) PageListAppChannel(ctx *gin.Context) {
	var req v1.PageListAppChannelRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	res, err := slf.acService.PageListAppChannel(ctx, &req)
	if err != nil {
		slf.Log(ctx).Error("appChannel.PageListAppChannel", zap.Error(err))
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// CreateAppChannel godoc
// @Summary 创建渠道
// @Description 创建渠道
// @Tags 渠道
// @Accept json
// @Produce json
// Security BearerAuth
// @Param request body v1.CreateAppChannelRequest true "params"
// @Success 200 {object} v1.CreateAppChannelResponse "创建渠道"
// @Router /app/channel/create [post]
func (slf *AppChannelHandler) CreateAppChannel(ctx *gin.Context) {
	var req v1.CreateAppChannelRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	res, err := slf.acService.CreateAppChannel(ctx, &req)
	if err != nil {
		slf.Log(ctx).Error("appChannel.CreateAppChannel", zap.Error(err))
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}
