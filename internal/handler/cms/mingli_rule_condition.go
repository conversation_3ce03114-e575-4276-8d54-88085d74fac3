package cms

import (
	"fmt"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"

	"github.com/gin-gonic/gin"
)

type MingliRuleConditionHandler struct {
	*handler.BaseHandler
	mingliRuleConditionService service.MingliRuleConditionService
}

func NewMingliRuleConditionHandler(
	handler *handler.BaseHandler,
	mingliRuleConditionService service.MingliRuleConditionService,
) *MingliRuleConditionHandler {
	return &MingliRuleConditionHandler{
		BaseHandler:                handler,
		mingliRuleConditionService: mingliRuleConditionService,
	}
}

func (slf *MingliRuleConditionHandler) Handle(required, _, _ *handler.AuthRouter) {
	required.
		POST("/mingliRuleCondition/create", slf.CreateMingliRuleCondition).
		POST("/mingliRuleCondition/update", slf.UpdateMingliRuleCondition).
		POST("/mingliRuleCondition/detail", slf.GetMingliRuleConditionDetail).
		POST("/mingliRuleCondition/pageList", slf.PageListMingliRuleCondition).
		POST("/mingliRuleCondition/delete", slf.DeleteMingliRuleCondition).
		POST("/mingliRuleCondition/setZuodui", slf.SetMingliRuleConditionZuodui).
		POST("/mingliRuleCondition/setXiji", slf.SetMingliRuleConditionXiji)
}

// CreateMingliRuleCondition godoc
// @Summary 创建命理规则条件
// @Schemes
// @Description 创建命理规则条件
// @Tags 命理规则条件
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.CreateMingliRuleConditionRequest true "params"
// @Success 200 {object} v1.CreateMingliRuleConditionResponse
// @Router /mingliRuleCondition/create [post]
func (slf *MingliRuleConditionHandler) CreateMingliRuleCondition(ctx *gin.Context) {
	var req v1.CreateMingliRuleConditionRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	resp, err := slf.mingliRuleConditionService.CreateMingliRuleCondition(ctx, &req)
	if err != nil {
		err = fmt.Errorf("mingliRuleConditionService.CreateMingliRuleCondition: %w", err)
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, resp)
}

// UpdateMingliRuleCondition godoc
// @Summary 更新命理规则条件
// @Schemes
// @Description 更新命理规则条件
// @Tags 命理规则条件
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.UpdateMingliRuleConditionRequest true "params"
// @Success 200 {object} v1.UpdateMingliRuleConditionResponse
// @Router /mingliRuleCondition/update [post]
func (slf *MingliRuleConditionHandler) UpdateMingliRuleCondition(ctx *gin.Context) {
	var req v1.UpdateMingliRuleConditionRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	if err := slf.mingliRuleConditionService.UpdateMingliRuleCondition(ctx, &req); err != nil {
		err = fmt.Errorf("mingliRuleConditionService.UpdateMingliRuleCondition: %w", err)
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, nil)
}

// GetMingliRuleConditionDetail godoc
// @Summary 获取命理规则条件
// @Schemes
// @Description 获取命理规则条件
// @Tags 命理规则条件
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.GetMingliRuleConditionDetailRequest true "params"
// @Success 200 {object} v1.GetMingliRuleConditionDetailResponse
// @Router /mingliRuleCondition/detail [post]
func (slf *MingliRuleConditionHandler) GetMingliRuleConditionDetail(ctx *gin.Context) {
	var req v1.GetMingliRuleConditionDetailRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	resp, err := slf.mingliRuleConditionService.GetMingliRuleConditionDetail(ctx, &req)
	if err != nil {
		err = fmt.Errorf("mingliRuleConditionService.GetMingliRuleConditionDetail: %w", err)
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, resp)
}

// PageListMingliRuleCondition godoc
// @Summary 分页查询命理规则条件
// @Schemes
// @Description 分页查询命理规则条件
// @Tags 命理规则条件
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.PageListMingliRuleConditionRequest true "params"
// @Success 200 {object} v1.PageListMingliRuleConditionResponse
// @Router /mingliRuleCondition/pageList [post]
func (slf *MingliRuleConditionHandler) PageListMingliRuleCondition(ctx *gin.Context) {
	var req v1.PageListMingliRuleConditionRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	resp, err := slf.mingliRuleConditionService.PageListMingliRuleCondition(ctx, &req)
	if err != nil {
		err = fmt.Errorf("mingliRuleConditionService.PageListMingliRuleCondition: %w", err)
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, resp)
}

// DeleteMingliRuleCondition godoc
// @Summary 删除命理规则条件
// @Schemes
// @Description 删除命理规则条件
// @Tags 命理规则条件
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.DeleteMingliRuleConditionRequest true "params"
// @Success 200 {object} v1.DeleteMingliRuleConditionResponse
// @Router /mingliRuleCondition/delete [post]
func (slf *MingliRuleConditionHandler) DeleteMingliRuleCondition(ctx *gin.Context) {
	var req v1.DeleteMingliRuleConditionRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	if err := slf.mingliRuleConditionService.DeleteMingliRuleCondition(ctx, &req); err != nil {
		err = fmt.Errorf("mingliRuleConditionService.DeleteMingliRuleCondition: %w", err)
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, nil)
}

// SetMingliRuleConditionZuodui godoc
// @Summary 设置坐对
// @Schemes
// @Description 设置坐对
// @Tags 命理规则条件
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.SetMingliRuleConditionZuoduiRequest true "params"
// @Success 200	{object} v1.SetMingliRuleConditionZuoduiResponse
// @Router /mingliRuleCondition/setZuodui [post]
func (slf *MingliRuleConditionHandler) SetMingliRuleConditionZuodui(ctx *gin.Context) {
	var req v1.SetMingliRuleConditionZuoduiRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	if err := slf.mingliRuleConditionService.SetMingliRuleConditionZuodui(ctx, &req); err != nil {
		err = fmt.Errorf("mingliRuleConditionService.SetZuodui: %w", err)
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, nil)
}

// SetMingliRuleConditionXiji godoc
// @Summary 设置喜忌
// @Schemes
// @Description 设置喜忌
// @Tags 命理规则条件
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.SetMingliRuleConditionXijiRequest true "params"
// @Success 200 {object} v1.SetMingliRuleConditionXijiResponse
// @Router /mingliRuleCondition/setXiji [post]
func (slf *MingliRuleConditionHandler) SetMingliRuleConditionXiji(ctx *gin.Context) {
	var req v1.SetMingliRuleConditionXijiRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	if err := slf.mingliRuleConditionService.SetMingliRuleConditionXiji(ctx, &req); err != nil {
		err = fmt.Errorf("mingliRuleConditionService.SetXiji: %w", err)
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, nil)
}
