package cms

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"
)

type AppVersionHandler struct {
	*handler.BaseHandler
	avService service.AppVersionService
}

func NewAppVersionHandler(handler *handler.BaseHandler, avService service.AppVersionService) *AppVersionHandler {
	return &AppVersionHandler{
		BaseHandler: handler,
		avService:   avService,
	}
}

func (slf *AppVersionHandler) Handle(required, optional, nameless *handler.AuthRouter) {
	required.
		POST("/app/version/create", slf.CreateAppVersion).
		POST("/app/version/update", slf.UpdateAppVersion).
		POST("/app/version/publish", slf.PublishAppVersion).
		POST("/app/version/recall", slf.RecallAppVersion).
		POST("/app/version/pageList", slf.PageListAppVersion)
}

// CreateAppVersion godoc
// @Summary 创建版本
// @Description 创建版本
// @Tags 应用版本
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.CreateAppVersionRequest true "params"
// @Success 200 {object} v1.CreateAppVersionResponse "创建版本"
// @Router /app/version/create [post]
func (slf *AppVersionHandler) CreateAppVersion(ctx *gin.Context) {
	var req v1.CreateAppVersionRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	resp, err := slf.avService.CreateAppVersion(ctx, &req)
	if err != nil {
		slf.Log(ctx).Error("appVersion.CreateAppVersion", zap.Error(err))
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, resp)
}

// UpdateAppVersion godoc
// @Summary 更新版本
// @Description 更新版本
// @Tags 应用版本
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.UpdateAppVersionRequest true "params"
// @Success 200 {object} v1.UpdateAppVersionResponse "更新版本"
// @Router /app/version/update [post]
func (slf *AppVersionHandler) UpdateAppVersion(ctx *gin.Context) {
	var req v1.UpdateAppVersionRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	err := slf.avService.UpdateAppVersion(ctx, &req)
	if err != nil {
		slf.Log(ctx).Error("appVersion.UpdateAppVersion", zap.Error(err))
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, nil)
}

// PageListAppVersion godoc
// @Summary 版本列表
// @Description 版本列表
// @Tags 应用版本
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.PageListAppVersionRequest true "params"
// @Success 200 {object} v1.PageListAppVersionResponse "版本列表"
// @Router /app/version/pageList [post]
func (slf *AppVersionHandler) PageListAppVersion(ctx *gin.Context) {
	var req v1.PageListAppVersionRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	resp, err := slf.avService.PageListAppVersion(ctx, &req)
	if err != nil {
		slf.Log(ctx).Error("appVersion.PageListAppVersion", zap.Error(err))
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, resp)
}

// PublishAppVersion godoc
// @Summary 发布版本
// @Description 发布版本
// @Tags 应用版本
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.PublishAppVersionRequest true "params"
// @Success 200 {object} v1.PublishAppVersionResponse "发布版本"
// @Router /app/version/publish [post]
func (slf *AppVersionHandler) PublishAppVersion(ctx *gin.Context) {
	var req v1.PublishAppVersionRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	err := slf.avService.PublishAppVersion(ctx, &req)
	if err != nil {
		slf.Log(ctx).Error("appVersion.PublishAppVersion", zap.Error(err))
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, nil)
}

// RecallAppVersion godoc
// @Summary 撤回版本
// @Description 撤回版本
// @Tags 应用版本
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.RecallAppVersionRequest true "params"
// @Success 200 {object} v1.RecallAppVersionResponse "撤回版本"
// @Router /app/version/recall [post]
func (slf *AppVersionHandler) RecallAppVersion(ctx *gin.Context) {
	var req v1.RecallAppVersionRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	err := slf.avService.RecallAppVersion(ctx, &req)
	if err != nil {
		slf.Log(ctx).Error("appVersion.RecallAppVersion", zap.Error(err))
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, nil)
}
