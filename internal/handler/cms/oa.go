package cms

import (
	"github.com/ArtisanCloud/PowerLibs/v3/http/helper"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"
)

type OffiaccountHandler struct {
	*handler.BaseHandler
	offiaccountService service.OffiaccountService
}

func NewOffiaccountHandler(handler *handler.BaseHandler, offiaccountService service.OffiaccountService) *OffiaccountHandler {
	return &OffiaccountHandler{
		BaseHandler:        handler,
		offiaccountService: offiaccountService,
	}
}

func (slf *OffiaccountHandler) Handle(required, optional, nameless *handler.AuthRouter) {
	nameless.GET("/offiaccount", slf.OffiaccountVerify).
		POST("/offiaccount", slf.OffiaccountNotify)

	required.POST("/oa/qrcode/create", slf.OffiaccountQrcodeCreate)
	required.POST("/oa/menu/create", slf.OffiaccountMenuCreate)
	required.POST("/oa/menu/detail", slf.OffiaccountMenuDetail)

	required.POST("/oa/material/add", slf.AddMaterial)
}

// AddMaterial godoc
// @Summary 公众号上传永久素材
// @Description 公众号上传永久素材
// @Tags 微信公众号
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param type formData string true "素材类型"
// @Param remark formData string true "素材备注"
// @Param file formData file true "素材文件"
// @Success 200 {object} v1.OaAppMaterialResponse
// @Router /oa/material/add [post]
func (slf *OffiaccountHandler) AddMaterial(ctx *gin.Context) {
	// 读取from-data的文件
	typ := ctx.PostForm("type")
	remark := ctx.PostForm("remark")
	file, err := ctx.FormFile("file")
	if err != nil {
		slf.Reply(ctx, v1.ErrBadRequest)
		return
	}
	req := &v1.OaAppMaterialRequest{
		Type:       typ,
		FileHeader: &v1.FileHeader{FileHeader: file},
		Remark:     remark,
	}
	data, err := slf.offiaccountService.AddMaterial(ctx, req)
	if err != nil {
		slf.Log(ctx).Error("上传公众号素材失败", zap.Error(err))
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, data)
}

// OffiaccountMenuDetail godoc
// @Summary 获取公众号菜单
// @Description 获取公众号菜单
// @Tags 微信公众号
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} v1.OaGetMenuDetailResponse
// @Router /oa/menu/detail [post]
func (slf *OffiaccountHandler) OffiaccountMenuDetail(ctx *gin.Context) {
	data, err := slf.offiaccountService.GetMenuDetail(ctx)
	if err != nil {
		slf.Log(ctx).Error("获取公众号菜单失败", zap.Error(err))
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, data)
}

// OffiaccountMenuCreate godoc
// @Summary 创建公众号菜单
// @Description 创建公众号菜单
// @Tags 微信公众号
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.OaCreateMenuRequest true "body"
// @Success 200 {object} v1.OaCreateMenuResponse
// @Router /oa/menu/create [post]
func (slf *OffiaccountHandler) OffiaccountMenuCreate(ctx *gin.Context) {
	req := &v1.OaCreateMenuRequest{}
	if err := ctx.ShouldBindJSON(req); err != nil {
		slf.Log(ctx).Error("创建公众号菜单参数绑定失败", zap.Error(err))
		slf.Reply(ctx, err)
		return
	}
	if err := slf.offiaccountService.CreateMenu(ctx, req); err != nil {
		slf.Log(ctx).Error("创建公众号菜单失败", zap.Error(err))
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, nil)
}

// OffiaccountQrcodeCreate godoc
// @Summary 创建公众号自定义参数二维码
// @Description 创建公众号自定义参数二维码
// @Tags 微信公众号
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.OaCreateQrcodeRequest true "body"
// @Success 200 {object} v1.OaCreateQrcodeResponse
// @Router /oa/qrcode/create [post]
func (slf *OffiaccountHandler) OffiaccountQrcodeCreate(ctx *gin.Context) {
	req := &v1.OaCreateQrcodeRequest{}
	if err := ctx.ShouldBindJSON(req); err != nil {
		slf.Log(ctx).Error("创建公众号自定义参数二维码参数绑定失败", zap.Error(err))
		slf.Reply(ctx, err)
		return
	}
	data, err := slf.offiaccountService.CreateQrcode(ctx, req)
	if err != nil {
		slf.Log(ctx).Error("创建公众号自定义参数二维码失败", zap.Error(err))
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, data)
}

// OffiaccountVerify 公众号验证回调
func (slf *OffiaccountHandler) OffiaccountVerify(ctx *gin.Context) {
	resp, err := slf.offiaccountService.OffiaccountVerify(ctx.Request)
	if err != nil {
		slf.Log(ctx).Error("公众号验证回调失败", zap.String("应用", "offiaccount"), zap.Error(err))
		_ = ctx.Error
		return
	}
	_, _ = ctx.Writer.WriteString(resp)

	/*
		if err = helper.HttpResponseSend(rs, ctx.Writer); err != nil {
			slf.Log(ctx).Error("公众号处理回调验证失败", zap.String("应用", "offiaccount"), zap.Error(err))
			_ = ctx.Error(err)
			return
		}
	*/
}

// OffiaccountNotify 公众号通知回调
func (slf *OffiaccountHandler) OffiaccountNotify(ctx *gin.Context) {
	slf.Log(ctx).Info("公众号处理回调通知", zap.String("应用", "offiaccount"))
	rs, err := slf.offiaccountService.OffiaccountNotify(ctx.Request)
	if err != nil {
		slf.Log(ctx).Error("公众号处理回调通知失败", zap.String("应用", "offiaccount"), zap.Error(err))
		_ = ctx.Error
		return
	}
	if err = helper.HttpResponseSend(rs, ctx.Writer); err != nil {
		slf.Log(ctx).Error("公众号处理回调通知失败", zap.String("应用", "offiaccount"), zap.Error(err))
		_ = ctx.Error(err)
		return
	}
}
