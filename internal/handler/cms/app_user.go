package cms

import (
	"github.com/gin-gonic/gin"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"
)

type AppUserHandler struct {
	*handler.BaseHandler
	appUserService service.AppUserService
}

func NewAppUserHandler(
	handler *handler.BaseHandler,
	appUserService service.AppUserService,
) *AppUserHandler {
	return &AppUserHandler{
		BaseHandler:    handler,
		appUserService: appUserService,
	}
}

func (slf *AppUserHandler) Handle(required, _, nameless *handler.AuthRouter) {
	nameless.POST("/appUser/pageList", slf.PageListAppUser)
	nameless.POST("/appUser/pageListVip", slf.PageListAppUserVip)
}

// PageListAppUserVip godoc
// @Summary 分页查询VIP用户列表
// @Schemes
// @Description 分页查询VIP用户列表
// @Tags 用户
// @Accept json
// @Produce json
// Security BearerAuth
// @Param request body v1.PageListAppUserVipRequest true "params"
// @Success 200 {object} v1.PageListAppUserVipResponse
// @Router /appUser/pageListVip [post]
func (slf *AppUserHandler) PageListAppUserVip(ctx *gin.Context) {
	var req v1.PageListAppUserVipRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	data, err := slf.appUserService.PageListAppUserVip(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, data)
}

// PageListAppUser godoc
// @Summary 分页查询用户列表
// @Schemes
// @Description 分页查询用户列表
// @Tags 用户
// @Accept json
// @Produce json
// Security BearerAuth
// @Param request body v1.PageListAppUserRequest true "params"
// @Success 200 {object} v1.PageListAppUserResponse
// @Router /appUser/pageList [post]
func (slf *AppUserHandler) PageListAppUser(ctx *gin.Context) {
	var req v1.PageListAppUserRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	data, err := slf.appUserService.PageListAppUser(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, data)
}
