package cms

import (
	"github.com/gin-gonic/gin"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"
)

type FeedbackHandler struct {
	*handler.BaseHandler
	feedbackService service.FeedbackService
}

func NewFeedbackHandler(
	handler *handler.BaseHandler,
	feedbackService service.FeedbackService,
) *FeedbackHandler {
	return &FeedbackHandler{
		BaseHandler:     handler,
		feedbackService: feedbackService,
	}
}

func (slf *FeedbackHandler) Handle(required, optional, nameless *handler.AuthRouter) {
	required.POST("/feedback/reply", slf.ReplyFeedback)
	required.POST("/feedback/pageList", slf.PageListFeedback)
}

// ReplyFeedback godoc
// @Summary 回复反馈
// @Description 回复反馈
// @Tags 反馈
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.ReplyFeedbackRequest true "回复反馈请求"
// @Router /feedback/reply [post]
func (slf *FeedbackHandler) ReplyFeedback(ctx *gin.Context) {
	var req v1.ReplyFeedbackRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	if err := slf.feedbackService.ReplyFeedback(ctx, &req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, nil)
}

// PageListFeedback godoc
// @Summary 分页查询反馈列表
// @Description 分页查询反馈列表
// @Tags 反馈
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.PageListFeedbackRequest true "分页查询反馈列表请求"
// @Success 200 {object} v1.PageListFeedbackResponse
// @Router /feedback/pageList [post]
func (slf *FeedbackHandler) PageListFeedback(ctx *gin.Context) {
	var req v1.PageListFeedbackRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	data, err := slf.feedbackService.PageListFeedback(ctx, &req)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, data)
}
