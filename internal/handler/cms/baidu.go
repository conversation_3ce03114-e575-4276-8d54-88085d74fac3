package cms

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"
	"zodiacus/pkg/log"
	"zodiacus/third_party/baidu/ocpc"
)

type BaiduHandler struct {
	*handler.BaseHandler
	logger      *log.Logger
	ocpcService service.OcpcService
}

// NewBaiduHandler 创建百度处理器
func NewBaiduHandler(handler *handler.BaseHandler, ocpcService service.OcpcService, logger *log.Logger) *BaiduHandler {
	return &BaiduHandler{
		BaseHandler: handler,
		logger:      logger,
		ocpcService: ocpcService,
	}
}

func (slf *BaiduHandler) Handle(required, optional, nameless *handler.AuthRouter) {
	nameless.POST("/baidu/ocpc/notice/android", slf.OcpcAndroidNotice)
	nameless.GET("/baidu/ocpc/notice/android", slf.OcpcAndroidNotice)
}

// OcpcAndroidNotice 安卓监测地址
func (slf *BaiduHandler) OcpcAndroidNotice(ctx *gin.Context) {
	/*
		akey：NjkyNTE2NDM=
		安卓监测地址：
		https://paipan-cms-api.yaoyoumeng.com/v1/baidu/ocpc/notice/android?imei_md5={{IMEI_MD5}}&oaid={{OAID}}&android_id_md5={{ANDROID_ID_MD5}}&mac={{MAC}}&os_version={{OS_VERSION}}&os_type={{OS_TYPE}}&ip={{IP}}&ua={{UA}}&ts={{TS}}&pid={{PLAN_ID}}&uid={{UNIT_ID}}&aid={{IDEA_ID}}&click_id={{CLICK_ID}}&callType=v2&ext_info={{EXT_INFO}}
	*/
	data, err := ocpc.ParseClickData(ctx)
	if err != nil {
		slf.Log(ctx).Error("百度安卓OCPC：解析点击数据失败", zap.Error(err))
		slf.Reply(ctx, err)
		return
	}
	if err = slf.ocpcService.Notice(ctx, data); err != nil {
		slf.Log(ctx).Error("百度安卓OCPC：ocpcService.Notice", zap.Error(err))
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, nil)
}
