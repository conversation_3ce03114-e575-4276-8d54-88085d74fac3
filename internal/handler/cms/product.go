package cms

import (
	v1 "zodiacus/api/v1"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"

	"github.com/gin-gonic/gin"
)

type AtlasProductHandler struct {
	*handler.BaseHandler
	atlasProductService service.AtlasProductService
}

func NewAtlasProductHandler(
	handler *handler.BaseHandler,
	atlasProductService service.AtlasProductService,
) *AtlasProductHandler {
	return &AtlasProductHandler{
		BaseHandler:         handler,
		atlasProductService: atlasProductService,
	}
}

func (slf *AtlasProductHandler) Handle(required, optional, nameless *handler.AuthRouter) {
	required.
		POST("/atlasProduct/create", slf.CreateAtlasProduct).
		POST("/atlasProduct/list", slf.ListAtlasProduct).
		POST("/atlasProduct/update", slf.UpdateAtlasProduct).
		POST("/atlasProduct/delete", slf.DeleteAtlasProduct)
}

// CreateAtlasProduct godoc
// @Summary 创建产品
// @Description 创建产品
// @Tags 产品
// @Accept json
// @Produce json
// @Param request body v1.AtlasCreateProductReq true "创建产品请求"
// @Success 200 {object} v1.Response{data=string} "创建产品成功"
// @Router /atlasProduct/create [post]
func (slf *AtlasProductHandler) CreateAtlasProduct(c *gin.Context) {
	var (
		req v1.AtlasCreateProductReq
		ctx = c.Request.Context()
	)
	if err := c.ShouldBindJSON(&req); err != nil {
		slf.Reply(c, err)
		return
	}

	id, err := slf.atlasProductService.CreateAtlasProduct(ctx, &req)
	if err != nil {
		slf.Reply(c, err)
		return
	}

	slf.Reply(c, id)
}

// ListAtlasProduct godoc
// @Summary 获取产品列表
// @Description 获取产品列表
// @Tags 产品
// @Accept json
// @Produce json
// @Param request body v1.AtlasListProductRequest true "获取产品列表请求"
// @Success 200 {object} v1.Response{data=v1.AtlasListProductResponse} "获取产品列表成功"
// @Router /atlasProduct/list [post]
func (slf *AtlasProductHandler) ListAtlasProduct(c *gin.Context) {
	var req v1.AtlasListProductRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		slf.Reply(c, err)
		return
	}

	resp, err := slf.atlasProductService.ListAtlasProduct(c, &req)
	if err != nil {
		slf.Reply(c, err)
		return
	}

	slf.Reply(c, resp)
}

// UpdateAtlasProduct godoc
// @Summary 更新产品
// @Description 更新产品
// @Tags 产品
// @Accept json
// @Produce json
// @Param request body v1.AtlasUpdateProductReq true "更新产品请求"
// @Success 200 {object} v1.Response{data=string} "更新产品成功"
// @Router /atlasProduct/update [post]
func (slf *AtlasProductHandler) UpdateAtlasProduct(c *gin.Context) {
	var req v1.AtlasUpdateProductReq
	if err := c.ShouldBindJSON(&req); err != nil {
		slf.Reply(c, err)
		return
	}

	err := slf.atlasProductService.UpdateAtlasProduct(c, &req)
	if err != nil {
		slf.Reply(c, err)
		return
	}

	slf.Reply(c, nil)
}

// DeleteAtlasProduct godoc
// @Summary 删除产品
// @Description 删除产品
// @Tags 产品
// @Accept json
// @Produce json
// @Param request body v1.AtlasDeleteProductReq true "删除产品请求"
// @Success 200 {object} v1.Response{data=string} "删除产品成功"
// @Router /atlasProduct/delete [post]
func (slf *AtlasProductHandler) DeleteAtlasProduct(c *gin.Context) {
	var req v1.AtlasDeleteProductReq
	if err := c.ShouldBindJSON(&req); err != nil {
		slf.Reply(c, err)
		return
	}

	err := slf.atlasProductService.DeleteAtlasProduct(c, req.Id)
	if err != nil {
		slf.Reply(c, err)
		return
	}

	slf.Reply(c, nil)
}
