package cms

import (
	"fmt"
	"github.com/gin-gonic/gin"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"
)

type TermHandler struct {
	*handler.BaseHandler
	termService service.TermService
}

func NewTermHandler(
	handler *handler.BaseHandler,
	termService service.TermService,
) *TermHandler {
	return &TermHandler{
		BaseHandler: handler,
		termService: termService,
	}
}

func (slf *TermHandler) Handle(required, optional, nameless *handler.AuthRouter) {
	nameless.POST("/term/query", slf.QueryTerm)
}

// QueryTerm godoc
// @Summary 查询术语
// @Schemes
// @Description 查询术语
// @Tags 术语
// @Accept json
// @Produce json
// @Param request body v1.QueryTermRequest true "params"
// @Success 200 {object} v1.QueryTermResponse
// @Router /term/query [post]
func (slf *TermHandler) QueryTerm(ctx *gin.Context) {
	var req v1.QueryTermRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	data, err := slf.termService.QueryTermByName(ctx, &req)
	if err != nil {
		err = fmt.Errorf("QueryTermByName: %w", err)
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, data)
}
