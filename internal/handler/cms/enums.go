package cms

import (
	"fmt"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/handler"
	"zodiacus/internal/service"

	"github.com/gin-gonic/gin"
)

type EnumsHandler struct {
	*handler.BaseHandler
	enumsService service.EnumsService
}

func NewEnumsHandler(
	handler *handler.BaseHandler,
	enumsService service.EnumsService,
) *EnumsHandler {
	return &EnumsHandler{
		BaseHandler:  handler,
		enumsService: enumsService,
	}
}

func (slf *EnumsHandler) Handle(required, optional, _ *handler.AuthRouter) {
	optional.
		POST("/enums/module", slf.Module).
		POST("/enums/tiangan", slf.Tiangan).
		POST("/enums/dizhi", slf.Dizhi).
		POST("/enums/zuodui", slf.Zuodui).
		POST("/enums/zuoduiPlus", slf.ZuoduiPlus).
		POST("/enums/nayin", slf.Nayin).
		POST("/enums/ganzhi", slf.Ganzhi).
		POST("/enums/shishen", slf.<PERSON>shen).
		POST("/enums/wuxing", slf.Wuxing).
		POST("/enums/xiji", slf.Xiji).
		POST("/enums/shensha", slf.Shensha).
		POST("/enums/shierchangsheng", slf.Shierchangsheng).
		POST("/enums/lunar", slf.Lunar).
		POST("/enums/app", slf.App)
}

// App godoc
// @Summary App
// @Schemes
// @Description App
// @Tags 枚举
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.EnumsAppRequest true "params"
// @Success 200 {object} v1.EnumsAppResponse
// @Router /enums/app [post]
func (slf *EnumsHandler) App(ctx *gin.Context) {
	var req v1.EnumsAppRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	res, err := slf.enumsService.App(ctx, &req)
	if err != nil {
		err = fmt.Errorf("enumsService.App: %w", err)
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// Module godoc
// @Summary 模块
// @Schemes
// @Description 模块
// @Tags 枚举
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.EnumsModuleRequest true "params"
// @Success 200 {object} v1.EnumsModuleResponse
// @Router /enums/module [post]
func (slf *EnumsHandler) Module(ctx *gin.Context) {
	res, err := slf.enumsService.Module(ctx)
	if err != nil {
		err = fmt.Errorf("enumsService.Module: %w", err)
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// Tiangan godoc
// @Summary 天干
// @Schemes
// @Description 天干
// @Tags 枚举
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.EnumsTianganRequest true "params"
// @Success 200 {object} v1.EnumsTianganResponse
// @Router /enums/tiangan [post]
func (slf *EnumsHandler) Tiangan(ctx *gin.Context) {
	res, err := slf.enumsService.Tiangan(ctx)
	if err != nil {
		err = fmt.Errorf("enumsService.Tiangan: %w", err)
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// Dizhi godoc
// @Summary 地支
// @Schemes
// @Description 地支
// @Tags 枚举
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.EnumsDizhiRequest true "params"
// @Success 200 {object} v1.EnumsDizhiResponse
// @Router /enums/dizhi [post]
func (slf *EnumsHandler) Dizhi(ctx *gin.Context) {
	res, err := slf.enumsService.Dizhi(ctx)
	if err != nil {
		err = fmt.Errorf("enumsService.Dizhi: %w", err)
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// Zuodui godoc
// @Summary 坐-对
// @Schemes
// @Description 坐-对
// @Tags 枚举
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.EnumsZuoduiRequest true "params"
// @Success 200 {object} v1.EnumsZuoduiResponse
// @Router /enums/zuodui [post]
func (slf *EnumsHandler) Zuodui(ctx *gin.Context) {
	res, err := slf.enumsService.Zuodui(ctx)
	if err != nil {
		err = fmt.Errorf("Zuodui: %w", err)
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// ZuoduiPlus godoc
// @Summary 坐-对+
// @Schemes
// @Description 坐-对+
// @Tags 枚举
// @Accept json
// @Produce json
// @Param request body v1.EnumsZuoduiPlusRequest true "params"
// @Success 200 {object} v1.EnumsZuoduiPlusResponse
// @Router /enums/zuoduiPlus [post]
func (slf *EnumsHandler) ZuoduiPlus(ctx *gin.Context) {
	res, err := slf.enumsService.ZuoduiPlus(ctx)
	if err != nil {
		err = fmt.Errorf("enumsService.ZuoduiPlus: %w", err)
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// Nayin godoc
// @Summary 纳音
// @Schemes
// @Description 纳音
// @Tags 枚举
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.EnumsNayinRequest true "params"
// @Success 200 {object} v1.EnumsNayinResponse
// @Router /enums/nayin [post]
func (slf *EnumsHandler) Nayin(ctx *gin.Context) {
	res, err := slf.enumsService.Nayin(ctx)
	if err != nil {
		err = fmt.Errorf("enumsService.Nayin: %w", err)
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// Ganzhi godoc
// @Summary 干支
// @Schemes
// @Description 干支
// @Tags 枚举
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.EnumsGanzhiRequest true "params"
// @Success 200 {object} v1.EnumsGanzhiResponse
// @Router /enums/ganzhi [post]
func (slf *EnumsHandler) Ganzhi(ctx *gin.Context) {
	res, err := slf.enumsService.Ganzhi(ctx)
	if err != nil {
		err = fmt.Errorf("enumsService.Ganzhi: %w", err)
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// Shishen godoc
// @Summary 十神
// @Schemes
// @Description 十神
// @Tags 枚举
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.EnumsShishenRequest true "params"
// @Success 200 {object} v1.EnumsShishenResponse
// @Router /enums/shishen [post]
func (slf *EnumsHandler) Shishen(ctx *gin.Context) {
	res, err := slf.enumsService.Shishen(ctx)
	if err != nil {
		err = fmt.Errorf("enumsService.Shishen: %w", err)
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// Wuxing godoc
// @Summary 五行
// @Schemes
// @Description 五行
// @Tags 枚举
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.EnumsWuxingRequest true "params"
// @Success 200 {object} v1.EnumsWuxingResponse
// @Router /enums/wuxing [post]
func (slf *EnumsHandler) Wuxing(ctx *gin.Context) {
	res, err := slf.enumsService.Wuxing(ctx)
	if err != nil {
		err = fmt.Errorf("enumsService.TblWuxing: %w", err)
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// Xiji godoc
// @Summary 喜忌
// @Schemes
// @Description 喜忌
// @Tags 枚举
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.EnumsXijiRequest true "params"
// @Success 200 {object} v1.EnumsXijiResponse
// @Router /enums/xiji [post]
func (slf *EnumsHandler) Xiji(ctx *gin.Context) {
	res, err := slf.enumsService.Xiji(ctx)
	if err != nil {
		err = fmt.Errorf("enumsService.Xiji: %w", err)
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// Shensha godoc
// @Summary 神煞
// @Schemes
// @Description 神煞
// @Tags 枚举
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.EnumsShenshaRequest true "params"
// @Success 200 {object} v1.EnumsShenshaResponse
// @Router /enums/shensha [post]
func (slf *EnumsHandler) Shensha(ctx *gin.Context) {
	res, err := slf.enumsService.Shensha(ctx)
	if err != nil {
		err = fmt.Errorf("enumsService.Shensha: %w", err)
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// Shierchangsheng godoc
// @Summary 十二长生
// @Schemes
// @Description 十二长生
// @Tags 枚举
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body v1.EnumsShierchangshengRequest true "params"
// @Success 200 {object} v1.EnumsShierchangshengResponse
// @Router /enums/shierchangsheng [post]
func (slf *EnumsHandler) Shierchangsheng(ctx *gin.Context) {
	res, err := slf.enumsService.Shierchangsheng(ctx)
	if err != nil {
		err = fmt.Errorf("enumsService.Shierchangsheng: %w", err)
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}

// Lunar godoc
// @Summary 获取农历列表
// @Schemes
// @Description 获取农历列表
// @Tags 枚举
// @Accept json
// @Produce json
// @Param request body v1.EnumsLunarRequest true "params"
// @Success 200 {object} v1.EnumsLunarResponse
// @Router /enums/lunar [post]
func (slf *EnumsHandler) Lunar(ctx *gin.Context) {
	var req v1.EnumsLunarRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		slf.Reply(ctx, err)
		return
	}
	res, err := slf.enumsService.Lunar(ctx, req.Year)
	if err != nil {
		slf.Reply(ctx, err)
		return
	}
	slf.Reply(ctx, res)
}
