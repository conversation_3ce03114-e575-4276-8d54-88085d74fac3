package model

import "github.com/uptrace/bun"

type Minggong struct {
	bun.BaseModel `bun:"minggong,alias:mg"`
	ID            int64  `bun:"id,pk" json:"-"`
	Name          string `bun:"name" json:"name"`
	Star          string `bun:"star" json:"star"`
	Summary       string `bun:"summary" json:"summary"`
	Temperament   string `bun:"temperament" json:"temperament"`
	Constellation string `bun:"constellation" json:"constellation"`
	Development   string `bun:"development" json:"development"`
	BaseFields4Time
}
