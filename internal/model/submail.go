package model

import "github.com/uptrace/bun"

/*
create table `submail_sms` (
    `id` int(11) not null auto_increment,
    `app` varchar(50) not null,
    `address` varchar(255) not null comment '手机号',
    `template_id` varchar(255) not null comment '模板ID',
    `vars` json comment '模板变量',
    `send_id` varchar(255) not null comment '发送ID',
    `status` tinyint(1) not null default 0 comment '状态：0-发送中、1-发送成功、2-发送失败',
    `content` text comment '发送内容',
    `delivered_at` bigint(20) not null default 0 comment '发送时间',
    `operator` varchar(255) not null comment '手机号归属运营商',
    `location` varchar(255) not null comment '手机号归属省份、城市',
    `dropped_at` bigint(20) not null default 0 comment '失败时间',
    `report` varchar(255) not null comment '网关失败回执',
    `report_desc` varchar(255) not null comment '网关失败回执描述',
    primary key (`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_general_ci;
*/

type SubMailSMS struct {
	bun.BaseModel `bun:"submail_sms,alias:ss"`
	ID            int64          `bun:"id,pk" json:"id"`
	App           string         `bun:"app" json:"app"`
	Address       string         `bun:"address" json:"address"`
	TemplateID    string         `bun:"template_id" json:"template_id"`
	Vars          map[string]any `bun:"vars" json:"vars"`
	SendID        string         `bun:"send_id" json:"send_id"`
	Status        int            `bun:"status" json:"status"`
	Content       string         `bun:"content" json:"content"`
	DeliveredAt   int64          `bun:"delivered_at" json:"delivered_at"`
	Operator      string         `bun:"operator" json:"operator"`
	Location      string         `bun:"location" json:"location"`
	DroppedAt     int64          `bun:"dropped_at" json:"dropped_at"`
	Report        string         `bun:"report" json:"report"`
	ReportDesc    string         `bun:"report_desc" json:"report_desc"`
	BaseFields4Time
}
