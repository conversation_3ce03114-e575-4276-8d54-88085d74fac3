package model

import (
	"github.com/uptrace/bun"
	"time"
)

/*
	create table `offiaccount_user` (
		id bigint unsigned auto_increment,
		open_id         varchar(128) default '' not null,
		union_id        varchar(128) default '' not null,
		subscribe       tinyint default 0 not null,
		language        varchar(32) default '' not null,
		subscribe_time  bigint default 0 not null,
		remark          varchar(255) default '' not null,
		group_id        int default 0 not null,
		tag_id_list     json,
		subscribe_scene varchar(255) default '' not null,
		qr_scene        int default 0 not null,
		qr_scene_str    varchar(255) default '' not null,
		created_at      datetime   default CURRENT_TIMESTAMP     not null,
		updated_at      datetime   default CURRENT_TIMESTAMP     not null,
		deleted_at      datetime   default '0001-01-01 00:00:00' null,
		primary key (id),
		unique index idx_open_id_deleted_at (open_id, deleted_at)
	)  ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci comment '微信公众号订阅用户';
*/

// OffiaccountUser 微信公众号订阅用户
type OffiaccountUser struct {
	bun.BaseModel   `bun:"oa_user,alias:ou"`
	ID              int64     `bun:"id,pk"`
	OpenID          string    `bun:"open_id"`
	UnionID         string    `bun:"union_id"`
	Subscribe       int       `bun:"subscribe"`
	Language        string    `bun:"language"`
	SubscribeTime   int       `bun:"subscribe_time"`
	UnsubscribeTime int       `bun:"unsubscribe_time"`
	Remark          string    `bun:"remark"`
	GroupID         int       `bun:"group_id"`
	TagIDList       []int     `bun:"tag_id_list"`
	SubscribeScene  string    `bun:"subscribe_scene"`
	QRScene         int       `bun:"qr_scene"`
	QRSceneStr      string    `bun:"qr_scene_str"`
	DeletedAt       time.Time `bun:",soft_delete,unique:idx_open_id_deleted_at,default:'0001-01-01 00:00:00'"`
	BaseFields4Time
}

type OaQrcode struct {
	bun.BaseModel `bun:"oa_qrcode,alias:oq"`
	ID            int64     `bun:"id,pk"`
	SceneStr      string    `bun:"scene_str"`
	Remark        string    `bun:"remark"`
	Ticket        string    `bun:"ticket"`
	ExpireSeconds int64     `bun:"expire_seconds"`
	Url           string    `bun:"url"`
	DeletedAt     time.Time `bun:",soft_delete,unique:idx_scene_str_deleted_at,default:'0001-01-01 00:00:00'"`
	BaseFields4Time
}

/*
create table `oa_material` (
    id bigint unsigned auto_increment,
    media_id       varchar(255) default '' not null,
    type           varchar(255) default '' not null,
    remark         varchar(255) default '' not null,
    url            varchar(255) default '' not null,
    created_at     datetime   default CURRENT_TIMESTAMP     not null,
    updated_at     datetime   default CURRENT_TIMESTAMP     not null,
    deleted_at     datetime   default '0001-01-01 00:00:00' null,
    primary key (id),
    unique index idx_media_id_deleted_at (media_id, deleted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci comment '微信公众号永久素材';
*/

// OffiaccountMaterial 微信公众号永久素材
type OffiaccountMaterial struct {
	bun.BaseModel `bun:"oa_material,alias:om"`
	ID            int64     `bun:"id,pk"`
	MediaID       string    `bun:"media_id"`
	Type          string    `bun:"type"`
	Remark        string    `bun:"remark"`
	Url           string    `bun:"url"`
	DeletedAt     time.Time `bun:",soft_delete,unique:idx_media_id_deleted_at,default:'0001-01-01 00:00:00'"`
	BaseFields4Time
}
