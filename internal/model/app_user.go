package model

import (
	"github.com/uptrace/bun"
	"time"
)

/*
CREATE TABLE `app_user` (
    id bigint primary key auto_increment,
    user_id varchar(100) not null unique,
    name varchar(100),
    display_name varchar(100),
    avatar varchar(200),
    phone varchar(50),
    country_code varchar(20),
    signup_application varchar(50) not null,
    signup_application_id bigint not null,
    deleted_at DATETIME NOT NULL DEFAULT '0001-01-01 00:00:00', -- 软删除标记
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,      -- 创建时间
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间
    UNIQUE KEY idx_user_id_deleted_at (user_id, deleted_at)
) engine=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
*/

// AppUser APP用户
type AppUser struct {
	bun.BaseModel `bun:"app_user,alias:au"`
	ID            int64     `bun:"id,pk"`                                      // 主键ID
	UserID        string    `bun:"user_id,unique:idx_user_id_deleted_at"`      // 用户唯一ID（不可变）
	Username      string    `bun:"username"`                                   // 用户名
	Nickname      string    `bun:"nickname"`                                   // 显示名称
	Avatar        string    `bun:"avatar"`                                     // 头像
	Phone         string    `bun:"phone"`                                      // 手机号
	Password      string    `bun:"password"`                                   // 密码
	RegisterIP    string    `bun:"register_ip"`                                // 注册IP
	DeletedAt     time.Time `bun:",soft_delete,default:'0001-01-01 00:00:00'"` // 删除时间
	BaseFields4Time
}
