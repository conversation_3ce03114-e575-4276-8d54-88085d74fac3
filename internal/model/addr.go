package model

import "github.com/uptrace/bun"

type (
	// AddrProvince 省份
	AddrProvince struct {
		bun.BaseModel `bun:"addr_provinces,alias:ap"`
		Code          int64  `bun:"code" json:"code"`
		Name          string `bun:"name" json:"name"`
	}
	// AddrArea 区县
	AddrArea struct {
		bun.BaseModel `bun:"addr_areas,alias:aa"`
		Code          int64  `bun:"code" json:"code"`
		Name          string `bun:"name" json:"name"`
		CityCode      int64  `bun:"cityCode" json:"cityCode"`
		ProvinceCode  int64  `bun:"provinceCode" json:"provinceCode"`
	}
	// AddrCity 城市
	AddrCity struct {
		bun.BaseModel `bun:"addr_cities,alias:ac"`
		Code          int64  `bun:"code" json:"code"`
		Name          string `bun:"name" json:"name"`
		ProvinceCode  int64  `bun:"provinceCode" json:"provinceCode"`
	}
)

type (
	// AddrCountry 国家与地区（海外）
	AddrCountry struct {
		bun.BaseModel `bun:"addr_country,alias:ac"`
		Code          int64  `bun:"code" json:"code"`
		Name          string `bun:"country" json:"name"`
	}
	// AddrCountryProvince 国家与地区（海外）省份
	AddrCountryProvince struct {
		bun.BaseModel `bun:"addr_out_provinces,alias:aop"`
		Code          int64  `bun:"code" json:"code"`
		Name          string `bun:"name" json:"name"`
		ParentsID     int64  `bun:"parents_id" json:"parentsId"`
	}
)
