package model

import (
	"encoding/json"
	"time"

	"github.com/uptrace/bun"
)

// -- 创建商品表
// CREATE TABLE `product` (
//     `id` int(11) NOT NULL AUTO_INCREMENT,
//     `sku_code` varchar(50) DEFAULT NULL COMMENT 'SKU编码',
//     `sku_name` varchar(50) NOT NULL COMMENT 'SKU名称',
//     `specification` varchar(50) NOT NULL COMMENT '规格',
//     `original_price` int NOT NULL COMMENT '划线价',
//     `sale_price` int NOT NULL COMMENT '出售价',
//     `stock` int(11) NOT NULL DEFAULT '0' COMMENT '库存量',
//     `type` tinyint(1) NOT NULL COMMENT '商品类型：0-其他，1-虚拟会员商品，2-咨询服务',
//     `application` varchar(50) NOT NULL COMMENT '所属应用',
//     `remark` varchar(255) DEFAULT NULL COMMENT '备注',
//     `enable` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用：0-禁用，1-启用',
//     `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
//     `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
//     PRIMARY KEY (`id`)
// ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品表';

// ALTER TABLE `product`
// ADD COLUMN `validity_days` int NOT NULL DEFAULT 0 COMMENT '会员有效期天数',
// MODIFY COLUMN `type` tinyint(1) NOT NULL COMMENT '商品类型：0-其他，1-虚拟会员商品，2-咨询服务',
// MODIFY COLUMN `enable` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用：0-禁用，1-启用';

// -- 插入数据
// INSERT INTO `product`
// (sku_code, sku_name, specification, original_price, sale_price, stock, type, application, remark, enable)
// VALUES
// ('7f4e8d31a2b5c9f6e0h3i7k9m1n4p6r8', '排盘-专业会员-月卡', '31天', 2800, 1800, 99999999, 1, 'horoscope', NULL, 1),
// ('2a5b8c3d6e9f1g4h7i0j2k5l8m3n6p9r', '排盘-专业会员-季卡', '92天', 8800, 5800, 99999999, 1, 'horoscope', NULL, 1),
// ('9c4f7e2d5b8a1h6i3j0k9l2m5n8p1r4t', '排盘-专业会员-年卡', '366天', 19800, 12800, 99999999, 1, 'horoscope', NULL, 1),
// ('3b6n9m2k5h8f1d4a7c0e3g6i9l2p5r8t', '排盘-专业会员-三年年卡', '1100天', 46800, 32800, 99999999, 1, 'horoscope', NULL, 1),
// ('5h8k1n4r7t0w3z6c9f2i5l8o1p4s7v0y', '排盘-专家会员-月卡', '31天', 6800, 4800, 99999999, 1, 'horoscope', '暂不开放', 0),
// ('1a4d7g0j3m6p9s2v5y8b1e4h7k0n3q6t', '排盘-专家会员-季卡', '92天', 16800, 9800, 99999999, 1, 'horoscope', '暂不开放', 0),
// ('8p1s4v7y0b3e6h9k2n5q8t1w4z7c0f3i', '排盘-专家会员-年卡', '366天', 36600, 29800, 99999999, 1, 'horoscope', '暂不开放', 0),
// ('2e5h8k1n4q7t0w3z6c9f2i5l8o1p4s7v', '排盘-专业会员-三年年卡', '1100天', 99800, 64800, 99999999, 1, 'horoscope', '暂不开放', 0),
// ('6k9n2q5t8w1z4c7f0i3l6o9r2u5x8a1d', '专项命理-论财', '份', 2880, 1680, 99999999, 2, 'horoscope', NULL, 1),
// ('4g7j0m3p6s9v2y5b8e1h4k7n0q3t6w9z', '今天做什么-月卡', '31天', 1000, 680, 99999999, 1, 'calendar', NULL, 1),
// ('9r2u5x8a1d4g7j0m3p6s9v2y5b8e1h4k', '今天做什么-季卡', '92天', 3000, 1680, 99999999, 1, 'calendar', NULL, 1),
// ('3f6i9l2o5r8u1x4a7d0g3j6m9p2s5v8y', '今天做什么-年卡', '366天', 12000, 9900, 99999999, 1, 'calendar', NULL, 1),
// ('7m0p3s6v9y2b5e8h1k4n7q0t3w6z9c2f', '万年历-年卡', '366天', 12000, 9900, 99999999, 1, 'calendar', '暂不开放', 0);

type Product struct {
	bun.BaseModel `bun:"product,alias:p" json:"-"`
	ID            int64  `bun:"id,pk" json:"id"`
	SkuCode       string `bun:"sku_code" json:"sku_code"` // SKU编码
	ProductCreate
	BaseFields4Time
}

type ProductCreate struct {
	SkuName       string `bun:"sku_name" json:"sku_name" required:"true"`            // SKU名称
	Specification string `bun:"specification" json:"specification" required:"true"`  // 规格
	OriginalPrice int    `bun:"original_price" json:"originalPrice" required:"true"` // 划线价
	SalePrice     int    `bun:"sale_price" json:"salePrice" required:"true"`         // 出售价
	Stock         int64  `bun:"stock" json:"stock" required:"true"`                  // 库存量
	Type          int64  `bun:"type" json:"type" required:"true"`                    // 商品类型：0-其他，1-虚拟会员商品，2-咨询服务
	Application   string `bun:"application" json:"application" required:"true"`      // 所属应用
	Remark        string `bun:"remark" json:"remark"`                                // 备注
	Enable        int64  `bun:"enable" json:"enable"`                                // 是否启用：0-禁用，1-启用
	ValidityDays  int64  `bun:"validity_days" json:"validityDays" required:"true"`   // 会员有效期天数
}

// CREATE TABLE `order` (
//     `id` bigint NOT NULL AUTO_INCREMENT,
//     `order_no` varchar(32) NOT NULL COMMENT '订单号',
//     `user_id` varchar(64) NOT NULL COMMENT '用户ID',
//     `product_id` bigint NOT NULL COMMENT '商品ID',
//     `product_snapshot` json DEFAULT NULL COMMENT '商品快照',
//     `quantity` int NOT NULL DEFAULT 1 COMMENT '购买数量',
//     `amount` int NOT NULL COMMENT '订单金额',
//     `pay_amount` int NOT NULL COMMENT '支付金额',
//     `pay_channel` tinyint(1) NOT NULL COMMENT '支付渠道：1-微信支付，2-支付宝',
//     `pay_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '支付状态：0-待支付，1-支付成功，2-支付失败，3-已退款',
//     `transaction_id` varchar(64) DEFAULT NULL COMMENT '支付平台交易号',
//     `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
//     `expire_time` datetime NOT NULL COMMENT '订单过期时间',
//     `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '订单状态：0-待支付，1-已完成，2-已取消，3-已退款',
//     `remark` varchar(255) DEFAULT NULL COMMENT '备注',
//     `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
//     `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
//     PRIMARY KEY (`id`),
//     UNIQUE KEY `idx_order_no` (`order_no`),
//     KEY `idx_user_id` (`user_id`),
//     KEY `idx_status` (`status`),
//     KEY `idx_pay_status` (`pay_status`)
// ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

type OrderExtraInfoLuncai struct {
	ID         int64    `json:"id"`         // 论财（排盘）记录ID
	Name       string   `json:"name"`       // 姓名
	Gender     int      `json:"gender"`     // 性别：1-男、2-女
	Birthtime  string   `json:"birthtime"`  // 出生日期：2021-01-01 00:00:00
	Birthplace []string `json:"birthplace"` // 出生地
	Bazi       []string `json:"bazi"`       // 八字
}

type OrderExtraInfoMingliBazi = OrderExtraInfoLuncai

type Order struct {
	bun.BaseModel   `bun:"order,alias:o" json:"-"`
	ID              int64           `bun:"id,pk" json:"id"`
	AppID           int64           `bun:"app_id" json:"app_id"`
	OrderNo         string          `bun:"order_no" json:"orderNo"`                 // 订单号
	UserID          string          `bun:"user_id" json:"userId"`                   // 用户ID
	ProductID       int64           `bun:"product_id" json:"productId"`             // 商品ID
	ProductSnapshot json.RawMessage `bun:"product_snapshot" json:"productSnapshot"` // 商品快照
	Quantity        int64           `bun:"quantity" json:"quantity"`                // 购买数量
	Amount          int             `bun:"amount" json:"amount"`                    // 订单金额
	PayAmount       int             `bun:"pay_amount" json:"payAmount"`             // 支付金额
	PayChannel      int8            `bun:"pay_channel" json:"payChannel"`           // 支付渠道：1-微信支付，2-支付宝
	PayStatus       int8            `bun:"pay_status" json:"payStatus"`             // 支付状态：0-待支付，1-支付成功，2-支付失败，3-已退款
	TransactionID   string          `bun:"transaction_id" json:"transactionId"`     // 支付平台交易号
	PayTime         *time.Time      `bun:"pay_time" json:"payTime"`                 // 支付时间
	ExpireTime      time.Time       `bun:"expire_time" json:"expireTime"`           // 订单过期时间
	Status          int8            `bun:"status" json:"status"`                    // 订单状态：0-待支付，1-已完成，2-已取消，3-已退款
	Remark          string          `bun:"remark" json:"remark"`
	ExtraInfo       map[string]any  `bun:"extra_info" json:"extraInfo"` // 额外信息 // 备注
	UA              string          `bun:"ua" json:"ua"`
	IP              string          `bun:"ip" json:"ip"`
	Source          int8            `bun:"source" json:"source"` // 订单来源：0-未知，1-安卓应用，2-苹果应用，3-安卓h5，4-苹果h5
	BaseFields4Time
}

// CREATE TABLE `member_duration` (
//     `id` bigint NOT NULL AUTO_INCREMENT,
//     `user_id` varchar(64) NOT NULL COMMENT '用户ID',
//     `expire_time` datetime NOT NULL COMMENT '会员过期时间',
//     `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
//     `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
//     PRIMARY KEY (`id`)
// ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员时长表';

// 会员时长

type MemberDuration struct {
	bun.BaseModel `bun:"member_duration,alias:md" json:"-"`
	ID            int64     `bun:"id,pk" json:"id"`
	UserID        string    `bun:"user_id" json:"userId"`         // 用户ID
	Role          int64     `bun:"role" json:"role"`              // 角色
	ExpireTime    time.Time `bun:"expire_time" json:"expireTime"` // 会员过期时间
	BaseFields4Time
}

// IsValid 检查会员是否有效
func (m *MemberDuration) IsValid() bool {
	return time.Now().Before(m.ExpireTime)
}

// ExtendDuration 延长会员时间
func (m *MemberDuration) ExtendDuration(seconds int) {
	if m.ExpireTime.Before(time.Now()) {
		// 如果已过期，从当前时间开始计算
		m.ExpireTime = time.Now().Add(time.Duration(seconds) * time.Second)
	} else {
		// 如果未过期，从过期时间开始累加
		m.ExpireTime = m.ExpireTime.Add(time.Duration(seconds) * time.Second)
	}
}

func (m *MemberDuration) ExtendDurationByDays(days int) {
	if m.ExpireTime.Before(time.Now()) {
		// 如果已过期，从当前时间开始计算
		m.ExpireTime = time.Now().AddDate(0, 0, days)
	} else {
		// 如果未过期，从过期时间开始累加
		m.ExpireTime = m.ExpireTime.AddDate(0, 0, days)
	}
}

// RemainingDays 获取剩余天数
func (m *MemberDuration) RemainingDays() int {
	if !m.IsValid() {
		return 0
	}
	return int(m.ExpireTime.Sub(time.Now()).Hours() / 24)
}
