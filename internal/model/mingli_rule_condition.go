package model

import (
	"github.com/uptrace/bun"
	"time"
)

/*
create table mingli_rule_condition
(

	id             bigint auto_increment comment '主键ID'
	    primary key,
	mingli_rule_id bigint                                 not null comment '命理规则ID',
	no   		   varchar(255)                           not null comment '条件编号',
	name           varchar(255)                           not null comment '条件名称',
	category       tinyint                                null comment '条件大类：2-坐对、3-喜忌、1-全选',
	type           bigint                                 not null comment '条件类型',
	criterion      varchar(1000)                          not null comment '判断依据',
	gender         tinyint  default 0                     not null comment '日主性别：1-不限，2-男，3-女',
	weizhi_zuo     json                                   not null comment '位置坐：1-年、2-月、3-日、4-时、5-大运、6-流年、7-流月、8-流日、9-流时',
	weizhi_dui     json                                   not null comment '位置对：1-年、2-月、3-日、4-时、5-大运、6-流年、7-流月、8-流日、9-流时',
	zu<PERSON><PERSON>         json                                   null comment '坐对',
	xiji           json                                   null comment '喜忌',
	created_at     datetime default CURRENT_TIMESTAMP     not null,
	updated_at     datetime default CURRENT_TIMESTAMP     not null,
	deleted_at     datetime default '0001-01-01 00:00:00' not null,
	unique index idx_rule_id_condition_no_deleted_at (mingli_rule_id, condition_no, deleted_at)

) engine = InnoDB default charset = utf8mb4 collate = utf8mb4_general_ci comment '命理规则条件';
*/
type (
	// MingliRuleCondition 命理规则条件
	MingliRuleCondition struct {
		bun.BaseModel `bun:"mingli_rule_condition,alias:mrc"`
		ID            int64                    `bun:"id,pk,autoincrement"`                                                                   // 主键ID
		MingliRuleID  int64                    `bun:",notnull"`                                                                              // 命理规则ID
		No            string                   `bun:",notnull,unique:idx_rule_id_condition_no_deleted_at"`                                   // 条件编号
		Name          string                   `bun:",notnull"`                                                                              // 条件名称
		Category      int                      `bun:",notnull"`                                                                              // 类别：2-坐对、3-喜忌、1-全选
		Type          int64                    `bun:",notnull"`                                                                              // 条件类型（坐-对）
		Criterion     string                   `bun:",notnull"`                                                                              // 判断依据说明
		Gender        int                      `bun:",notnull"`                                                                              // 日主性别（2-男、3-女、1-无关性别）
		WeizhiZuo     []int                    `bun:",notnull"`                                                                              // 位置坐（1-年、2-月、3-日、4-时、5-大运、6-流年、7-流月、8-流日、9-流时）
		WeizhiDui     []int                    `bun:",notnull"`                                                                              // 位置对（1-年、2-月、3-日、4-时、5-大运、6-流年、7-流月、8-流日、9-流时）
		Zuodui        []*MingliRuleConditionKV `bun:","`                                                                                     // 坐对
		Xiji          []*MingliRuleConditionKV `bun:","`                                                                                     // 喜忌
		DeletedAt     time.Time                `bun:",soft_delete,unique:idx_rule_id_condition_no_deleted_at,default:'0001-01-01 00:00:00'"` // 删除时间
		BaseFields4Time
	}

	MingliRuleConditionValue struct {
		Type  int   `json:"type" example:"1"`  // 类型：1-支、2-干、3-干支、4-十神、5-五行
		Value int64 `json:"value" example:"1"` // 值
	}
	MingliRuleConditionKV struct {
		Key     int64                      `json:"key" example:"1"`
		Values  []MingliRuleConditionValue `json:"values"`
		Checked bool                       `json:"checked" example:"true"` // 是否选中
	}
)
