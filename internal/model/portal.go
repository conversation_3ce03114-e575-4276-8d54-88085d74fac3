package model

import (
	"github.com/uptrace/bun"
	"time"
)

/*
create table `portal_website` (
    id bigint unsigned auto_increment comment '主键ID',
    name varchar(255) default '' not null comment '网站名称',
    domain varchar(255) default '' not null comment '网站域名',
    remark varchar(255) default '' not null comment '备注',
    created_at datetime default CURRENT_TIMESTAMP not null ,
    updated_at datetime default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP not null,
    deleted_at datetime default '0001-01-01 00:00:00' null,
    primary key (id),
    unique index idx_domain_deleted_at (domain, deleted_at)
)   ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci comment '门户网站';

create table `portal_section` (
    id bigint unsigned auto_increment comment '主键ID',
    website_id bigint unsigned not null comment '网站ID',
    name varchar(255) default '' not null comment '栏目名称',
    code varchar(255) default '' not null comment '栏目编码',
    remark varchar(255) default '' not null comment '备注',
    created_at datetime default CURRENT_TIMESTAMP not null ,
    updated_at datetime default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP not null,
    deleted_at datetime default '0001-01-01 00:00:00' null,
    primary key (id),
    unique index idx_website_id_code_deleted_at (website_id, code, deleted_at)
)  ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci comment '门户栏目';

create table `portal_article` (
    id bigint unsigned auto_increment comment '主键ID',
    section_id bigint unsigned not null comment '栏目ID',
    title varchar(255) default '' not null comment '文章标题',
    author varchar(255) default '' not null comment '作者',
    source varchar(255) default '' not null comment '来源',
    content longtext not null comment '文章内容',
    remark varchar(255) default '' not null comment '备注',
    created_at datetime default CURRENT_TIMESTAMP not null ,
    updated_at datetime default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP not null,
    deleted_at datetime default '0001-01-01 00:00:00' null,
    primary key (id),
    unique index idx_section_id_deleted_at (section_id, deleted_at)
)  ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci comment '门户文章';
*/

type PortalSection struct {
	bun.BaseModel `bun:"portal_section,alias:ps"`
	ID            int64     `bun:"id,pk"`     // 栏目ID
	Name          string    `bun:"name"`      // 栏目名称
	Remark        string    `bun:"remark"`    // 栏目备注
	IsEnable      bool      `bun:"is_enable"` // 是否启用
	DeletedAt     time.Time `bun:",soft_delete,unique:idx_name_deleted_at,default:'0001-01-01 00:00:00'"`
	BaseFields4Time
}

type PortalArticle struct {
	bun.BaseModel `bun:"portal_article,alias:pa"`
	ID            int64      `bun:"id,pk"`
	SectionID     int64      `bun:"section_id"`
	Title         string     `bun:"title"`
	Author        string     `bun:"author"`
	Tags          []string   `bun:"tags"`
	IsTop         bool       `bun:"is_top"`
	TopTime       *time.Time `bun:"top_time"`
	IsPublish     bool       `bun:"is_publish"`
	PublishTime   *time.Time `bun:"publish_time"`
	IsModify      bool       `bun:"is_modify"`
	ModifyTime    *time.Time `bun:"modify_time"`
	Content       string     `bun:"content"`
	Remark        string     `bun:"remark"`
	DeletedAt     time.Time  `bun:",soft_delete,default:'0001-01-01 00:00:00'"`
	BaseFields4Time
}

type PortalTag struct {
	bun.BaseModel `bun:"portal_tag,alias:pt"`
	ID            int64  `bun:"id,pk"`
	Name          string `bun:"name"`
	BaseFields4Time
}
