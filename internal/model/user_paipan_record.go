package model

import (
	"github.com/uptrace/bun"
	"time"
)

type PaipanRecord struct {
	bun.BaseModel  `bun:"table:user_paipan_record,alias:upr"`
	ID             int64     `bun:"id,pk,autoincrement"`
	UserID         string    `bun:"user_id"`         // 用户ID：已登录用户不为空
	Name           string    `bun:"name"`            // 排盘姓名
	Gender         int       `bun:"gender"`          // 排盘性别：1-男、2-女
	Birthtime      time.Time `bun:"birthtime"`       // 公历出生时间
	BirthtimeSun   time.Time `bun:"birthtime_sun"`   // 真太阳时
	BirthtimeLunar string    `bun:"birthtime_lunar"` // 农历出生时间
	Bazi           []string  `bun:"bazi"`            // 八字
	Birthplace     []string  `bun:"birthplace"`      // 出生地
	Type           int       `bun:"type"`            // 固定值：1-即时起盘
	SaveTime       time.Time `bun:"save_time"`       // 保存时间
	UserAgent      string    `bun:"user_agent"`      // 用户代理
	IP             string    `bun:"ip"`              // IP
	Region         string    `bun:"region"`          // 地区
	AppID          int64     `bun:"app_id"`          // 应用：2-排盘、3-万年历、4-运势、5-论财、6-在线投放、7-高考专题
	AppPlatformID  int64     `bun:"app_platform_id"` // 平台：1-未知（默认值）
	Scene          string    `bun:"scene"`           // 场景
	ExtraInfo      any       `bun:"extra_info"`      // 额外信息
	BaseFields4Time
	DeletedAt time.Time `bun:",soft_delete,default:'0001-01-01 00:00:00'"` // 删除时间
}
