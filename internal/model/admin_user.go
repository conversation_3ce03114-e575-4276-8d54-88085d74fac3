package model

import "github.com/uptrace/bun"

type AdminUser struct {
	bun.BaseModel `bun:"table:admin_user,alias:au"`
	ID            int64  `bun:"id,pk,autoincrement"`
	UserID        string `bun:"user_id,unique:idx_user_id_deleted_at"`
	Username      string `bun:",notnull,unique:idx_username_deleted_at"`
	Password      string `bun:",notnull"`
	DeletedAt     string `bun:",soft_delete,unique:idx_username_deleted_at,default:'0001-01-01 00:00:00'"`
	BaseFields4Time
}

/*
create table `admin_user` (
    `id` int(11) not null auto_increment,
	`user_id` varchar(50) not null unique,
    `username` varchar(50) not null,
    `password` varchar(50) not null,
    `deleted_at` DATETIME NOT NULL DEFAULT '0001-01-01 00:00:00', -- 软删除标记
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,      -- 创建时间
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间
    primary key (`id`),
    unique key idx_user_id_deleted_at (`user_id`, `deleted_at`),
    unique key idx_username_deleted_at (`username`, `deleted_at`)
) engine=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
*/
