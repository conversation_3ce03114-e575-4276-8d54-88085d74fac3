package model

import "github.com/uptrace/bun"

/*
create table `yizhu_lunmin` (
	`id` int(11) not null auto_increment,
	`ganzhi` varchar(50) not null,
	`lunmin` text not null comment '论命',
	`deleted_at` DATETIME NOT NULL DEFAULT '0001-01-01 00:00:00', -- 软删除标记
	`created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,      -- 创建时间
	`updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间
	primary key (`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_general_ci;
*/

type YizhuLunmin struct {
	bun.BaseModel `bun:"table:yizhu_lunmin,alias:yl"`
	ID            int64  `bun:"id,pk,autoincrement"`
	R<PERSON>hu         string `bun:"rizhu"`
	Lun<PERSON>        string `bun:"lunmin"`
}
