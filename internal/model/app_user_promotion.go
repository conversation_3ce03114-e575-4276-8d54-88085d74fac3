package model

import "github.com/uptrace/bun"

/*
create table `app_user_promotion` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `user_id` varchar(36) NOT NULL COMMENT '用户ID',
    `promotion_code` varchar(255) NOT NULL COMMENT '推广码（同邀请码）',
    `level1_ratio` int NOT NULL DEFAULT 0 COMMENT '一级推广分成比例',
    `level2_ratio` int NOT NULL DEFAULT 0 COMMENT '二级推广分成比例',
    `rebate_withdrawn` int NOT NULL DEFAULT 0 COMMENT '已提现的返利金额（分）',
    `remark` varchar(50) DEFAULT NULL COMMENT '备注',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_user_promotion` (`user_id`, `promotion_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户推广';
*/

type AppUserPromotion struct {
	bun.BaseModel   `bun:"table:app_user_promotion,alias:aup"`
	ID              int64  `bun:"id,pk,autoincrement"`
	UserID          string `bun:"user_id,notnull,unique:unique_user_promotion"`
	PromotionCode   string `bun:"promotion_code,notnull,unique:unique_user_promotion"`
	Level1Ratio     int    `bun:"level1_ratio,notnull"`
	Level2Ratio     int    `bun:"level2_ratio,notnull"`
	RebateWithdrawn int    `bun:"rebate_withdrawn,notnull"`
	Remark          string `bun:"remark"`
	BaseFields4Time
}
