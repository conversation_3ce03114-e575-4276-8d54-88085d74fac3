package model

import (
	"encoding/json"
	"github.com/uptrace/bun"
	"strconv"
	"strings"
)

type Shensha2 struct {
	bun.BaseModel `bun:"shensha_two,alias:st"`
	ID            int64             `bun:"id,pk"`
	ShenshaoneID  int64             `bun:"shenshaone_id"`
	Name          string            `bun:"name"`
	ZhongleiID    int64             `bun:"zhonglei_id"`
	IsMapMultiple bool              `bun:"single_multiple"` // true-一对一，false-一对多
	Zuo           string            `bun:"zuo"`             // 逗号分割的数字，如：1,2,3，可能为空
	ZuoList       []int             `bun:"-"`
	Dui           string            `bun:"dui"` // 逗号分割的数字，如：1,2,3，可能为空
	DuiList       []int             `bun:"-"`
	ZuoTwo        string            `bun:"zuo_two"`  // 逗号分割的数字，如：1,2,3
	DuiTwo        string            `bun:"dui_two"`  // 逗号分割的数字，如：1,2,3
	Sex           int               `bun:"sex"`      // 0-通用，1-男，2-女
	DataJson      string            `bun:"datajson"` // json字符串
	DataMap       map[string]string `bun:"-"`
	IsSetup       bool              `bun:"-"`
}

func (slf *Shensha2) Setup() {
	if slf.IsSetup {
		return
	}
	splitN := strings.Split(slf.Zuo, ",")
	for _, str := range splitN {
		i, _ := strconv.Atoi(str)
		slf.ZuoList = append(slf.ZuoList, i)
	}
	splitN = strings.Split(slf.Dui, ",")
	for _, str := range splitN {
		i, _ := strconv.Atoi(str)
		slf.DuiList = append(slf.DuiList, i)
	}
	slf.DataMap = make(map[string]string)
	_ = json.Unmarshal([]byte(slf.DataJson), &slf.DataMap)
	slf.IsSetup = true
}
