package model

import "github.com/uptrace/bun"

/*
create table `adflow_qw_link` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `add_state` varchar(255) not null,
    `user_ids` json not null ,
    `qr_code` varchar(255) default null,
    `config_id` varchar(255) default null,
    `paipan_id` int(11) NOT NULL,
    `phone` varchar(255) default null,
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `add_state` (`add_state`)
) ENGINE=InnoDB CHARSET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '在线投放企微链接表';
*/

type AdflowQwLink struct {
	bun.BaseModel `bun:"adflow_qw_link,alias:aql"`
	ID            int64    `bun:"id,pk" json:"id"`
	AddState      string   `bun:"add_state" json:"add_state"`
	UserIds       []string `bun:"user_ids" json:"user_ids"`
	QrCode        string   `bun:"qr_code" json:"qr_code"`
	ConfigId      string   `bun:"config_id" json:"config_id"`
	PaipanId      int64    `bun:"paipan_id" json:"paipan_id"`
	Phone         string   `bun:"phone" json:"phone"`
	BaseFields4Time
}

/*
create table `adflow_qw_user` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_ids` json not null ,
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
)  ENGINE=InnoDB CHARSET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '在线投放企微客服表';
*/

type AdflowQwUser struct {
	bun.BaseModel `bun:"adflow_qw_user,alias:aqw"`
	ID            int64    `bun:"id,pk" json:"id"`
	UserIds       []string `bun:"user_ids" json:"user_ids"`
	BaseFields4Time
}
