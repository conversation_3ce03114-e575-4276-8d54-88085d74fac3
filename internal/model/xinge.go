package model

type RizhuXinxing struct {
	Rigan        string // 日干
	RiganRizhu   string // 日干日主
	RizhuXinxing string // 日主心性
	Mongpie      string // 盲派十干断性格
}

type YuezhiShishenXinge struct {
	Shishen string // 十神
	Xinxing string // 心性
	Youdot  string // 优点
	Quedot  string // 缺点
}

type Yuezhu<PERSON><PERSON>yun struct {
	Xingyun  string // 星运
	Shuoming string // 星运说明
}

type ShishenYongji struct {
	Shishen  string // 十神
	XYCJ     string // 喜用仇忌
	Shuoming string // 说明
}

type RiganWaixiang struct {
	Rigan    string
	Waixiang string
}

type WuxingWangxiang struct {
	Wuxing          string
	Wangshuai       string
	WuxingWangshuai string
	Shuoming        string
}
