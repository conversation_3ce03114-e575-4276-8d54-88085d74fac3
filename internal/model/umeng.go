package model

import "time"

/*
crate table `umeng_event_sls` (
	`id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
	event_md5 varchar(255) not null unique comment '事件md5',
	event_time datetime not null comment '事件时间',
	event_json json not null comment '事件json',
	sls_project varchar(255) not null comment 'sls项目',
	sls_logstore varchar(255) not null comment 'sls日志库',
	sls_shared_id int not null comment 'sls shard id',
	sls_cursor varchar(255) not null comment 'sls cursor',
	sls_next_cursor varchar(255) not null comment 'sls next cursor',
	processed_at datetime comment '处理时间',
	created_at datetime default CURRENT_TIMESTAMP not null,
	updated_at datetime default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP not null,
	primary key (id),
	unique key idx_event_md5 (event_md5)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='umeng event logs';
*/

type UmengEventSls struct {
	ID            int64       `bun:"id,pk,autoincrement"`
	EventMD5      string      `bun:"event_md5,unique"`
	EventTime     time.Time   `bun:"event_time"`
	EventJson     *UmengEvent `bun:"event_json"`
	SlsProject    string      `bun:"sls_project"`
	SlsLogstore   string      `bun:"sls_logstore"`
	SlsSharedID   int         `bun:"sls_shared_id"`
	SlsCursor     string      `bun:"sls_cursor"`
	SlsNextCursor string      `bun:"sls_next_cursor"`
	ProcessedAt   time.Time   `bun:"processed_at"`
	BaseFields4Time
}

/*
android_id
app_channel
app_key
app_version
city
cli_timestamp
country
device_brand
device_model
event_kv_json
event_name
event_type
health
idfa
idfv
install_app_version
install_channel
install_datetime
ip
network_access
oaid
oid
os
os_version
pre_app_version
province
screen_height
screen_width
sdk_version
session_id
svr_timestamp
umid
*/
type UmengEvent struct {
	AndroidID         string         `bun:"android_id" json:"android_id"`
	AppChannel        string         `bun:"app_channel" json:"app_channel"`
	AppKey            string         `bun:"app_key" json:"app_key"`
	AppVersion        string         `bun:"app_version" json:"app_version"`
	City              string         `bun:"city" json:"city"`
	CliTimestamp      string         `bun:"cli_timestamp" json:"cli_timestamp"`
	Country           string         `bun:"country" json:"country"`
	DeviceBrand       string         `bun:"device_brand" json:"device_brand"`
	DeviceModel       string         `bun:"device_model" json:"device_model"`
	EventKVJson       map[string]any `bun:"event_kv_json" json:"event_kv_json"`
	EventName         string         `bun:"event_name" json:"event_name"`
	EventType         string         `bun:"event_type" json:"event_type"`
	Health            string         `bun:"health" json:"health"`
	IDFA              string         `bun:"idfa" json:"idfa"`
	IDFV              string         `bun:"idfv" json:"idfv"`
	InstallAppVersion string         `bun:"install_app_version" json:"install_app_version"`
	InstallChannel    string         `bun:"install_channel" json:"install_channel"`
	InstallDatetime   string         `bun:"install_datetime" json:"install_datetime"`
	IP                string         `bun:"ip" json:"ip"`
	NetworkAccess     string         `bun:"network_access" json:"network_access"`
	OAID              string         `bun:"oaid" json:"oaid"`
	OID               string         `bun:"oid" json:"oid"`
	OS                string         `bun:"os" json:"os"`
	OSVersion         string         `bun:"os_version" json:"os_version"`
	PreAppVersion     string         `bun:"pre_app_version" json:"pre_app_version"`
	Province          string         `bun:"province" json:"province"`
	ScreenHeight      string         `bun:"screen_height" json:"screen_height"`
	ScreenWidth       string         `bun:"screen_width" json:"screen_width"`
	SDKVersion        string         `bun:"sdk_version" json:"sdk_version"`
	SessionID         string         `bun:"session_id" json:"session_id"`
	SvrTimestamp      string         `bun:"svr_timestamp" json:"svr_timestamp"`
	UMID              string         `bun:"umid" json:"umid"`
}
