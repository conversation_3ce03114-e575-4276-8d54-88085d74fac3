package model

import "github.com/uptrace/bun"

type App struct {
	bun.BaseModel `bun:"table:app,alias:app"`
	ID            int64  `bun:"id,pk,autoincrement"`
	Name          string `bun:",notnull"`
	Code          string `bun:",notnull"`
	IsSelf        bool   `bun:",notnull,default:true"`
	Org           string `bun:",notnull"`
	App           string `bun:",notnull"`
	AppID         string `bun:",notnull"`
	ClientID      string `bun:",notnull"`
	Role          int64  `bun:",notnull"`
	WxMpAppID     string `bun:",notnull"`
	DeletedAt     string `bun:",soft_delete,unique:idx_code_deleted_at,default:'0001-01-01 00:00:00'"`
	BaseFields4Time
}

/*
create table `app_channel` (
    id bigint unsigned auto_increment comment '主键ID',
    name varchar(255) default '' not null comment '渠道名称',
    remark varchar(255) default '' not null comment '备注',
    code varchar(255) default '' not null comment '渠道编码（自动生成）',
    created_at datetime default CURRENT_TIMESTAMP not null,
    updated_at datetime default CURRENT_TIMESTAMP not null,
    deleted_at datetime default '0001-01-01 00:00:00' null,
    primary key (id),
    unique index idx_name_deleted_at (name, deleted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci comment '应用渠道';
*/

type AppChannel struct {
	bun.BaseModel `bun:"table:app_channel,alias:ac"`
	ID            int64  `bun:"id,pk,autoincrement"`
	Name          string `bun:",notnull"`
	Remark        string `bun:",notnull"`
	Code          string `bun:",notnull"`
	DeletedAt     string `bun:",soft_delete,unique:idx_code_deleted_at,default:'0001-01-01 00:00:00'"`
	BaseFields4Time
}

/*
create table `app_version`(

	id bigint unsigned auto_increment comment '主键ID',
	app_id bigint unsigned not null comment '应用ID',
	version_code int not null comment '版本号',
	version_name varchar(255) default '' not null comment '版本名称',
	update_note tinytext comment '更新说明',
	os_type int not null comment '1:android, 2:ios',
	url varchar(512) default '' not null comment '下载地址',
	is_force_update tinyint default 0 not null comment '是否强制更新',
	remark varchar(255) default '' not null comment '备注',
	status tinyint default 0 not null comment '状态：0-未发布、1-已发布、2-已撤回',
	created_at datetime default CURRENT_TIMESTAMP not null,
	updated_at datetime default CURRENT_TIMESTAMP not null,
	deleted_at datetime default '0001-01-01 00:00:00' null,
	primary key (id),
	unique index idx_appid_versioncode_deleted_at (app_id, version_code, deleted_at)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci comment '应用版本';
*/

type AppVersion struct {
	bun.BaseModel `bun:"table:app_version,alias:av"`
	ID            int64  `bun:"id,pk,autoincrement"`
	AppID         int64  `bun:",notnull"` // 应用ID
	VersionCode   int    `bun:",notnull"` // 版本号
	VersionName   string `bun:",notnull"` // 版本名称
	UpdateNote    string `bun:",notnull"` // 更新说明
	OsType        int    `bun:",notnull"` // 1:android, 2:ios
	Url           string `bun:",notnull"` // 下载地址
	IsForceUpdate bool   `bun:",notnull"` // 是否强制更新
	IsHotUpdate   bool   `bun:",notnull"` // 是否热更新
	Remark        string `bun:",notnull"` // 备注
	Status        int    `bun:",notnull"` // 状态：0-未发布、1-已发布、2-已撤回
	DeletedAt     string `bun:",soft_delete,unique:idx_appid_versioncode_deleted_at,default:'0001-01-01 00:00:00'"`
	BaseFields4Time
}
