package model

import (
	"github.com/uptrace/bun"
	"time"
)

// UserMingliGroup 用户命例分组
type UserMingliGroup struct {
	bun.BaseModel `bun:"table:user_mingli_group,alias:umg"`
	ID            int64     `bun:"id,pk,autoincrement"`    // ID
	UserID        string    `bun:",notnull"`               // 用户ID
	AppID         int64     `bun:",notnull"`               // 应用：2-排盘、3-万年历、4-运势、5-论财
	Name          string    `bun:",notnull"`               // 分组名称
	IsDefault     bool      `bun:",notnull,default:false"` // 是否默认分组
	DeletedAt     time.Time `bun:",soft_delete,default:'0001-01-01 00:00:00'"`
	BaseFields4Time
}
