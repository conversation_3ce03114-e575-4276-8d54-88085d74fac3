package model

import (
	"context"
	"github.com/uptrace/bun"
	"strings"
	"time"
)

type BaseFields4Time struct {
	CreatedAt time.Time `bun:",nullzero,notnull,default:current_timestamp"` // 创建时间
	UpdatedAt time.Time `bun:",nullzero,notnull,default:current_timestamp"` // 更新时间
}

func (slf *BaseFields4Time) BeforeAppendModel(_ context.Context, query bun.Query) error {
	switch q := query.(type) {
	case *bun.InsertQuery:
		slf.CreatedAt = time.Now()
		slf.UpdatedAt = time.Now()
	case *bun.UpdateQuery:
		slf.UpdatedAt = time.Now()
		if !strings.Contains(q.String(), "updated_at") {
			q.Set("updated_at = ?", time.Now())
		}
	}
	return nil
}
