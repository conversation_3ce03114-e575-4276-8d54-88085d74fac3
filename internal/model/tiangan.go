package model

import "github.com/uptrace/bun"

type Tiangan struct {
	bun.BaseModel `bun:"table:tiangan,alias:tg"`
	ID            int64  `bun:"id,pk,autoincrement"`
	Tiangan       string `bun:"tiangan"`
	<PERSON><PERSON>       string `bun:"yinyang"`
	Wuxing        string `bun:"wuxing"`
}

type TianganWuhe struct {
	bun.BaseModel `bun:"table:tiangan_wuhe,alias:tgwh"`
	ID            int64  `bun:"id,pk,autoincrement"`
	Tiangan1      string `bun:"tiangan"`
	Tiangan2      string `bun:"tiangan_two"`
	Hehua         string `bun:"hehua"`
}
