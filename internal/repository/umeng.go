package repository

import (
	"context"
	"zodiacus/internal/model"
)

type UmengRepository interface {
	FetchUmengEventSlsByEventMD5(ctx context.Context, eventMD5 string) (*model.UmengEventSls, error)
	CreateUmengEventSls(ctx context.Context, event *model.UmengEventSls) (int64, error)
	GetNextCursorByShardID(ctx context.Context, project, logstore string, shardID int) (string, error)
}

func NewUmengRepository(
	repo *Repository,
) UmengRepository {
	return &umengRepository{
		Repository: repo,
	}
}

type umengRepository struct {
	*Repository
}

func (slf *umengRepository) FetchUmengEventSlsByEventMD5(ctx context.Context, eventMD5 string) (*model.UmengEventSls, error) {
	var eventSls model.UmengEventSls
	if err := slf.DB(ctx).NewSelect().Model(&eventSls).
		Where("event_md5 = ?", eventMD5).Scan(ctx); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &eventSls, nil
}

func (slf *umengRepository) GetNextCursorByShardID(ctx context.Context, project, logstore string, shardID int) (string, error) {
	var cursor string
	if err := slf.DB(ctx).NewSelect().Model((*model.UmengEventSls)(nil)).
		ColumnExpr("sls_next_cursor").
		Where("sls_project = ?", project).
		Where("sls_logstore = ?", logstore).
		Where("sls_shared_id = ?", shardID).
		Order("id desc").
		Limit(1).
		Scan(ctx, &cursor); err != nil {
		if slf.NotFound(err) {
			return "", nil
		}
		return "", err
	}
	return cursor, nil
}

func (slf *umengRepository) CreateUmengEventSls(ctx context.Context, event *model.UmengEventSls) (int64, error) {
	exec, err := slf.DB(ctx).NewInsert().Model(event).Exec(ctx)
	if err != nil {
		return 0, err
	}
	return exec.LastInsertId()
}
