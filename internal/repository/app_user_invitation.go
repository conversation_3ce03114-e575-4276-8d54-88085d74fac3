package repository

import (
	"context"
	"database/sql"
	"errors"
	"github.com/uptrace/bun"
	"go.uber.org/zap"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
)

type AppUserInvitationRepository interface {
	FetchInviteTree2Level(ctx context.Context, userID string) ([]*model.UserInviteTree2Level, error)
	FetchInviteTree2LevelWithRecharge(ctx context.Context, userID string) ([]*model.UserInviteTree2LevelWithRecharge, error)
	UpdateInviteCode(ctx context.Context, code *model.InviteCode) error
	FetchInviteLevel(ctx context.Context, level int) (*model.InviteLevel, error)
	MarkInviteRecordsAsRead4Inviter(ctx context.Context, userID string) error
	MarkInviteRecordsAsRead4Invitee(ctx context.Context, userID string) error
	FetchUnreadInviteRecords(ctx context.Context, userID string) ([]*model.InviteRecord, error)
	UpdateInviteBindingIP(ctx context.Context, binding *model.InviteBindingIP) error
	FetchInviteBindingIPByIP(ctx context.Context, ip string) (*model.InviteBindingIP, error)
	FetchInviteBindingIP(ctx context.Context, inviteCode string, ip string) (*model.InviteBindingIP, error)
	CreateInviteBindingIP(ctx context.Context, binding *model.InviteBindingIP) error
	CreateInviteCode(ctx context.Context, code *model.InviteCode) (bool, error)
	GetInviteCodeByUID(ctx context.Context, uid string) (*model.InviteCode, error)
	GetInviteCode(ctx context.Context, code string) (*model.InviteCode, error)
	CreateInviteRecord(ctx context.Context, record *model.InviteRecord) error
	UpdateInviteRecord(ctx context.Context, record *model.InviteRecord) error
	GetInviteRecordByInvitee(ctx context.Context, uid string) (*model.InviteRecord, error)
	GetInviteRecordsByInviter(ctx context.Context, param *v1.GetInviteReferralsRequest) (*v1.GetInviteReferralsResponseData, error)
	GetAppVipDuration(ctx context.Context, uid string, role int64) (*model.MemberDuration, error)
	CreateAppVipDuration(ctx context.Context, duration *model.MemberDuration) error
	UpdateAppVipDuration(ctx context.Context, duration *model.MemberDuration) error
}

func NewAppUserInvitationRepository(repo *Repository) AppUserInvitationRepository {
	return &appUserInvitationRepository{
		Repository: repo,
	}
}

type appUserInvitationRepository struct {
	*Repository
}

func (slf *appUserInvitationRepository) UpdateInviteRecord(ctx context.Context, record *model.InviteRecord) error {
	_, err := slf.DB(ctx).NewUpdate().Model(record).WherePK().Exec(ctx)
	return err
}

func (slf *appUserInvitationRepository) FetchInviteTree2LevelWithRecharge(ctx context.Context, userID string) ([]*model.UserInviteTree2LevelWithRecharge, error) {
	var list []*model.UserInviteTree2LevelWithRecharge
	if err := slf.DB(ctx).NewSelect().Model(&model.UserInviteTree2Level{}).
		ColumnExpr("uitl.root_user_id").
		ColumnExpr("uitl.inviter").
		ColumnExpr("uitl.invitee").
		ColumnExpr("uitl.level").
		ColumnExpr("uitl.invite_time").
		ColumnExpr("IFNULL(SUM(auo.pay_amount), 0) as recharge_amount").
		Join("left join `order` auo ON auo.user_id = uitl.invitee AND auo.pay_status = 1 AND auo.pay_channel != 4").
		Group("uitl.root_user_id", "uitl.inviter", "uitl.invitee", "uitl.level", "uitl.invite_time").
		Where("uitl.root_user_id = ?", userID).
		Order("uitl.invite_time desc").
		Scan(ctx, &list); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}

func (slf *appUserInvitationRepository) FetchInviteTree2Level(ctx context.Context, userID string) ([]*model.UserInviteTree2Level, error) {
	var list []*model.UserInviteTree2Level
	if err := slf.DB(ctx).NewSelect().Model(&list).
		Where("root_user_id = ?", userID).
		Order("invite_time desc").
		Scan(ctx); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}

func (slf *appUserInvitationRepository) UpdateInviteCode(ctx context.Context, code *model.InviteCode) error {
	_, err := slf.DB(ctx).NewUpdate().Model(code).WherePK().Exec(ctx)
	return err
}

func (slf *appUserInvitationRepository) FetchInviteLevel(ctx context.Context, level int) (*model.InviteLevel, error) {
	var inviteLevel model.InviteLevel
	if err := slf.DB(ctx).NewSelect().Model(&inviteLevel).Where("level = ?", level).Scan(ctx); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &inviteLevel, nil
}

func (slf *appUserInvitationRepository) MarkInviteRecordsAsRead4Invitee(ctx context.Context, userID string) error {
	if _, err := slf.DB(ctx).NewUpdate().Model(&model.InviteRecord{}).
		Where("invitee_id = ?", userID).
		Where("invitee_read = false").
		Set("invitee_read = ?", true).
		Exec(ctx); err != nil {
		return err
	}
	return nil
}

func (slf *appUserInvitationRepository) MarkInviteRecordsAsRead4Inviter(ctx context.Context, userID string) error {
	if _, err := slf.DB(ctx).NewUpdate().Model(&model.InviteRecord{}).
		Where("inviter_id = ?", userID).
		Where("inviter_read = false").
		Set("inviter_read = ?", true).
		Exec(ctx); err != nil {
		return err
	}
	return nil
}

func (slf *appUserInvitationRepository) FetchUnreadInviteRecords(ctx context.Context, userID string) ([]*model.InviteRecord, error) {
	var list []*model.InviteRecord
	if err := slf.DB(ctx).NewSelect().Model(&list).
		WhereGroup(" AND ", func(q *bun.SelectQuery) *bun.SelectQuery {
			return q.Where("inviter_id = ? AND inviter_read = false", userID).
				WhereOr("invitee_id = ? AND invitee_read = false", userID)
		}).
		Order("invite_time asc").
		Scan(ctx); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}

func (slf *appUserInvitationRepository) UpdateInviteBindingIP(ctx context.Context, binding *model.InviteBindingIP) error {
	if _, err := slf.DB(ctx).NewUpdate().Model(binding).WherePK().Exec(ctx); err != nil {
		return err
	}
	return nil
}

func (slf *appUserInvitationRepository) FetchInviteBindingIPByIP(ctx context.Context, ip string) (*model.InviteBindingIP, error) {
	var binding model.InviteBindingIP
	if err := slf.DB(ctx).NewSelect().Model(&binding).
		Where("ip = ?", ip).
		Where("expire_time > now()").
		Order("expire_time desc").
		Limit(1).
		Scan(ctx); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &binding, nil
}

func (slf *appUserInvitationRepository) FetchInviteBindingIP(ctx context.Context, inviteCode string, ip string) (*model.InviteBindingIP, error) {
	var binding model.InviteBindingIP
	if err := slf.DB(ctx).NewSelect().Model(&binding).
		Where("invite_code = ?", inviteCode).Where("ip = ?", ip).
		Where("expire_time > now()").
		Order("expire_time desc").
		Limit(1).Scan(ctx); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &binding, nil
}

func (slf *appUserInvitationRepository) CreateInviteBindingIP(ctx context.Context, binding *model.InviteBindingIP) error {
	if _, err := slf.DB(ctx).NewInsert().Model(binding).Exec(ctx); err != nil {
		return err
	}
	return nil
}

func (slf *appUserInvitationRepository) UpdateAppVipDuration(ctx context.Context, duration *model.MemberDuration) error {
	if _, err := slf.DB(ctx).NewUpdate().Model(duration).WherePK().Exec(ctx); err != nil {
		return err
	}
	return nil
}

func (slf *appUserInvitationRepository) GetAppVipDuration(ctx context.Context, uid string, role int64) (*model.MemberDuration, error) {
	var (
		duration model.MemberDuration
	)
	if err := slf.DB(ctx).NewSelect().Model(&duration).
		Where("user_id = ?", uid).
		Where("role = ?", role).Limit(1).
		Scan(ctx); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &duration, nil
}

func (slf *appUserInvitationRepository) CreateAppVipDuration(ctx context.Context, duration *model.MemberDuration) error {
	if _, err := slf.DB(ctx).NewInsert().Model(duration).Exec(ctx); err != nil {
		return err
	}
	return nil
}

func (slf *appUserInvitationRepository) GetInviteCode(ctx context.Context, code string) (*model.InviteCode, error) {
	var inviteCode model.InviteCode
	if err := slf.DB(ctx).NewSelect().Model(&inviteCode).Where("code = ?", code).Scan(ctx); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &inviteCode, nil
}

// CreateInviteCode 创建邀请码
func (slf *appUserInvitationRepository) CreateInviteCode(ctx context.Context, code *model.InviteCode) (bool, error) {
	if _, err := slf.DB(ctx).NewInsert().Model(code).Exec(ctx); err != nil {
		if slf.IsDuplicateEntry(err) {
			slf.logger.WithContext(ctx).Error("appUserInvitationRepository.CreateInviteCode: Duplicate entry", zap.Error(err))
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// GetInviteCodeByUID 获取邀请码
func (slf *appUserInvitationRepository) GetInviteCodeByUID(ctx context.Context, userID string) (*model.InviteCode, error) {
	var code model.InviteCode
	if err := slf.DB(ctx).NewSelect().Model(&code).Where("user_id = ?", userID).Scan(ctx); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &code, nil
}

// CreateInviteRecord 创建邀请记录
func (slf *appUserInvitationRepository) CreateInviteRecord(ctx context.Context, record *model.InviteRecord) error {
	if _, err := slf.DB(ctx).NewInsert().Model(record).Exec(ctx); err != nil {
		return err
	}
	return nil
}

// GetInviteRecordByInvitee 通过被邀请者查询邀请记录
func (slf *appUserInvitationRepository) GetInviteRecordByInvitee(ctx context.Context, inviteeId string) (*model.InviteRecord, error) {
	var record model.InviteRecord
	if err := slf.DB(ctx).NewSelect().Model(&record).Where("invitee_id = ?", inviteeId).Scan(ctx); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &record, nil
}

// GetInviteRecordsByInviter 通过邀请者查询邀请记录列表
func (slf *appUserInvitationRepository) GetInviteRecordsByInviter(ctx context.Context, req *v1.GetInviteReferralsRequest) (*v1.GetInviteReferralsResponseData, error) {
	var (
		list []*v1.GetInviteReferralsResponseDataItem
	)
	total, err := slf.DB(ctx).NewSelect().Model(&model.InviteRecord{}).
		ColumnExpr("DATE_FORMAT(ir.invite_time, '%Y-%m-%d %H:%i:%s') as inviteAt").
		ColumnExpr("ir.invitee_name as invitee").
		ColumnExpr("ir.inviter_vip_duration as giftVipDuration").
		Where("inviter_id = ?", req.Param.User.UserID).
		Order("invite_time DESC").
		Limit(req.Limit()).Offset(req.Offset()).
		ScanAndCount(ctx, &list)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return &v1.GetInviteReferralsResponseData{
				Total: 0,
				List:  nil,
			}, nil
		}
		return nil, err
	}
	return &v1.GetInviteReferralsResponseData{
		Total: total,
		List:  list,
	}, nil
}
