package repository

import (
	"context"
	"github.com/uptrace/bun"
	"time"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
)

type QwRepository interface {
	CreateContactWay(ctx context.Context, way *model.QwContactWay) (int64, error)
	UpdateContactWay(ctx context.Context, way *model.QwContactWay) error
	DeleteContactWay(ctx context.Context, id int64) error
	FetchContactWayByID(ctx context.Context, id int64) (*model.QwContactWay, error)
	FetchContactWayByAddState(ctx context.Context, addState string) (*model.QwContactWay, error)
	PageListContactWay(ctx context.Context, req *v1.QwPageListContactWayRequest) (*v1.QwPageListContactWayResponseData, error)
	PageListBill(ctx context.Context, req *v1.QwPageListBillRequest) (*v1.QwPageListBillResponseData, error)
	UpsertBill(ctx context.Context, bill *model.QwBill) error
	FetchContactByExternalUserID(ctx context.Context, externalUserID string) (*model.QwContact, error)
	UpsertContact(ctx context.Context, contact *model.QwContact) error
	UpdateContact(ctx context.Context, req *v1.QwUpdateContactRequest) error
	FetchContactFollowByExternalUserIDAndUserID(ctx context.Context, externalUserID, userID string) (*model.QwContactFollow, error)
	UpsertContactFollow(ctx context.Context, follow *model.QwContactFollow) error
	PageListContactFollow(ctx context.Context, req *v1.QwPageListContactFollowRequest) (*v1.QwPageListContactFollowResponseData, error)
	FetchContactFollowUserIDs(ctx context.Context) ([]string, error)
}

func NewQwRepository(repo *Repository) QwRepository {
	return &qwRepository{
		Repository: repo,
	}
}

type qwRepository struct {
	*Repository
}

func (slf *qwRepository) FetchContactWayByAddState(ctx context.Context, addState string) (*model.QwContactWay, error) {
	var way model.QwContactWay
	if err := slf.DB(ctx).NewSelect().Model(&model.QwContactWay{}).
		Where("add_state = ?", addState).
		Where("type = 1").
		Scan(ctx, &way); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &way, nil
}

func (slf *qwRepository) UpdateContact(ctx context.Context, req *v1.QwUpdateContactRequest) error {
	query := slf.DB(ctx).NewUpdate().Model(&model.QwContact{ID: req.ID})
	if req.RealName != nil {
		query.Set("real_name = ?", *req.RealName)
	}
	if req.Gender != nil {
		query.Set("gender = ?", *req.Gender)
	}
	if req.Phone != nil {
		query.Set("phone = ?", *req.Phone)
	}
	if _, err := query.WherePK().OmitZero().Exec(ctx); err != nil {
		return err
	}
	return nil
}

func (slf *qwRepository) FetchContactFollowUserIDs(ctx context.Context) ([]string, error) {
	var list []string
	if err := slf.DB(ctx).NewSelect().Model(&model.QwContactFollow{}).ColumnExpr("distinct user_id").Scan(ctx, &list); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}

func (slf *qwRepository) PageListContactFollow(ctx context.Context, req *v1.QwPageListContactFollowRequest) (*v1.QwPageListContactFollowResponseData, error) {
	var list []*v1.QwPageListContactFollowResponseItem
	query := slf.DB(ctx).NewSelect().Model(&model.QwContactFollow{}).
		ColumnExpr("qcf.id, qcf.remark, qcf.description").
		ColumnExpr("qc.name, qc.gender, qc.avatar").
		ColumnExpr("qcf.user_id, qcf.add_way, qcf.add_time, qcf.add_state").
		ColumnExpr("qcf.del_flag, qcf.del_time, qcf.del_source").
		ColumnExpr("qcf.events").
		ColumnExpr("qc.real_name, qc.phone").
		Join("left join qw_contact qc on qc.external_userid = qcf.external_userid")
	if req.Param.RealName != nil {
		query.Where("qc.real_name like concat('%', ?, '%')", *req.Param.RealName)
	}
	if req.Param.Phone != nil {
		query.Where("qc.phone like concat('%', ?, '%')", *req.Param.Phone)
	}
	if req.Param.Name != nil {
		query.Where("qc.name like concat('%', ?, '%')", *req.Param.Name)
	}
	if len(req.Param.Gender) > 0 {
		query.Where("qc.gender in (?)", bun.In(req.Param.Gender))
	}
	if req.Param.Remark != nil {
		query.Where("qcf.remark like concat('%', ?, '%')", *req.Param.Remark)
	}
	if len(req.Param.UserIDs) > 0 {
		query.Where("qcf.user_id in (?)", bun.In(req.Param.UserIDs))
	}
	if len(req.Param.DelFlag) > 0 {
		query.Where("qcf.del_flag in (?)", bun.In(req.Param.DelFlag))
	}
	if req.Param.AddStartAt != nil {
		startTime, err := time.Parse(time.DateTime, *req.Param.AddStartAt)
		if err != nil {
			return nil, err
		}
		query.Where("qcf.add_time >= ?", startTime.Unix())
	}
	if req.Param.AddEndAt != nil {
		endTime, err := time.Parse(time.DateTime, *req.Param.AddEndAt)
		if err != nil {
			return nil, err
		}
		query.Where("qcf.add_time <= ?", endTime.Unix())
	}
	count, err := query.Order("qcf.add_time desc").Offset(req.Offset()).Limit(req.Limit()).ScanAndCount(ctx, &list)
	if err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &v1.QwPageListContactFollowResponseData{
		Total: count,
		List:  list,
	}, nil
}

func (slf *qwRepository) UpsertContactFollow(ctx context.Context, follow *model.QwContactFollow) error {
	if _, err := slf.DB(ctx).NewInsert().Model(follow).On("duplicate key update").
		Set("add_way = ?", follow.AddWay).
		Set("add_time = ?", follow.AddTime).
		Set("add_state = ?", follow.AddState).
		Set("remark = ?", follow.Remark).
		Set("description = ?", follow.Description).
		Set("del_flag = ?", follow.DelFlag).
		Set("del_time = ?", follow.DelTime).
		Set("del_source = ?", follow.DelSource).
		Set("events = ?", follow.Events).Exec(ctx); err != nil {
		return err
	}
	return nil
}

func (slf *qwRepository) FetchContactFollowByExternalUserIDAndUserID(ctx context.Context, externalUserID, userID string) (*model.QwContactFollow, error) {
	follow := &model.QwContactFollow{}
	if err := slf.DB(ctx).NewSelect().Model(follow).
		Where("external_userid = ?", externalUserID).
		Where("user_id = ?", userID).
		Scan(ctx); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return follow, nil
}

func (slf *qwRepository) FetchContactByExternalUserID(ctx context.Context, externalUserID string) (*model.QwContact, error) {
	contact := &model.QwContact{}
	if err := slf.DB(ctx).NewSelect().Model(contact).
		Where("external_userid = ?", externalUserID).
		Scan(ctx); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return contact, nil
}

func (slf *qwRepository) UpsertContact(ctx context.Context, contact *model.QwContact) error {
	if _, err := slf.DB(ctx).NewInsert().Model(contact).On("duplicate key update").
		Set("external_userid = ?", contact.ExternalUserID).
		Set("union_id = ?", contact.UnionID).
		Set("name = ?", contact.Name).
		Set("avatar = ?", contact.Avatar).
		Set("gender = ?", contact.Gender).Exec(ctx); err != nil {
		return err
	}
	return nil
}

/*
	QwBill struct {
		bun.BaseModel   `bun:"qw_bill,alias:qb"`
		ID              int64             `bun:"id,pk,autoincrement" json:"id"`            // 主键ID
		TransactionID   string            `bun:"transaction_id" json:"transaction_id"`     // 交易单号
		BillType        int8              `bun:"bill_type" json:"bill_type"`               // 交易类型：0-收款记录 1-退款记录
		TradeState      int8              `bun:"trade_state" json:"trade_state"`           // 交易状态（退款记录不返回该字段）：1-已完成 3-已完成有退款
		PayTime         int64             `bun:"pay_time" json:"pay_time"`                 // 支付时间
		OutTradeNo      string            `bun:"out_trade_no" json:"out_trade_no"`         // 商户单号（退款记录返回对应收款记录的商户单号）
		OutRefundNo     string            `bun:"out_refund_no" json:"out_refund_no"`       // 商户退款单号（仅退款记录返回该字段）
		ExternalUserID  string            `bun:"external_userid" json:"external_userid"`   // 付款人的userid
		TotalFee        int64             `bun:"total_fee" json:"total_fee"`               // 收款总金额（单位：分）
		PayeeUserID     string            `bun:"payee_userid" json:"payee_userid"`         // 收款成员/退款成员的userid
		PaymentType     int8              `bun:"payment_type" json:"payment_type"`         // 收款方式：0-聊天中收款 1-收款码收款 2-直播间收款 3-产品图册收款 4-转账 5-小程序
		MchID           string            `bun:"mch_id" json:"mch_id"`                     // 收款商户号id
		Remark          string            `bun:"remark" json:"remark"`                     // 收款/退款备注
		CommodityList   []QwBillCommodity `bun:"commodity_list" json:"commodity_list"`     // 商品信息详情列表（仅收款记录返回）
		TotalRefundFee  int64             `bun:"total_refund_fee" json:"total_refund_fee"` // 退款总金额（单位：分）
		RefundList      []QwBillRefund    `bun:"refund_list" json:"refund_list"`           // 退款单据详情列表（仅收款记录返回）
		ContactInfo     QwBillContact     `bun:"contact_info" json:"contact_info"`         // 联系人信息（如创建收款项目时设置为不需要联系地址，则该字段为空，退款记录不返回该字段）
		MiniProgramInfo QwBillMiniProgram `bun:"miniprogram_info" json:"miniprogram_info"` // 小程序信息（收款方式为小程序时返回该字段）
	}
*/

func (slf *qwRepository) UpsertBill(ctx context.Context, bill *model.QwBill) error {
	if _, err := slf.DB(ctx).NewInsert().Model(bill).On("duplicate key update").
		Set("transaction_id = ?", bill.TransactionID).
		Set("bill_type = ?", bill.BillType).
		Set("trade_state = ?", bill.TradeState).
		Set("pay_time = ?", bill.PayTime).
		Set("out_trade_no = ?", bill.OutTradeNo).
		Set("out_refund_no = ?", bill.OutRefundNo).
		Set("external_userid = ?", bill.ExternalUserID).
		Set("total_fee = ?", bill.TotalFee).
		Set("payee_userid = ?", bill.PayeeUserID).
		Set("payment_type = ?", bill.PaymentType).
		Set("mch_id = ?", bill.MchID).
		Set("remark = ?", bill.Remark).
		Set("commodity_list = ?", bill.CommodityList).
		Set("total_refund_fee = ?", bill.TotalRefundFee).
		Set("refund_list = ?", bill.RefundList).
		Set("contact_info = ?", bill.ContactInfo).
		Set("miniprogram_info = ?", bill.MiniProgramInfo).
		Exec(ctx); err != nil {
		return err
	}
	return nil
}

func (slf *qwRepository) PageListBill(ctx context.Context, req *v1.QwPageListBillRequest) (*v1.QwPageListBillResponseData, error) {
	var list []*v1.QwPageListBillResponseItem
	query := slf.DB(ctx).NewSelect().Model(&model.QwBill{})
	if req.Param.ID != nil {
		query.Where("id like concat('%', ?, '%')", *req.Param.ID)
	}
	if req.Param.TransactionID != nil {
		query.Where("transaction_id like concat('%', ?, '%')", *req.Param.TransactionID)
	}
	if req.Param.BillType != nil {
		query.Where("bill_type = ?", *req.Param.BillType)
	}
	if req.Param.TradeState != nil {
		query.Where("trade_state = ?", *req.Param.TradeState)
	}
	if req.Param.PayStartTime != nil {
		startTime, err := time.Parse(time.DateTime, *req.Param.PayStartTime)
		if err != nil {
			return nil, err
		}
		query.Where("pay_time >= ?", startTime)
	}
	if req.Param.PayEndTime != nil {
		endTime, err := time.Parse(time.DateTime, *req.Param.PayEndTime)
		if err != nil {
			return nil, err
		}
		query.Where("pay_time <= ?", endTime)
	}
	if len(req.Param.PayeeUserIDs) > 0 {
		query.Where("payee_user_id in (?)", bun.In(req.Param.PayeeUserIDs))
	}
	count, err := query.Order("pay_time desc").
		Offset(req.Offset()).Limit(req.Limit()).ScanAndCount(ctx, &list)
	if err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &v1.QwPageListBillResponseData{
		Total: count,
		List:  list,
	}, nil
}

func (slf *qwRepository) PageListContactWay(ctx context.Context, req *v1.QwPageListContactWayRequest) (*v1.QwPageListContactWayResponseData, error) {
	var list []*v1.QwPageListContactWayResponseItem
	query := slf.DB(ctx).NewSelect().Model(&model.QwContactWay{}).
		ColumnExpr("qcw.id, qcw.name, qcw.app_id, qcw.platform_id").
		ColumnExpr("a.name as app_name, p.name as platform_name").
		ColumnExpr("qcw.link, qcw.skip_verify, qcw.user_ids, qcw.add_state").
		ColumnExpr("date_format(qcw.created_at, '%Y-%m-%d %H:%i:%s') AS created_time").
		ColumnExpr("date_format(qcw.updated_at, '%Y-%m-%d %H:%i:%s') AS updated_time").
		Join("left join app a on a.id = qcw.app_id").
		Join("left join platform p on p.id = qcw.platform_id")
	if req.Param.ID != nil {
		query.Where("qcw.id like concat('%', ?, '%')", *req.Param.ID)
	}
	if req.Param.Name != nil {
		query.Where("qcw.name like concat('%', ?, '%')", *req.Param.Name)
	}
	if len(req.Param.AppIDs) > 0 {
		query.Where("qcw.app_id in (?)", req.Param.AppIDs)
	}
	if len(req.Param.PlatformIDs) > 0 {
		query.Where("qcw.platform_id in (?)", req.Param.PlatformIDs)
	}
	if req.Param.AddState != nil {
		query.Where("qcw.add_state like concat('%', ?, '%')", *req.Param.AddState)
	}
	count, err := query.Order("qcw.updated_at desc").
		Offset(req.Offset()).Limit(req.Limit()).ScanAndCount(ctx, &list)
	if err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &v1.QwPageListContactWayResponseData{
		Total: count,
		List:  list,
	}, nil
}

func (slf *qwRepository) FetchContactWayByID(ctx context.Context, id int64) (*model.QwContactWay, error) {
	way := &model.QwContactWay{ID: id}
	if err := slf.DB(ctx).NewSelect().Model(way).WherePK().Scan(ctx); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return way, nil
}

func (slf *qwRepository) DeleteContactWay(ctx context.Context, id int64) error {
	if _, err := slf.DB(ctx).NewDelete().Model(&model.QwContactWay{ID: id}).
		WherePK().Exec(ctx); err != nil {
		return err
	}
	return nil
}

func (slf *qwRepository) UpdateContactWay(ctx context.Context, way *model.QwContactWay) error {
	if _, err := slf.DB(ctx).NewUpdate().Model(&model.QwContactWay{ID: way.ID}).
		Set("name = ?", way.Name).
		Set("skip_verify = ?", way.SkipVerify).
		Set("user_ids = ?", way.UserIDs).
		WherePK().OmitZero().Exec(ctx); err != nil {
		return err
	}
	return nil
}

func (slf *qwRepository) CreateContactWay(ctx context.Context, way *model.QwContactWay) (int64, error) {
	result, err := slf.DB(ctx).NewInsert().
		Model(way).Exec(ctx)
	if err != nil {
		if slf.IsDuplicateEntry(err) {
			return 0, v1.ErrQwContactWayAlreadyTaken
		}
		return 0, err
	}
	return result.LastInsertId()
}
