package repository

import (
	"context"
	"github.com/uptrace/bun"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
)

type UserHepanRecordRepository interface {
	CreateHepanRecord(ctx context.Context, record *model.HepanRecord) (int64, error)
	PageListHepanRecord(ctx context.Context, req *v1.PageListHepanRequest) (*v1.PageListHepanResponseData, error)
	GetHepanRecordByID(ctx context.Context, userId string, id int64) (*model.HepanRecord, error)
	DeleteHepanRecord(ctx context.Context, req *v1.DeleteHepanRequest) error
}

func NewUserHepanRecordRepository(
	repo *Repository,
) UserHepanRecordRepository {
	return &userHepanRecordRepository{
		Repository: repo,
	}
}

type userHepanRecordRepository struct {
	*Repository
}

func (slf *userHepanRecordRepository) DeleteHepanRecord(ctx context.Context, req *v1.DeleteHepanRequest) error {
	query := slf.DB(ctx).NewDelete().Model(&model.HepanRecord{}).
		Where("user_id = ?", req.User)
	if len(req.IDs) > 0 {
		query.Where("id in (?)", bun.In(req.IDs))
	}
	_, err := query.Exec(ctx)
	return err
}

func (slf *userHepanRecordRepository) CreateHepanRecord(ctx context.Context, record *model.HepanRecord) (int64, error) {
	exec, err := slf.DB(ctx).NewInsert().Model(record).Exec(ctx)
	if err != nil {
		return 0, err
	}
	return exec.LastInsertId()
}

func (slf *userHepanRecordRepository) PageListHepanRecord(ctx context.Context, req *v1.PageListHepanRequest) (*v1.PageListHepanResponseData, error) {
	var records []*model.HepanRecord
	query := slf.DB(ctx).NewSelect().Model(&model.HepanRecord{}).
		Where("user_id = ?", req.Param.User)
	count, err := query.Order("created_at desc").Offset(req.Offset()).Limit(req.Limit()).ScanAndCount(ctx, &records)
	if err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	var list []*v1.PageListHepanResponseDataItem
	for _, record := range records {
		list = append(list, &v1.PageListHepanResponseDataItem{
			ID: record.ID,
			MingliA: &v1.PageListHepanResponseDataItemMingli{
				ID:             record.MingliIdA,
				Name:           record.NameA,
				Gender:         record.GenderA,
				Birthtime:      record.BirthtimeA.Format("2006-01-02 15:04:05"),
				BirthtimeSun:   record.BirthtimeSunA.Format("2006-01-02 15:04:05"),
				BirthtimeLunar: record.BirthtimeLunarA,
				Bazi:           record.BaziA,
				Birthplace:     record.BirthplaceA,
			},
			MingliB: &v1.PageListHepanResponseDataItemMingli{
				ID:             record.MingliIdB,
				Name:           record.NameB,
				Gender:         record.GenderB,
				Birthtime:      record.BirthtimeB.Format("2006-01-02 15:04:05"),
				BirthtimeSun:   record.BirthtimeSunB.Format("2006-01-02 15:04:05"),
				BirthtimeLunar: record.BirthtimeLunarB,
				Bazi:           record.BaziB,
				Birthplace:     record.BirthplaceB,
			},
			CreatedAt: record.CreatedAt.Format("2006-01-02 15:04:05"),
		})
	}
	return &v1.PageListHepanResponseData{
		Total: count,
		List:  list,
	}, nil
}

func (slf *userHepanRecordRepository) GetHepanRecordByID(ctx context.Context, userId string, id int64) (*model.HepanRecord, error) {
	var record model.HepanRecord
	if err := slf.DB(ctx).NewSelect().Model(&model.HepanRecord{}).
		Where("id = ?", id).
		Where("user_id = ?", userId).
		Scan(ctx, &record); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &record, nil
}
