package repository

import (
	"context"
	"zodiacus/internal/model"
)

type AuthRepository interface {
	InsertVerificationCode(ctx context.Context, vc *model.VerificationCode) (int64, error)
	FetchNewestVerificationCode(ctx context.Context, typ, scene, dest string) (*model.VerificationCode, error)
	SetVerificationCodeUsed(ctx context.Context, id int64) error
}

func NewAuthRepository(
	repo *Repository,
) AuthRepository {
	return &authRepository{
		Repository: repo,
	}
}

type authRepository struct {
	*Repository
}

// SetVerificationCodeUsed 设置验证码已使用
func (slf *authRepository) SetVerificationCodeUsed(ctx context.Context, id int64) error {
	if _, err := slf.DB(ctx).NewUpdate().Model(&model.VerificationCode{ID: id}).
		Set("is_used = ?", true).
		WherePK().
		Exec(ctx); err != nil {
		return err
	}
	return nil
}

// FetchNewestVerificationCode 获取最新验证码（类型、场景、目标相同）
func (slf *authRepository) FetchNewestVerificationCode(ctx context.Context, typ, scene, dest string) (*model.VerificationCode, error) {
	var vc model.VerificationCode
	if err := slf.DB(ctx).NewSelect().Model(&model.VerificationCode{}).
		Where("type = ?", typ).
		Where("scene = ?", scene).
		Where("dest = ?", dest).
		Order("created_at desc").
		Limit(1).
		Scan(ctx, &vc); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &vc, nil
}

// InsertVerificationCode 插入验证码
func (slf *authRepository) InsertVerificationCode(ctx context.Context, vc *model.VerificationCode) (int64, error) {
	exec, err := slf.DB(ctx).NewInsert().Model(vc).Exec(ctx)
	if err != nil {
		return 0, err
	}
	return exec.LastInsertId()
}
