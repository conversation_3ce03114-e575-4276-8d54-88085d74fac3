package repository

import (
	"context"
	"github.com/uptrace/bun"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
)

type AppVersionRepository interface {
	FetchAppVersionByID(ctx context.Context, id int64) (*model.AppVersion, error)
	FetchAppVersionByName(ctx context.Context, appID int64, name string, osType int) (*model.AppVersion, error)
	FetchNextVersion(ctx context.Context, appID int64, osType int, cur int, isHot bool) (*model.AppVersion, error)
	FetchLatestAppVersionByAppID(ctx context.Context, appID int64, osType int) (*model.AppVersion, error)
	CreateAppVersion(ctx context.Context, appVersion *model.AppVersion) (int64, error)
	UpdateAppVersion(ctx context.Context, appVersion *model.AppVersion) error
	PageListAppVersion(ctx context.Context, req *v1.PageListAppVersionRequest) (*v1.PageListAppVersionResponseData, error)
}

func NewAppVersionRepository(repo *Repository) AppVersionRepository {
	return &appVersionRepository{Repository: repo}
}

type appVersionRepository struct {
	*Repository
}

func (slf *appVersionRepository) PageListAppVersion(ctx context.Context, req *v1.PageListAppVersionRequest) (*v1.PageListAppVersionResponseData, error) {
	var list []*v1.PageListAppVersionResponseDateItem
	query := slf.DB(ctx).NewSelect().Model(&model.AppVersion{}).
		Join("left join app on app.id = av.app_id").
		ColumnExpr("av.id as id").
		ColumnExpr("av.app_id as app_id").
		ColumnExpr("app.name as app_name").
		ColumnExpr("av.version_code as version_code").
		ColumnExpr("av.version_name as version_name").
		ColumnExpr("av.update_note as update_note").
		ColumnExpr("av.os_type as os_type").
		ColumnExpr("av.url as url").
		ColumnExpr("av.is_force_update as is_force_update").
		ColumnExpr("av.is_hot_update as is_hot_update").
		ColumnExpr("av.remark as remark").
		ColumnExpr("av.status as status").
		ColumnExpr("date_format(av.created_at, '%Y-%m-%d %H:%i:%s') as created_at").
		ColumnExpr("date_format(av.updated_at, '%Y-%m-%d %H:%i:%s') as updated_at")
	if len(req.Param.AppIDs) > 0 {
		query.Where("av.app_id in (?)", bun.In(req.Param.AppIDs))
	}
	if req.Param.VersionName != nil {
		query.Where("av.version_name like concat('%', ?, '%')", *req.Param.VersionName)
	}
	if req.Param.UpdateNote != nil {
		query.Where("av.update_note like concat('%', ?, '%')", *req.Param.UpdateNote)
	}
	if req.Param.Remark != nil {
		query.Where("av.remark like concat('%', ?, '%')", *req.Param.Remark)
	}
	if len(req.Param.OsType) > 0 {
		query.Where("av.os_type in (?)", bun.In(req.Param.OsType))
	}
	count, err := query.Offset(req.Offset()).Limit(req.Limit()).Order("av.created_at desc").ScanAndCount(ctx, &list)
	if err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &v1.PageListAppVersionResponseData{
		Total: count,
		List:  list,
	}, nil
}

func (slf *appVersionRepository) UpdateAppVersion(ctx context.Context, appVersion *model.AppVersion) error {
	if _, err := slf.DB(ctx).NewUpdate().Model(appVersion).
		Set("version_name = ?", appVersion.VersionName).
		Set("update_note = ?", appVersion.UpdateNote).
		Set("url = ?", appVersion.Url).
		Set("is_force_update = ?", appVersion.IsForceUpdate).
		Set("is_hot_update = ?", appVersion.IsHotUpdate).
		Set("remark = ?", appVersion.Remark).
		Set("status = ?", appVersion.Status).
		WherePK().
		OmitZero().Exec(ctx); err != nil {
		return err
	}
	return nil
}

func (slf *appVersionRepository) FetchAppVersionByID(ctx context.Context, id int64) (*model.AppVersion, error) {
	var appVersion model.AppVersion
	if err := slf.DB(ctx).NewSelect().Model(&model.AppVersion{}).
		Where("id = ?", id).
		Scan(ctx, &appVersion); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &appVersion, nil
}

func (slf *appVersionRepository) FetchAppVersionByName(ctx context.Context, appID int64, name string, osType int) (*model.AppVersion, error) {
	var appVersion model.AppVersion
	if err := slf.DB(ctx).NewSelect().Model(&model.AppVersion{}).
		Where("app_id = ?", appID).
		Where("version_name = ?", name).
		Where("os_type = ?", osType).
		Scan(ctx, &appVersion); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &appVersion, nil
}

func (slf *appVersionRepository) FetchLatestAppVersionByAppID(ctx context.Context, appID int64, osType int) (*model.AppVersion, error) {
	var appVersion model.AppVersion
	if err := slf.DB(ctx).NewSelect().Model(&model.AppVersion{}).
		Where("app_id = ?", appID).
		Where("os_type = ?", osType).
		Order("version_code desc").
		Limit(1).
		Scan(ctx, &appVersion); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &appVersion, nil
}

func (slf *appVersionRepository) CreateAppVersion(ctx context.Context, appVersion *model.AppVersion) (int64, error) {
	res, err := slf.DB(ctx).NewInsert().Model(appVersion).Exec(ctx)
	if err != nil {
		return 0, err
	}
	return res.LastInsertId()
}

func (slf *appVersionRepository) FetchNextVersion(ctx context.Context, appID int64, osType int, cur int, isHot bool) (*model.AppVersion, error) {
	var appVersion model.AppVersion
	query := slf.DB(ctx).NewSelect().Model(&model.AppVersion{}).
		Where("app_id = ?", appID).
		Where("os_type = ?", osType).
		Where("version_code > ?", cur).
		Where("is_hot_update = ?", isHot)
	if isHot {
		query.Order("version_code desc")
	} else {
		query.Order("version_code asc")
	}
	if err := query.Limit(1).
		Scan(ctx, &appVersion); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &appVersion, nil
}
