package repository

import (
	"context"
	"database/sql"
	"errors"
	"github.com/uptrace/bun"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
)

type UserMingliRepository interface {
	FetchUserMingli(ctx context.Context, userID string, mingliID int64) (*model.UserMingli, error)
	CreateUserMingli(ctx context.Context, mingli *model.UserMingli) (int64, error)
	UpdateUserMingli(ctx context.Context, mingli *model.UserMingli) error
	DeleteUserMingli(ctx context.Context, mingliID int64, uid string) error
	ListUserMingli(ctx context.Context, userID string, appID, groupID int64) ([]*model.UserMingli, error)
	SetDefaultMingli(ctx context.Context, userID string, mingliID int64) error
	SetMingliWuxing(ctx context.Context, userID string, mingliID int64, wuxing []string) error
	GetDefaultMingli(ctx context.Context, userID string, appID, groupID int64) (*model.UserMingli, error)
	GetMingliByID(ctx context.Context, userID string, mingliID int64) (*model.UserMingli, error)
	GetMingli(ctx context.Context, id int64) (*model.UserMingli, error)
	PageListUserMingli(ctx context.Context, req *v1.PageListUserMingliRequest) (*v1.PageListUserMingliResponseData, error)
}

func NewUserMingliRepository(
	repo *Repository,
) UserMingliRepository {
	return &userMingliRepository{
		Repository: repo,
	}
}

type userMingliRepository struct {
	*Repository
}

func (slf *userMingliRepository) GetMingli(ctx context.Context, id int64) (*model.UserMingli, error) {
	var item model.UserMingli
	if err := slf.DB(ctx).NewSelect().Model(&model.UserMingli{}).
		Where("id = ?", id).
		Scan(ctx, &item); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &item, nil
}

func (slf *userMingliRepository) GetMingliByID(ctx context.Context, userID string, mingliID int64) (*model.UserMingli, error) {
	var item model.UserMingli
	if err := slf.DB(ctx).NewSelect().Model(&model.UserMingli{}).
		Where("id = ?", mingliID).
		Where("user_id = ?", userID).
		Scan(ctx, &item); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &item, nil
}

func (slf *userMingliRepository) PageListUserMingli(ctx context.Context, req *v1.PageListUserMingliRequest) (*v1.PageListUserMingliResponseData, error) {
	var items []*v1.PageListUserMingliResponseDataItem
	query := slf.DB(ctx).NewSelect().Model(&model.UserMingli{}).
		Join("left join user_mingli_group umg on umg.id = um.group_id").
		Join("left join app a on a.id = um.app_id").
		ColumnExpr("um.id").
		ColumnExpr("um.name").
		ColumnExpr("um.user_id").
		ColumnExpr("um.gender").
		ColumnExpr("um.birthtime").
		ColumnExpr("um.birthtime_sun").
		ColumnExpr("um.birthtime_lunar").
		ColumnExpr("um.birthplace").
		ColumnExpr("um.bazi").
		ColumnExpr("um.is_default").
		ColumnExpr("um.wuxing").
		ColumnExpr("um.xiaoyun").
		ColumnExpr("um.dayun").
		ColumnExpr("um.app_id").
		ColumnExpr("um.group_id").
		ColumnExpr("a.name as app_name").
		ColumnExpr("umg.name as group_name")

	if req.Param.Name != nil {
		query.Where("um.name like concat('%', ?, '%')", *req.Param.Name)
	}
	if len(req.Param.Gender) > 0 {
		query.Where("um.gender in (?)", bun.In(req.Param.Gender))
	}
	if req.Param.BirthTimeStart != nil {
		query.Where("um.birthtime >= ?", *req.Param.BirthTimeStart)
	}
	if req.Param.BirthTimeEnd != nil {
		query.Where("um.birthtime <= ?", *req.Param.BirthTimeEnd)
	}
	if req.Param.Bazi != nil {
		query.Where("REPLACE(JSON_UNQUOTE(um.bazi), '\", \"', '') LIKE concat('%', ?, '%')", *req.Param.Bazi)
	}
	if req.Param.Birthplace != nil {
		query.Where("REPLACE(JSON_UNQUOTE(um.birthplace), '\", \"', '') LIKE concat('%', ?, '%')", *req.Param.Birthplace)
	}
	if req.Param.BirthTimeSunStart != nil {
		query.Where("um.birthtime_sun >= ?", *req.Param.BirthTimeSunStart)
	}
	if req.Param.BirthTimeSunEnd != nil {
		query.Where("um.birthtime_sun <= ?", *req.Param.BirthTimeSunEnd)
	}
	if len(req.Param.AppIDs) > 0 {
		query.Where("um.app_id in (?)", bun.In(req.Param.AppIDs))
	}

	count, err := query.Order("um.created_at desc").Offset(req.Offset()).Limit(req.Limit()).ScanAndCount(ctx, &items)
	if err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}

	for _, item := range items {
		item.BirthtimeStr = item.Birthtime.Format("2006-01-02 15:04:05")
		item.BirthtimeSunStr = item.BirthtimeSun.Format("2006-01-02 15:04:05")
		item.Wuxing = nil
		item.Xiaoyun = nil
		item.Dayun = nil
	}
	return &v1.PageListUserMingliResponseData{
		Total: count,
		List:  items,
	}, nil
}

func (slf *userMingliRepository) GetDefaultMingli(ctx context.Context, userID string, appID, groupID int64) (*model.UserMingli, error) {
	var item model.UserMingli
	if err := slf.DB(ctx).NewSelect().Model(&model.UserMingli{}).
		Where("user_id = ?", userID).
		Where("app_id = ?", appID).
		Where("group_id = ?", groupID).
		Where("is_default = ?", 1).
		Scan(ctx, &item); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &item, nil
}

func (slf *userMingliRepository) FetchUserMingli(ctx context.Context, userID string, mingliID int64) (*model.UserMingli, error) {
	var item model.UserMingli
	if err := slf.DB(ctx).NewSelect().Model(&model.UserMingli{}).
		Where("id = ?", mingliID).
		Where("user_id = ?", userID).
		Scan(ctx, &item); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &item, nil
}

func (slf *userMingliRepository) ListUserMingli(ctx context.Context, userID string, appID, groupID int64) ([]*model.UserMingli, error) {
	var items []*model.UserMingli
	query := slf.DB(ctx).NewSelect().Model(&model.UserMingli{}).
		Where("user_id = ?", userID).
		Where("app_id = ?", appID)
	if groupID != 0 {
		query.Where("group_id = ?", groupID)
	}
	if err := query.
		Order("updated_at desc").
		Order("is_default desc").
		Scan(ctx, &items); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return items, nil
}

func (slf *userMingliRepository) DeleteUserMingli(ctx context.Context, mingliID int64, uid string) error {
	if _, err := slf.DB(ctx).NewDelete().Model(&model.UserMingli{}).
		Where("id = ?", mingliID).
		Where("user_id = ?", uid).
		Exec(ctx); err != nil {
		return err
	}
	return nil
}

func (slf *userMingliRepository) UpdateUserMingli(ctx context.Context, mingli *model.UserMingli) error {
	if _, err := slf.DB(ctx).NewUpdate().Model(mingli).
		WherePK().
		Where("user_id = ?", mingli.UserID).
		OmitZero().Exec(ctx); err != nil {
		return err
	}
	return nil
}

func (slf *userMingliRepository) CreateUserMingli(ctx context.Context, mingli *model.UserMingli) (int64, error) {
	exec, err := slf.DB(ctx).NewInsert().Model(mingli).Exec(ctx)
	if err != nil {
		return 0, err
	}
	return exec.LastInsertId()
}

func (slf *userMingliRepository) SetDefaultMingli(ctx context.Context, userID string, mingliID int64) error {
	if _, err := slf.DB(ctx).NewUpdate().Model(&model.UserMingli{ID: mingliID}).
		Set("is_default = ?", 1).
		WherePK().
		Where("user_id = ?", userID).
		OmitZero().Exec(ctx); err != nil {
		return err
	}
	if _, err := slf.DB(ctx).NewUpdate().Model(&model.UserMingli{}).
		Set("is_default = ?", 0).
		Where("id != ?", mingliID).
		Where("user_id = ?", userID).
		OmitZero().Exec(ctx); err != nil {
		return err
	}
	return nil
}

func (slf *userMingliRepository) SetMingliWuxing(ctx context.Context, userID string, mingliID int64, wuxing []string) error {
	if _, err := slf.DB(ctx).NewUpdate().Model(&model.UserMingli{ID: mingliID}).
		Set("wuxing = ?", wuxing).
		WherePK().
		Where("user_id = ?", userID).
		OmitZero().Exec(ctx); err != nil {
		return err
	}
	return nil
}
