package repository

import (
	"context"
	"zodiacus/internal/model"
)

type OffiaccountRepository interface {
	FetchOffiaccountUserByOpenID(ctx context.Context, openID string) (*model.OffiaccountUser, error)
	CreateOffiaccountUser(ctx context.Context, user *model.OffiaccountUser) (int64, error)
	UpdateOffiaccountUser(ctx context.Context, user *model.OffiaccountUser) error
	FetchQrcodeBySceneStr(ctx context.Context, sceneStr string) (*model.OaQrcode, error)
	CreateQrcode(ctx context.Context, qrcode *model.OaQrcode) (int64, error)
	CreateMaterial(ctx context.Context, material *model.OffiaccountMaterial) (int64, error)
	FetchMaterialByRemark(ctx context.Context, remark string) (*model.OffiaccountMaterial, error)
}

func NewOffiaccountRepository(repo *Repository) OffiaccountRepository {
	return &offiaccountRepository{Repository: repo}
}

type offiaccountRepository struct {
	*Repository
}

func (slf *offiaccountRepository) CreateMaterial(ctx context.Context, material *model.OffiaccountMaterial) (int64, error) {
	result, err := slf.DB(ctx).NewInsert().Model(material).Exec(ctx)
	if err != nil {
		return 0, err
	}
	return result.LastInsertId()
}

func (slf *offiaccountRepository) FetchMaterialByRemark(ctx context.Context, remark string) (*model.OffiaccountMaterial, error) {
	var material model.OffiaccountMaterial
	if err := slf.DB(ctx).NewSelect().Model(&model.OffiaccountMaterial{}).
		Where("remark = ?", remark).Scan(ctx, &material); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &material, nil
}

func (slf *offiaccountRepository) CreateQrcode(ctx context.Context, qrcode *model.OaQrcode) (int64, error) {
	result, err := slf.DB(ctx).NewInsert().Model(qrcode).Exec(ctx)
	if err != nil {
		return 0, err
	}
	return result.LastInsertId()
}

func (slf *offiaccountRepository) FetchQrcodeBySceneStr(ctx context.Context, sceneStr string) (*model.OaQrcode, error) {
	var qrcode model.OaQrcode
	if err := slf.DB(ctx).NewSelect().Model(&model.OaQrcode{}).
		Where("scene_str = ?", sceneStr).Scan(ctx, &qrcode); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &qrcode, nil
}

func (slf *offiaccountRepository) UpdateOffiaccountUser(ctx context.Context, user *model.OffiaccountUser) error {
	_, err := slf.DB(ctx).NewUpdate().Model(user).WherePK().Exec(ctx)
	return err
}

func (slf *offiaccountRepository) CreateOffiaccountUser(ctx context.Context, user *model.OffiaccountUser) (int64, error) {
	result, err := slf.DB(ctx).NewInsert().Model(user).Exec(ctx)
	if err != nil {
		return 0, err
	}
	return result.LastInsertId()
}

func (slf *offiaccountRepository) FetchOffiaccountUserByOpenID(ctx context.Context, openID string) (*model.OffiaccountUser, error) {
	var user model.OffiaccountUser
	if err := slf.DB(ctx).NewSelect().Model(&model.OffiaccountUser{}).
		Where("open_id = ?", openID).Scan(ctx, &user); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}
