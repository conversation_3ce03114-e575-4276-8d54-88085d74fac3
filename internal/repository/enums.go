package repository

import (
	"context"
	"database/sql"
	"errors"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
)

type EnumsRepository interface {
	GetAllApp(ctx context.Context, req *v1.EnumsAppRequest) ([]*model.App, error)
	GetAllModule(ctx context.Context) ([]*model.Module, error)
	GetAllTiangan(ctx context.Context) ([]*model.<PERSON><PERSON><PERSON>, error)
	GetAllDizhi(ctx context.Context) ([]*model.Dizhi, error)
	GetAllZuodui(ctx context.Context) ([]*model.Zuodui, error)
	GetAllGanzhi(ctx context.Context) ([]*model.G<PERSON>zhi, error)
	GetAllShishen(ctx context.Context) ([]*model.Shishen, error)
	GetAllXiji(ctx context.Context) ([]*model.Xiji, error)
	GetAllShierchangsheng(ctx context.Context) ([]*model.<PERSON><PERSON><PERSON><PERSON>ng, error)
	GetAllJieqi(ctx context.Context) ([]*model.<PERSON><PERSON><PERSON>, error)
	GetAllTime(ctx context.Context) ([]*model.Time, error)
	GetAllYiji(ctx context.Context) ([]*model.Yiji, error)
	GetAllWuxing(ctx context.Context) ([]*model.Wuxing, error)
	GetAllZhishen(ctx context.Context) ([]*model.Zhishen, error)
	GetAllShierjianri(ctx context.Context) ([]*model.Shierjianri, error)
	GetAllRiyuanShishen(ctx context.Context) ([]*model.RiyuanShishen, error)
	GetAllNayin(ctx context.Context) ([]*model.Nayin, error)
	GetAllShensha(ctx context.Context) ([]*model.Shensha2, error)
	GetJiXiong(ctx context.Context) ([]*model.JiXiong, error)
	GetAllPengzubaiji(ctx context.Context) ([]*model.Pengzubaiji, error)
	GetAllDaysByLunarYear(ctx context.Context, lunarYear string) ([]*v1.LunarDay, error)
	GetAllJiemeng(ctx context.Context, req *v1.JiemengRequest) (*v1.JiemengResponseData, error)
	FetchAllAddrProvince(ctx context.Context) ([]*model.AddrProvince, error)
	FetchAllAddrCity(ctx context.Context) ([]*model.AddrCity, error)
	FetchAllArea(ctx context.Context) ([]*model.AddrArea, error)
	FetchAllCountry(ctx context.Context) ([]*model.AddrCountry, error)
	FetchAllCountryProvince(ctx context.Context) ([]*model.AddrCountryProvince, error)
	GetAllLunarTime(ctx context.Context) ([]*model.LunarTime, error)
	GetAllLunarDate(ctx context.Context) ([]*model.LunarDate, error)
	GetAllLunarMonth(ctx context.Context) ([]*model.LunarMonth, error)
	GetCalendarByDate(ctx context.Context, date string) (*model.Calendar, error)
}

func NewEnumsRepository(
	repo *Repository,
) EnumsRepository {
	return &enumsRepository{
		Repository: repo,
	}
}

type enumsRepository struct {
	*Repository
}

func (slf *enumsRepository) GetAllApp(ctx context.Context, req *v1.EnumsAppRequest) ([]*model.App, error) {
	var list []*model.App
	query := slf.DB(ctx).NewSelect().Model((*model.App)(nil))
	if req.OnlySelf {
		query = query.Where("is_self = ?", true)
	}
	if err := query.Order("id asc").
		Scan(ctx, &list); err != nil {
		return nil, err
	}
	return list, nil
}

func (slf *enumsRepository) GetCalendarByDate(ctx context.Context, date string) (*model.Calendar, error) {
	var item model.Calendar
	if err := slf.DB(ctx).NewSelect().Model(&item).
		Where("date = ?", date).
		Scan(ctx); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &item, nil
}

func (slf *enumsRepository) GetAllLunarTime(ctx context.Context) ([]*model.LunarTime, error) {
	var list []*model.LunarTime
	if err := slf.DB(ctx).NewSelect().Model((*model.LunarTime)(nil)).
		Order("id asc").
		Scan(ctx, &list); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}

func (slf *enumsRepository) GetAllLunarDate(ctx context.Context) ([]*model.LunarDate, error) {
	var list []*model.LunarDate
	if err := slf.DB(ctx).NewSelect().Model((*model.LunarDate)(nil)).
		Order("id asc").
		Scan(ctx, &list); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}

func (slf *enumsRepository) GetAllLunarMonth(ctx context.Context) ([]*model.LunarMonth, error) {
	var list []*model.LunarMonth
	if err := slf.DB(ctx).NewSelect().Model((*model.LunarMonth)(nil)).
		Order("id asc").
		Scan(ctx, &list); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}

func (slf *enumsRepository) GetAllXiji(ctx context.Context) ([]*model.Xiji, error) {
	var list []*model.Xiji
	if err := slf.DB(ctx).NewSelect().Model((*model.Xiji)(nil)).
		Order("id asc").
		Scan(ctx, &list); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}

func (slf *enumsRepository) GetAllShierchangsheng(ctx context.Context) ([]*model.Shierchangsheng, error) {
	var list []*model.Shierchangsheng
	if err := slf.DB(ctx).NewSelect().Model((*model.Shierchangsheng)(nil)).
		Order("id asc").
		Scan(ctx, &list); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}

func (slf *enumsRepository) GetAllModule(ctx context.Context) ([]*model.Module, error) {
	var list []*model.Module
	if err := slf.DB(ctx).NewSelect().Model((*model.Module)(nil)).
		Order("sort asc").Order("created_at desc").
		Scan(ctx, &list); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}

func (slf *enumsRepository) GetAllTiangan(ctx context.Context) ([]*model.Tiangan, error) {
	var list []*model.Tiangan
	if err := slf.DB(ctx).NewSelect().Model((*model.Tiangan)(nil)).
		Order("id asc").
		Scan(ctx, &list); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}

func (slf *enumsRepository) GetAllDizhi(ctx context.Context) ([]*model.Dizhi, error) {
	var list []*model.Dizhi
	if err := slf.DB(ctx).NewSelect().Model((*model.Dizhi)(nil)).
		Order("id asc").
		Scan(ctx, &list); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}

func (slf *enumsRepository) GetAllZuodui(ctx context.Context) ([]*model.Zuodui, error) {
	var list []*model.Zuodui
	if err := slf.DB(ctx).NewSelect().Model((*model.Zuodui)(nil)).
		Order("id asc").
		Scan(ctx, &list); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}

func (slf *enumsRepository) GetAllGanzhi(ctx context.Context) ([]*model.Ganzhi, error) {
	var list []*model.Ganzhi
	if err := slf.DB(ctx).NewSelect().Model((*model.Ganzhi)(nil)).
		Order("id asc").
		Scan(ctx, &list); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}

func (slf *enumsRepository) GetAllShishen(ctx context.Context) ([]*model.Shishen, error) {
	var list []*model.Shishen
	if err := slf.DB(ctx).NewSelect().Model((*model.Shishen)(nil)).
		Order("id asc").
		Scan(ctx, &list); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}

func (slf *enumsRepository) GetAllJiemeng(ctx context.Context, req *v1.JiemengRequest) (*v1.JiemengResponseData, error) {
	var list []*v1.JiemengResponseDataItem
	query := slf.DB(ctx).NewSelect().Model((*model.Jiemeng)(nil)).
		ColumnExpr("title").
		ColumnExpr("content")
	if req.Param.Content != "" {
		query = query.Where("title like concat('%', ?, '%')", req.Param.Content)
	}
	count, err := query.Order("id asc").Offset(req.Offset()).Limit(req.Limit()).ScanAndCount(ctx, &list)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &v1.JiemengResponseData{
		Total: count,
		List:  list,
	}, nil
}

func (slf *enumsRepository) GetAllShensha(ctx context.Context) ([]*model.Shensha2, error) {
	var list []*model.Shensha2
	if err := slf.DB(ctx).NewSelect().Model((*model.Shensha2)(nil)).
		Order("id asc").
		Scan(ctx, &list); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	for _, item := range list {
		item.Setup()
	}
	return list, nil
}

func (slf *enumsRepository) GetJiXiong(ctx context.Context) ([]*model.JiXiong, error) {
	var list []*model.JiXiong
	if err := slf.DB(ctx).NewSelect().Model((*model.JiXiong)(nil)).
		Order("id asc").
		Scan(ctx, &list); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}

func (slf *enumsRepository) GetAllPengzubaiji(ctx context.Context) ([]*model.Pengzubaiji, error) {
	var list []*model.Pengzubaiji
	if err := slf.DB(ctx).NewSelect().Model((*model.Pengzubaiji)(nil)).
		Order("id asc").
		Scan(ctx, &list); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}

func (slf *enumsRepository) GetAllNayin(ctx context.Context) ([]*model.Nayin, error) {
	var list []*model.Nayin
	if err := slf.DB(ctx).NewSelect().Model((*model.Nayin)(nil)).
		Order("id asc").
		Scan(ctx, &list); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}

func (slf *enumsRepository) GetAllShierjianri(ctx context.Context) ([]*model.Shierjianri, error) {
	var list []*model.Shierjianri
	if err := slf.DB(ctx).NewSelect().Model((*model.Shierjianri)(nil)).
		Order("id asc").
		Scan(ctx, &list); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}

func (slf *enumsRepository) GetAllZhishen(ctx context.Context) ([]*model.Zhishen, error) {
	var list []*model.Zhishen
	if err := slf.DB(ctx).NewSelect().Model((*model.Zhishen)(nil)).
		Order("id asc").
		Scan(ctx, &list); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}

func (slf *enumsRepository) GetAllRiyuanShishen(ctx context.Context) ([]*model.RiyuanShishen, error) {
	var list []*model.RiyuanShishen
	if err := slf.DB(ctx).NewSelect().Model((*model.RiyuanShishen)(nil)).
		Order("id asc").
		Scan(ctx, &list); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}

func (slf *enumsRepository) GetAllWuxing(ctx context.Context) ([]*model.Wuxing, error) {
	var list []*model.Wuxing
	if err := slf.DB(ctx).NewSelect().Model((*model.Wuxing)(nil)).
		Order("id asc").
		Scan(ctx, &list); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}

func (slf *enumsRepository) GetAllDaysByLunarYear(ctx context.Context, lunarYear string) ([]*v1.LunarDay, error) {
	var items []*v1.LunarDay
	if err := slf.DB(ctx).NewSelect().Model(&model.Calendar{}).
		ColumnExpr("date").       // 公历：2006-01-02
		ColumnExpr("lunar_date"). // 农历日期：腊月初一
		ColumnExpr("lunar_year"). // 农历年份：二零零六
		Where("lunar_year = ?", lunarYear).
		Order("date asc").
		Scan(ctx, &items); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return items, nil
}

func (slf *enumsRepository) GetAllJieqi(ctx context.Context) ([]*model.Jieqi, error) {
	var list []*model.Jieqi
	if err := slf.DB(ctx).NewSelect().Model((*model.Jieqi)(nil)).
		Order("id asc").
		Scan(ctx, &list); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}

func (slf *enumsRepository) GetAllTime(ctx context.Context) ([]*model.Time, error) {
	var list []*model.Time
	if err := slf.DB(ctx).NewSelect().Model((*model.Time)(nil)).
		Order("id asc").
		Scan(ctx, &list); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}

func (slf *enumsRepository) GetAllYiji(ctx context.Context) ([]*model.Yiji, error) {
	var list []*model.Yiji
	if err := slf.DB(ctx).NewSelect().Model((*model.Yiji)(nil)).
		Order("id asc").
		Scan(ctx, &list); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}

func (slf *enumsRepository) FetchAllCountry(ctx context.Context) ([]*model.AddrCountry, error) {
	var list []*model.AddrCountry
	if err := slf.DB(ctx).NewSelect().Model((*model.AddrCountry)(nil)).
		Scan(ctx, &list); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}

func (slf *enumsRepository) FetchAllCountryProvince(ctx context.Context) ([]*model.AddrCountryProvince, error) {
	var list []*model.AddrCountryProvince
	if err := slf.DB(ctx).NewSelect().Model((*model.AddrCountryProvince)(nil)).
		Scan(ctx, &list); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}

func (slf *enumsRepository) FetchAllAddrProvince(ctx context.Context) ([]*model.AddrProvince, error) {
	var list []*model.AddrProvince
	if err := slf.DB(ctx).NewSelect().Model((*model.AddrProvince)(nil)).
		Scan(ctx, &list); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}

func (slf *enumsRepository) FetchAllAddrCity(ctx context.Context) ([]*model.AddrCity, error) {
	var list []*model.AddrCity
	if err := slf.DB(ctx).NewSelect().Model((*model.AddrCity)(nil)).
		Scan(ctx, &list); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}

func (slf *enumsRepository) FetchAllArea(ctx context.Context) ([]*model.AddrArea, error) {
	var list []*model.AddrArea
	if err := slf.DB(ctx).NewSelect().Model((*model.AddrArea)(nil)).
		Scan(ctx, &list); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}
