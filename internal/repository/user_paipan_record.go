package repository

import (
	"context"
	"github.com/samber/lo"
	"github.com/uptrace/bun"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
)

type UserPaipanRecordRepository interface {
	CreatePaipanRecord(ctx context.Context, record *model.PaipanRecord) (int64, error)
	PageListPaipanRecord(ctx context.Context, req *v1.PageListPaipanRecordRequest) (*v1.PageListPaipanRecordResponseData, error)
	FetchPaipanRecordByID(ctx context.Context, id int64) (*model.PaipanRecord, error)
	DeletePaipanRecord(ctx context.Context, req *v1.DeletePaipanRecordRequest) error
	OwnPaipanRecord(ctx context.Context, userID string, recordIDs []int64) error
}

func NewUserPaipanRecordRepository(
	repo *Repository,
) UserPaipanRecordRepository {
	return &userPaipanRecordRepository{
		Repository: repo,
	}
}

type userPaipanRecordRepository struct {
	*Repository
}

func (slf *userPaipanRecordRepository) OwnPaipanRecord(ctx context.Context, userID string, recordIDs []int64) error {
	_, err := slf.DB(ctx).NewUpdate().Model(&model.PaipanRecord{}).
		Set("user_id = ?", userID).
		Where("id in (?)", bun.In(recordIDs)).
		Where("user_id = ''").
		OmitZero().
		Exec(ctx)
	return err
}

func (slf *userPaipanRecordRepository) DeletePaipanRecord(ctx context.Context, req *v1.DeletePaipanRecordRequest) error {
	query := slf.DB(ctx).NewDelete().Model(&model.PaipanRecord{}).
		Where("user_id = ?", req.User)
	if len(req.IDs) > 0 {
		query.Where("id in (?)", bun.In(req.IDs))
	}
	_, err := query.Exec(ctx)
	return err
}

func (slf *userPaipanRecordRepository) FetchPaipanRecordByID(ctx context.Context, id int64) (*model.PaipanRecord, error) {
	var record model.PaipanRecord
	if err := slf.DB(ctx).NewSelect().Model(&model.PaipanRecord{}).
		Where("id = ?", id).
		Scan(ctx, &record); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &record, nil
}

func (slf *userPaipanRecordRepository) CreatePaipanRecord(ctx context.Context, record *model.PaipanRecord) (int64, error) {
	res, err := slf.DB(ctx).NewInsert().Model(record).
		Exec(ctx)
	if err != nil {
		return 0, err
	}
	return res.LastInsertId()
}

func (slf *userPaipanRecordRepository) PageListPaipanRecord(ctx context.Context, req *v1.PageListPaipanRecordRequest) (*v1.PageListPaipanRecordResponseData, error) {
	var records []*v1.PageListPaipanRecordResponseDataItem
	query := slf.DB(ctx).NewSelect().Model((*model.PaipanRecord)(nil)).
		ColumnExpr("upr.id").
		ColumnExpr("upr.user_id").
		ColumnExpr("upr.name").
		ColumnExpr("upr.gender").
		ColumnExpr("upr.birthtime").
		ColumnExpr("upr.birthtime_sun").
		ColumnExpr("upr.birthtime_lunar").
		ColumnExpr("upr.bazi").
		ColumnExpr("upr.birthplace").
		ColumnExpr("upr.type").
		ColumnExpr("upr.save_time").
		ColumnExpr("upr.user_agent").
		ColumnExpr("upr.app_id").
		ColumnExpr("upr.app_platform_id").
		ColumnExpr("upr.created_at").
		ColumnExpr("app.name as app_name").
		ColumnExpr("ap.name as app_platform_name").
		ColumnExpr("upr.extra_info").
		Join("left join app on app.id = upr.app_id").
		Join("left join app_platform ap on ap.id = upr.app_platform_id")

	if req.Param.User != nil && req.Param.Application != "" {
		query.
			Where("app.app = ?", req.Param.Application).
			Where("upr.user_id = ?", req.Param.User.UserID)
	}
	if req.Param.User != nil {
		query.Where("upr.user_id = ?", req.Param.User.UserID)
	}
	if req.Param.Application != "" {
		query.Where("app.app = ?", req.Param.Application)
	}

	if req.Param.Name != nil {
		query.Where("upr.name like concat('%', ?, '%')", *req.Param.Name)
	}
	if len(req.Param.Gender) > 0 {
		query.Where("upr.gender in (?)", bun.In(req.Param.Gender))
	}
	if req.Param.BirthTimeStart != nil {
		query.Where("upr.birthtime >= ?", *req.Param.BirthTimeStart)
	}
	if req.Param.BirthTimeEnd != nil {
		query.Where("upr.birthtime <= ?", *req.Param.BirthTimeEnd)
	}
	if req.Param.Bazi != nil {
		query.Where("REPLACE(JSON_UNQUOTE(upr.bazi), '\", \"', '') LIKE concat('%', ?, '%')", *req.Param.Bazi)
	}
	if req.Param.Birthplace != nil {
		query.Where("REPLACE(JSON_UNQUOTE(upr.birthplace), '\", \"', '') LIKE concat('%', ?, '%')", *req.Param.Birthplace)
	}
	if req.Param.BirthTimeSunStart != nil {
		query.Where("upr.birthtime_sun >= ?", *req.Param.BirthTimeSunStart)
	}
	if req.Param.BirthTimeSunEnd != nil {
		query.Where("upr.birthtime_sun <= ?", *req.Param.BirthTimeSunEnd)
	}
	if len(req.Param.AppIDs) > 0 {
		query.Where("upr.app_id in (?)", bun.In(req.Param.AppIDs))
	}
	count, err := query.Order("upr.created_at desc").Offset(req.Offset()).Limit(req.Limit()).ScanAndCount(ctx, &records)
	if err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	for _, record := range records {
		record.GenderStr = lo.Ternary(record.Gender == 1, "男", "女")
		if !record.Birthtime.IsZero() {
			record.BirthtimeStr = record.Birthtime.Format("2006-01-02 15:04:05")
		}
		if !record.BirthtimeSun.IsZero() {
			record.BirthtimeSunStr = record.BirthtimeSun.Format("2006-01-02 15:04:05")
		}
		if !record.SaveTime.IsZero() {
			record.SaveTimeStr = record.SaveTime.Format("2006-01-02 15:04:05")
		}
		if !record.CreatedAt.IsZero() {
			record.CreatedTime = record.CreatedAt.Format("2006-01-02 15:04:05")
		}
		switch record.Type {
		case 1:
			record.TypeStr = "即时起盘"
		}
	}
	return &v1.PageListPaipanRecordResponseData{
		Total: count,
		List:  records,
	}, nil
}
