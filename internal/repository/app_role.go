package repository

import (
	"context"
	"github.com/uptrace/bun"
	"zodiacus/internal/model"
)

// RoleRepository 角色仓库
type RoleRepository interface {
	// GetRolesByAppIDs 根据应用ID获取角色
	GetRolesByAppIDs(ctx context.Context, appIDs []int64) ([]*model.AppRole, error)
}

// NewRoleRepository 创建角色仓库
func NewRoleRepository(repo *Repository) RoleRepository {
	return &roleRepository{
		Repository: repo,
	}
}

type roleRepository struct {
	*Repository
}

// GetRolesByAppIDs 根据应用ID获取角色
func (slf *roleRepository) GetRolesByAppIDs(ctx context.Context, appIDs []int64) ([]*model.AppRole, error) {
	var roles []*model.AppRole
	if err := slf.DB(ctx).NewSelect().Model(&roles).Distinct().
		Join("left join app a on a.role = ar.id").
		Where("a.id in (?)", bun.In(appIDs)).Scan(ctx); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return roles, nil
}
