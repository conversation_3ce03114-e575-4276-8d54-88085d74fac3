package repository

import (
	"context"
	"zodiacus/internal/model"
)

type AppUserKefuRepository interface {
	FetchUserIdByAddState(ctx context.Context, addState string) (string, error)
	FetchAppUserQwKefu(ctx context.Context, userID string, hashed string) (*model.AppUserQwKefu, error)
	FetchQwKefuVip(ctx context.Context, userID string, contactWayID int64) (*model.AppUserQwKefu, error)
	CreateAppUserQwKefu(ctx context.Context, kefu *model.AppUserQwKefu) (int64, error)
}

func NewAppUserKefuRepository(repo *Repository) AppUserKefuRepository {
	return &appUserKefuRepository{Repository: repo}
}

type appUserKefuRepository struct {
	*Repository
}

func (slf *appUserKefuRepository) FetchUserIdByAddState(ctx context.Context, addState string) (string, error) {
	var userID string
	if err := slf.DB(ctx).NewSelect().Model(&model.AppUserQwKefu{}).
		ColumnExpr("user_id").
		Where("add_state = ?", addState).
		Scan(ctx, &userID); err != nil {
		if slf.NotFound(err) {
			return "", nil
		}
		return "", err
	}
	return userID, nil
}

func (slf *appUserKefuRepository) CreateAppUserQwKefu(ctx context.Context, kefu *model.AppUserQwKefu) (int64, error) {
	exec, err := slf.DB(ctx).NewInsert().Model(kefu).Exec(ctx)
	if err != nil {
		return 0, err
	}
	return exec.LastInsertId()
}

func (slf *appUserKefuRepository) FetchAppUserQwKefu(ctx context.Context, userID string, hashed string) (*model.AppUserQwKefu, error) {
	var kefu model.AppUserQwKefu
	if err := slf.DB(ctx).NewSelect().Model(&kefu).
		Where("user_id = ?", userID).
		Where("type = ?", 1). // 客服（联系我）
		Where("qw_user_ids_hashed = ?", hashed).
		Scan(ctx); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &kefu, nil
}

func (slf *appUserKefuRepository) FetchQwKefuVip(ctx context.Context, userID string, contactWayID int64) (*model.AppUserQwKefu, error) {
	var kefu model.AppUserQwKefu
	if err := slf.DB(ctx).NewSelect().Model(&kefu).
		Where("user_id = ?", userID).
		Where("qw_contact_way_id = ?", contactWayID).
		Where("type = ?", 2). // 会员专属客服（获客链接）
		Scan(ctx); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &kefu, nil
}
