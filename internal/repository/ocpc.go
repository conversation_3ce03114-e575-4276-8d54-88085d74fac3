package repository

import (
	"context"
	"github.com/uptrace/bun"
	"zodiacus/internal/model"
	"zodiacus/third_party/baidu/ocpc"
)

type OcpcRepository interface {
	UpdateOcpcClick(ctx context.Context, click *model.BaiduOcpcClick) error
	CreateOcpcClick(ctx context.Context, click *model.BaiduOcpcClick) (int64, error)
	FetchOcpcClickByAppUserID(ctx context.Context, appUserID string) (*model.BaiduOcpcClick, error)
	FetchOcpcClickByCbType(ctx context.Context, cbType ocpc.ConversionType, device map[string]string) (*model.BaiduOcpcClick, error)
}

func NewOcpcRepository(repo *Repository) OcpcRepository {
	return &ocpcRepository{Repository: repo}
}

type ocpcRepository struct {
	*Repository
}

func (slf *ocpcRepository) FetchOcpcClickByCbType(ctx context.Context, cbType ocpc.ConversionType, device map[string]string) (*model.BaiduOcpcClick, error) {
	var (
		click model.BaiduOcpcClick
		query = slf.DB(ctx).NewSelect().Model(&click)
	)
	switch cbType {
	case ocpc.ConversionTypeActivate:
		query.Where("cb_activate = ?", false)
	case ocpc.ConversionTypeRegister:
		query.Where("cb_register = ?", false)
	case ocpc.ConversionTypeOrders:
		query.Where("cb_orders = ?", false)
	default:
		return nil, nil
	}
	query.WhereGroup(" AND ", func(q1 *bun.SelectQuery) *bun.SelectQuery {
		if device["imei"] != "" {
			q1.WhereOr("imei_md5 = ?", device["imei"])
		}
		if device["oaid"] != "" {
			q1.WhereOr("oaid = ?", device["oaid"])
		}
		if device["android_id"] != "" {
			q1.WhereOr("android_id_md5 = ?", device["android_id"])
		}
		if device["idfa"] != "" {
			q1.WhereOr("idfa = ?", device["idfa"])
		}
		if device["mac"] != "" {
			q1.WhereOr("mac_md5 = ?", device["mac"])
		}
		if device["caid"] != "" {
			q1.WhereOr("caid = ?", device["caid"])
		}
		/*
			if device["ip"] != "" && device["os_type"] != "" && device["os_version"] != "" {
				q1.WhereOr("ip = ? AND os_type = ? AND os_version = ?", device["ip"], device["os_type"], device["os_version"])
			}
		*/
		if device["ip"] != "" && device["os_type"] != "" {
			q1.WhereOr("ip = ? AND os_type = ?", device["ip"], device["os_type"])
		}
		return q1
	})
	if err := query.Order("created_at desc").Limit(1).Scan(ctx, &click); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &click, nil
}

func (slf *ocpcRepository) UpdateOcpcClick(ctx context.Context, click *model.BaiduOcpcClick) error {
	_, err := slf.DB(ctx).NewUpdate().Model(click).WherePK().OmitZero().Exec(ctx)
	return err
}

func (slf *ocpcRepository) FetchOcpcClickByAppUserID(ctx context.Context, appUserID string) (*model.BaiduOcpcClick, error) {
	var click model.BaiduOcpcClick
	if err := slf.DB(ctx).NewSelect().Model(&click).
		Where("app_user_id = ?", appUserID).Scan(ctx); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &click, nil
}

func (slf *ocpcRepository) CreateOcpcClick(ctx context.Context, click *model.BaiduOcpcClick) (int64, error) {
	exec, err := slf.DB(ctx).NewInsert().Model(click).Exec(ctx)
	if err != nil {
		return 0, err
	}
	return exec.LastInsertId()
}
