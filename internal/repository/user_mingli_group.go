package repository

import (
	"context"
	"database/sql"
	"github.com/uptrace/bun"
	"zodiacus/internal/model"
)

type UserMingliGroupRepository interface {
	GetDefaultGroup(ctx context.Context, uid string, appid int64) (*model.UserMingliGroup, error)
	SetDefaultGroup(ctx context.Context, uid string, appid int64, id int64) error
	CreateGroup(ctx context.Context, group *model.UserMingliGroup) (int64, error)
	FetchGroup(ctx context.Context, uid string, appid int64) (*model.UserMingliGroup, error)
	FetchGroups(ctx context.Context, uid string, appid int64) ([]*model.UserMingliGroup, error)
	DeleteGroup(ctx context.Context, id int64, uid int64) error
	UpdateGroup(ctx context.Context, group *model.UserMingliGroup) error
}

func NewUserMingliGroupRepository(repo *Repository) UserMingliGroupRepository {
	return &userMingliGroupRepository{
		Repository: repo,
	}
}

type userMingliGroupRepository struct {
	*Repository
}

func (slf *userMingliGroupRepository) FetchGroups(ctx context.Context, uid string, appid int64) ([]*model.UserMingliGroup, error) {
	var groups []*model.UserMingliGroup
	if err := slf.DB(ctx).NewSelect().Model(&groups).
		Where("user_id = ?", uid).
		Where("app_id = ?", appid).
		Order("is_default desc").Order("created_at desc").Scan(ctx); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return groups, nil
}

func (slf *userMingliGroupRepository) FetchGroup(ctx context.Context, uid string, appid int64) (*model.UserMingliGroup, error) {
	var group model.UserMingliGroup
	if err := slf.DB(ctx).NewSelect().Model(&group).
		Where("user_id = ?", uid).
		Where("app_id = ?", appid).Scan(ctx); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &group, nil
}

func (slf *userMingliGroupRepository) SetDefaultGroup(ctx context.Context, uid string, appid int64, id int64) error {
	return slf.DB(ctx).RunInTx(ctx, &sql.TxOptions{}, func(ctx context.Context, tx bun.Tx) error {
		_, err := tx.NewUpdate().Model(&model.UserMingliGroup{}).
			Set("is_default = 0").
			Where("user_id = ?", uid).
			Where("app_id = ?", appid).
			Exec(ctx)
		if err != nil {
			return err
		}
		_, err = tx.NewUpdate().Model(&model.UserMingliGroup{}).
			Set("is_default = 1").
			Where("id = ?", id).
			Where("user_id = ?", uid).
			Exec(ctx)
		return err
	})
}

func (slf *userMingliGroupRepository) UpdateGroup(ctx context.Context, group *model.UserMingliGroup) error {
	_, err := slf.DB(ctx).NewUpdate().Model(group).Where("id = ?", group.ID).Where("user_id = ?", group.UserID).Exec(ctx)
	return err
}

func (slf *userMingliGroupRepository) DeleteGroup(ctx context.Context, id int64, uid int64) error {
	_, err := slf.DB(ctx).NewDelete().Model(&model.UserMingliGroup{}).Where("id = ?", id).Where("user_id = ?", uid).Exec(ctx)
	return err
}

func (slf *userMingliGroupRepository) CreateGroup(ctx context.Context, group *model.UserMingliGroup) (int64, error) {
	res, err := slf.DB(ctx).NewInsert().Model(group).Exec(ctx)
	if err != nil {
		return 0, err
	}
	return res.LastInsertId()
}

func (slf *userMingliGroupRepository) GetDefaultGroup(ctx context.Context, uid string, appid int64) (*model.UserMingliGroup, error) {
	var group model.UserMingliGroup
	if err := slf.DB(ctx).NewSelect().Model(&group).
		Where("user_id = ?", uid).
		Where("app_id = ?", appid).
		Where("is_default = 1").Scan(ctx); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &group, nil
}
