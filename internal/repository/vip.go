package repository

import (
	"context"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
)

type VIPRepository interface {
	GetVIPMemberInfo(ctx context.Context, uid, app string) (*model.MemberDuration, error)
	PageListVipMember(ctx context.Context, req *v1.PageListVipMemberRequest) (*v1.PageListVipMemberResponseData, error)
}

type vipRepository struct {
	*Repository
}

func NewVIPRepository(repo *Repository) VIPRepository {
	return &vipRepository{Repository: repo}
}

func (slf *vipRepository) GetVIPMemberInfo(ctx context.Context, app, uid string) (*model.MemberDuration, error) {
	var duration model.MemberDuration
	if err := slf.DB(ctx).NewSelect().Model(&duration).
		Join("left join app as app on app.role = md.role").
		Where("md.user_id = ?", uid).
		Where("app.app = ?", app).Scan(ctx); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &duration, nil
}

func (slf *vipRepository) PageListVipMember(ctx context.Context, req *v1.PageListVipMemberRequest) (*v1.PageListVipMemberResponseData, error) {
	//TODO implement me
	panic("implement me")
}
