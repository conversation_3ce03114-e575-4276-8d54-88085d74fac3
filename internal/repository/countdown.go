package repository

import (
	"context"
	"database/sql"
	"errors"
	"zodiacus/internal/model"
)

type CalendarCountdownDayRepository interface {
	CreateUserCountdownDay(ctx context.Context, countdownDay *model.CalendarUserCountdownDay) (int64, error)
	UpdateUserCountdownDay(ctx context.Context, countdownDay *model.CalendarUserCountdownDay) error
	DeleteUserCountdownDay(ctx context.Context, countdownDayID int64) error
	ListUserCountdownDay(ctx context.Context, userID string) ([]*model.CalendarUserCountdownDay, error)
	SetUserCountdownAsTop(ctx context.Context, id int64, userid string) error
	CancelUserCountdownAsTop(ctx context.Context, id int64, userid string) error
}

func NewCalendarCountdownDayRepository(
	repo *Repository,
) CalendarCountdownDayRepository {
	return &calendarCountdownDayRepository{
		Repository: repo,
	}
}

type calendarCountdownDayRepository struct {
	*Repository
}

func (slf *calendarCountdownDayRepository) CreateUserCountdownDay(ctx context.Context, countdownDay *model.CalendarUserCountdownDay) (int64, error) {
	exec, err := slf.DB(ctx).NewInsert().Model(countdownDay).Exec(ctx)
	if err != nil {
		return 0, err
	}
	return exec.LastInsertId()
}

func (slf *calendarCountdownDayRepository) UpdateUserCountdownDay(ctx context.Context, countdownDay *model.CalendarUserCountdownDay) error {
	if _, err := slf.DB(ctx).NewUpdate().Model(countdownDay).
		WherePK().
		Where("user_id = ?", countdownDay.UserID).
		OmitZero().Exec(ctx); err != nil {
		return err
	}
	return nil
}

func (slf *calendarCountdownDayRepository) DeleteUserCountdownDay(ctx context.Context, countdownDayID int64) error {
	if _, err := slf.DB(ctx).NewDelete().Model(&model.CalendarUserCountdownDay{ID: countdownDayID}).WherePK().Exec(ctx); err != nil {
		return err
	}
	return nil
}

func (slf *calendarCountdownDayRepository) ListUserCountdownDay(ctx context.Context, userID string) ([]*model.CalendarUserCountdownDay, error) {
	var items []*model.CalendarUserCountdownDay
	if err := slf.DB(ctx).NewSelect().
		Model(&items).
		Where("user_id = ?", userID).
		Order("is_top desc").
		Scan(ctx); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return items, nil
}

func (slf *calendarCountdownDayRepository) SetUserCountdownAsTop(ctx context.Context, id int64, userid string) error {
	if _, err := slf.DB(ctx).NewUpdate().Model(&model.CalendarUserCountdownDay{ID: id}).
		WherePK().Where("user_id = ?", userid).
		Set("is_top = ?", true).OmitZero().Exec(ctx); err != nil {
		return err
	}
	if _, err := slf.DB(ctx).NewUpdate().Model(&model.CalendarUserCountdownDay{}).
		Where("id != ?", id).
		Where("user_id = ?", userid).
		Set("is_top = ?", false).OmitZero().Exec(ctx); err != nil {
		return err
	}
	return nil
}

func (slf *calendarCountdownDayRepository) CancelUserCountdownAsTop(ctx context.Context, id int64, userid string) error {
	if _, err := slf.DB(ctx).NewUpdate().Model(&model.CalendarUserCountdownDay{ID: id}).
		WherePK().Where("user_id = ?", userid).
		Set("is_top = ?", false).OmitZero().Exec(ctx); err != nil {
		return err
	}
	return nil
}
