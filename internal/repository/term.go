package repository

import (
	"context"
	"database/sql"
	"zodiacus/internal/model"

	"github.com/pkg/errors"
)

type TermRepository interface {
	GetTermByName(ctx context.Context, name string) (*model.Term, error)
}

func NewTermRepository(
	repo *Repository,
) TermRepository {
	return &termRepository{
		Repository: repo,
	}
}

type termRepository struct {
	*Repository
}

func (slf *termRepository) GetTermByName(ctx context.Context, name string) (*model.Term, error) {
	var term model.Term
	if err := slf.DB(ctx).NewSelect().Model(&term).
		Where("name = ?", name).
		Scan(ctx); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &term, nil
}
