package repository

import (
	"context"
	v1 "zodiacus/api/v1"
	"zodiacus/internal/model"
)

type AppChannelRepository interface {
	CreateAppChannel(ctx context.Context, appChannel *model.AppChannel) (int64, error)
	UpdateAppChannel(ctx context.Context, appChannel *model.AppChannel) error
	FetchAppChannelByCode(ctx context.Context, code string) (*model.AppChannel, error)
	FetchAppChannelByID(ctx context.Context, id int64) (*model.AppChannel, error)
	PageListAppChannel(ctx context.Context, req *v1.PageListAppChannelRequest) (*v1.PageListAppChannelResponseData, error)
	CountAppChannelSignupNum(ctx context.Context, appChannelID int64) ([]*v1.ChannelAppStatistics, error)
}

func NewAppChannelRepository(repo *Repository) AppChannelRepository {
	return &appChannelRepository{Repository: repo}
}

type appChannelRepository struct {
	*Repository
}

func (slf *appChannelRepository) UpdateAppChannel(ctx context.Context, appChannel *model.AppChannel) error {
	_, err := slf.DB(ctx).NewUpdate().Model(appChannel).WherePK().OmitZero().Exec(ctx)
	return err
}

func (slf *appChannelRepository) FetchAppChannelByID(ctx context.Context, id int64) (*model.AppChannel, error) {
	var appChannel model.AppChannel
	if err := slf.DB(ctx).NewSelect().Model(&model.AppChannel{}).
		Where("id = ?", id).Scan(ctx, &appChannel); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &appChannel, nil
}

func (slf *appChannelRepository) CountAppChannelSignupNum(ctx context.Context, appChannelID int64) ([]*v1.ChannelAppStatistics, error) {
	var list []*v1.ChannelAppStatistics
	if err := slf.DB(ctx).NewSelect().Model(&model.App{}).
		ColumnExpr("app.id as app_id").
		ColumnExpr("app.name as app_name").
		ColumnExpr("count(au.id) as signup_num").
		Join("cross join app_channel ac").
		Join("left join app_user au on au.signup_application_id = app.id and au.channel_id = ac.id").
		Where("app.is_self = 1").
		Group("app.id").
		Group("app.name").
		Order("app.id desc").Scan(ctx, &list); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return list, nil
}

func (slf *appChannelRepository) PageListAppChannel(ctx context.Context, req *v1.PageListAppChannelRequest) (*v1.PageListAppChannelResponseData, error) {
	var list []*v1.PageListAppChannelResponseDataItem
	query := slf.DB(ctx).NewSelect().Model(&model.AppChannel{}).
		ColumnExpr("ac.id").
		ColumnExpr("ac.name").
		ColumnExpr("ac.remark").
		ColumnExpr("ac.code").
		ColumnExpr("date_format(ac.created_at,'%Y-%m-%d %H:%i:%s') as created_at")
	count, err := query.Offset(req.Offset()).Limit(req.Limit()).Order("ac.created_at desc").ScanAndCount(ctx, &list)
	if err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &v1.PageListAppChannelResponseData{
		Total: count,
		List:  list,
	}, nil
}

func (slf *appChannelRepository) CreateAppChannel(ctx context.Context, appChannel *model.AppChannel) (int64, error) {
	exec, err := slf.DB(ctx).NewInsert().Model(appChannel).Exec(ctx)
	if err != nil {
		return 0, err
	}
	return exec.LastInsertId()
}

func (slf *appChannelRepository) FetchAppChannelByCode(ctx context.Context, code string) (*model.AppChannel, error) {
	var appChannel model.AppChannel
	if err := slf.DB(ctx).NewSelect().Model(&model.AppChannel{}).
		Where("code = ?", code).Scan(ctx, &appChannel); err != nil {
		if slf.NotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return &appChannel, nil
}
