package v1

import (
	"time"
	"zodiacus/internal/model"
)

type (
	PageListUserOrderRequestParam struct {
		OrderNo          *string  `json:"orderNo,omitempty"`          // 订单ID
		UserID           []string `json:"userID,omitempty"`           // 用户ID
		ProductID        []int64  `json:"productID,omitempty"`        // 商品ID
		CreatedTimeStart *string  `json:"createdTimeStart,omitempty"` // 创建时间开始
		CreatedTimeEnd   *string  `json:"createdTimeEnd,omitempty"`   // 创建时间结束
		PayTimeStart     *string  `json:"payTimeStart,omitempty"`     // 支付时间开始
		PayTimeEnd       *string  `json:"payTimeEnd,omitempty"`       // 支付时间结束
		PayAmountMin     *int     `json:"payAmountMin,omitempty"`     // 支付金额最小值
		PayAmountMax     *int     `json:"payAmountMax,omitempty"`     // 支付金额最大值
		SkuName          *string  `json:"skuName,omitempty"`          // SKU名称
		PayStatus        []int    `json:"payStatus,omitempty"`        // 支付状态：0-待支付、1-已支付、2-支付失败、3-已退款
		UserName         *string  `json:"userName,omitempty"`         // 用户名
		UserPhone        *string  `json:"userPhone,omitempty"`        // 用户手机号
	}
	PageListUserOrderRequest = PagerIn[PageListUserOrderRequestParam]
	ProductSnapshot          struct {
		ID      int64  `json:"id"`               // ID
		Type    int    `json:"type"`             // 类型
		Stock   int    `json:"stock"`            // 库存
		Enable  int    `json:"enable"`           // 是否启用
		Remark  string `json:"remark,omitempty"` // 备注
		SkuCode string `json:"sku_code"`         // SKU编码
		SkuName string `json:"sku_name"`         // SKU名称
	}
	PageListUserOrderResponseDataItem struct {
		ID              int64           `json:"-" bun:"id"`                                   // ID
		OrderNo         string          `json:"orderNo" bun:"order_no"`                       // 订单号
		UserID          string          `json:"userId" bun:"user_id"`                         // 用户ID
		UserName        string          `json:"userName" bun:"username"`                      // 用户名
		UserDisplayName string          `json:"userDisplayName" bun:"nickname"`               // 用户昵称
		UserAvatar      string          `json:"userAvatar" bun:"user_avatar"`                 // 用户头像
		UserPhone       string          `json:"userPhone" bun:"user_phone"`                   // 用户手机号
		ProductID       int64           `json:"productId" bun:"product_id"`                   // 商品ID
		ProductSnapshot ProductSnapshot `json:"productSnapshot" bun:"product_snapshot"`       // 商品快照
		Quantity        int             `json:"quantity" bun:"quantity"`                      // 购买数量
		Amount          int             `json:"amount" bun:"amount"`                          // 订单金额
		PayAmount       int             `json:"payAmount" bun:"pay_amount"`                   // 实际支付金额
		PayChannel      int             `json:"payChannel" bun:"pay_channel"`                 // 支付渠道：1-微信、2-支付宝
		PayStatus       int             `json:"payStatus" bun:"pay_status"`                   // 支付状态：0-待支付、1-已支付、2-支付失败、3-已退款
		TransactionID   string          `json:"transactionId,omitempty" bun:"transaction_id"` // 交易流水号
		PayTime         time.Time       `json:"-" bun:"pay_time"`                             // 支付时间
		PayTimeStr      string          `json:"payTime,omitempty" bun:"-"`                    // 支付时间
		ExpireTime      time.Time       `json:"-" bun:"expire_time"`                          // 订单未支付失效时间
		ExpireTimeStr   string          `json:"expireTime,omitempty" bun:"-"`                 // 订单未支付失效时间
		CreatedTime     time.Time       `json:"-" bun:"created_at"`                           // 创建时间
		CreatedTimeStr  string          `json:"createdTime" bun:"-"`                          // 创建时间
		AppID           string          `json:"appId" bun:"app_id"`                           // app id
		AppName         string          `json:"appName" bun:"app_name"`                       // app name
		UA              string          `json:"ua" bun:"ua"`                                  // ua
		IP              string          `json:"ip" bun:"ip"`                                  // ip
		Source          int             `json:"source" bun:"source"`                          // 订单来源：0-未知，1-安卓应用，2-苹果应用，3-安卓h5，4-苹果h5
	}
	PageListUserOrderResponseData = PagerOut[*PageListUserOrderResponseDataItem]
	PageListUserOrderResponse     struct {
		Response
		Data *PageListUserOrderResponseData `json:"data"`
	}
)

type (
	OrderDetailRequest struct {
		OrderNo string `json:"orderNo" binding:"required"` // 订单编号
	}
	OrderDetailResponseData struct {
		model.Order
	}
	OrderDetailResponse struct {
		Response
		Data OrderDetailResponseData `json:"data"`
	}
)
