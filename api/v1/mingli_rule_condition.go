package v1

import "zodiacus/internal/model"

type (
	MingliRuleConditionZuodui = model.MingliRuleConditionKV
	MingliRuleConditionXiji   = model.MingliRuleConditionKV
)

type (
	CreateMingliRuleConditionRequest struct {
		MingliRuleID int64  `json:"mingliRuleId" binding:"required" example:"1"` // 命理规则ID
		No           string `json:"no" binding:"required" example:"条件编号"`        // 条件编号
		Name         string `json:"name" binding:"required" example:"条件名称"`      // 条件名称
		Category     int    `json:"category" example:"1"`                        // 类别：2-坐对、3-喜忌、1-全选
		Type         int64  `json:"type" example:"1"`                            // 条件类型（坐-对）
		Criterion    string `json:"criterion" example:"判断依据说明"`                  // 判断依据说明
		Gender       int    `json:"gender" example:"1"`                          // 日主性别（2-男、3-女、1-无关性别）
		WeizhiZuo    []int  `json:"weizhiZuo" example:"1,2,3"`                   // 位置坐（1-年、2-月、3-日、4-时、5-大运、6-流年、7-流月、8-流日、9-流时）
		WeizhiDui    []int  `json:"weizhiDui" example:"1,2,3"`                   // 位置对（1-年、2-月、3-日、4-时、5-大运、6-流年、7-流月、8-流日、9-流时）
	} // CreateMingliRuleConditionRequest 创建命理规则条件请求
	CreateMingliRuleConditionResponseData struct {
		ID int64 `json:"id" example:"1"` // 命理规则条件ID
	} // CreateMingliRuleConditionResponseData 创建命理规则条件响应数据
	CreateMingliRuleConditionResponse struct {
		Response
		Data CreateMingliRuleConditionResponseData `json:"data"`
	} // CreateMingliRuleConditionResponse 创建命理规则条件响应
)

type (
	UpdateMingliRuleConditionRequest struct {
		ID        int64  `json:"id" binding:"required" example:"1"`      // 命理规则条件ID
		No        string `json:"no" binding:"required" example:"条件编号"`   // 条件编号
		Name      string `json:"name" binding:"required" example:"条件名称"` // 条件名称
		Category  int    `json:"category" example:"1"`                   // 类别：2-坐对、3-喜忌、1-全选
		Type      int64  `json:"type" example:"1"`                       // 条件类型（坐-对）
		Criterion string `json:"criterion" example:"判断依据说明"`             // 判断依据说明
		Gender    int    `json:"gender" example:"1"`                     // 日主性别（2-男、3-女、1-无关性别）
		WeizhiZuo []int  `json:"weizhiZuo" example:"1,2,3"`              // 位置坐（1-年、2-月、3-日、4-时、5-大运、6-流年、7-流月、8-流日、9-流时）
		WeizhiDui []int  `json:"weizhiDui" example:"1,2,3"`              // 位置对（1-年、2-月、3-日、4-时、5-大运、6-流年、7-流月、8-流日、9-流时）
	}
	UpdateMingliRuleConditionResponse struct {
		Response
	}
)

type (
	GetMingliRuleConditionDetailRequest struct {
		ID int64 `json:"id" binding:"required" example:"1"` // 命理规则条件ID
	}
	GetMingliRuleConditionDetailResponseData struct {
		ID        int64                        `json:"id" example:"1"`                       // 主键ID
		No        string                       `json:"no" example:"条件编号"`                    // 条件编号
		Name      string                       `json:"name" example:"条件名称"`                  // 条件名称
		Category  int                          `json:"category,omitempty" example:"1"`       // 类别：2-坐对、3-喜忌、1-全选
		Type      int64                        `json:"type,omitempty" example:"1"`           // 条件类型（坐-对）
		Criterion string                       `json:"criterion,omitempty" example:"判断依据说明"` // 判断依据说明
		Gender    int                          `json:"gender,omitempty" example:"1"`         // 日主性别（2-男、3-女、1-无关性别）
		WeizhiZuo []int                        `json:"weizhiZuo,omitempty" example:"1,2,3"`  // 位置坐（1-年、2-月、3-日、4-时、5-大运、6-流年、7-流月、8-流日、9-流时）
		WeizhiDui []int                        `json:"weizhiDui,omitempty" example:"1,2,3"`  // 位置对（1-年、2-月、3-日、4-时、5-大运、6-流年、7-流月、8-流日、9-流时）
		Zuodui    []*MingliRuleConditionZuodui `json:"zuodui,omitempty"`                     // 坐对
		Xiji      []*MingliRuleConditionXiji   `json:"xiji,omitempty"`                       // 喜忌
	}
	GetMingliRuleConditionDetailResponse struct {
		Response
		Data GetMingliRuleConditionDetailResponseData `json:"data"`
	}
)

type (
	PageListMingliRuleConditionRequestParam struct {
		MingliRuleID int64 `json:"mingliRuleId" binding:"required" example:"1"` // 命理规则ID
	}
	PageListMingliRuleConditionRequest      = PagerIn[PageListMingliRuleConditionRequestParam]
	PageListMingliRuleConditionResponseItem struct {
		ID        int64                        `json:"id" bun:"id" example:"1"`                      // 主键ID
		No        string                       `json:"no" bun:"no" example:"条件编号"`                   // 条件编号
		Name      string                       `json:"name" bun:"name" example:"条件名称"`               // 条件名称
		Category  int                          `json:"category" bun:"category" example:"1"`          // 类别：2-坐对、3-喜忌、1-全选
		Type      int64                        `json:"type" bun:"type" example:"1"`                  // 条件类型（坐-对）
		Criterion string                       `json:"criterion" bun:"criterion" example:"判断依据说明"`   // 判断依据说明
		Gender    int                          `json:"gender" bun:"gender" example:"1"`              // 日主性别（2-男、3-女、1-无关性别）
		WeizhiZuo []int                        `json:"weizhiZuo" binding:"required" example:"1,2,3"` // 位置坐（1-年、2-月、3-日、4-时、5-大运、6-流年、7-流月、8-流日、9-流时）
		WeizhiDui []int                        `json:"weizhiDui" binding:"required" example:"1,2,3"` // 位置对（1-年、2-月、3-日、4-时、5-大运、6-流年、7-流月、8-流日、9-流时）
		Zuodui    []*MingliRuleConditionZuodui `json:"zuodui" bun:"zuodui"`                          // 坐对
		Xiji      []*MingliRuleConditionXiji   `json:"xiji" bun:"xiji"`                              // 喜忌
	}
	PageListMingliRuleConditionResponseData = PagerOut[PageListMingliRuleConditionResponseItem]
	PageListMingliRuleConditionResponse     struct {
		Response
		Data PageListMingliRuleConditionResponseData `json:"data"`
	}
)

type (
	DeleteMingliRuleConditionRequest struct {
		ID int64 `json:"id" binding:"required" example:"1"` // 命理规则条件ID
	}
	DeleteMingliRuleConditionResponse struct {
		Response
	}
)

type (
	SetMingliRuleConditionZuoduiRequest struct {
		ID     int64                        `json:"id" binding:"required" example:"1"` // 命理规则条件ID
		Zuodui []*MingliRuleConditionZuodui `json:"zuodui" binding:"required"`         // 坐对
	}
	SetMingliRuleConditionZuoduiResponse struct {
		Response
	}
)

type (
	SetMingliRuleConditionXijiRequest struct {
		ID   int64                      `json:"id" binding:"required" example:"1"` // 命理规则条件ID
		Xiji []*MingliRuleConditionXiji `json:"xiji" binding:"required"`           // 喜忌
	}
	SetMingliRuleConditionXijiResponse struct {
		Response
	}
)
