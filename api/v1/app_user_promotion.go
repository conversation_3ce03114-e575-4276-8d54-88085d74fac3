package v1

import (
	"time"
	"zodiacus/pkg/jwthub"
)

type (
	CreateAppUserPromotionRequest struct {
		Name        string `json:"name" binding:"required"`  // 渠道名称
		Phone       string `json:"phone" binding:"required"` // 渠道手机号
		Level1Ratio int    `json:"level1Ratio"`              // 一级返利比例
		Level2Ratio int    `json:"level2Ratio"`              // 二级返利比例
		Remark      string `json:"remark"`                   // 备注
	}
	CreateAppUserPromotionResponseData struct {
		ID int64 `json:"id"`
	}
	CreateAppUserPromotionResponse struct {
		Response
		Data *CreateAppUserPromotionResponseData `json:"data"`
	}
)

type (
	UpdateAppUserPromotionRequest struct {
		ID          int64   `json:"id" binding:"required"` // ID
		Name        *string `json:"name"`                  // 渠道名称
		Password    *string `json:"password"`              // 渠道密码
		Level1Ratio *int    `json:"level1Ratio"`           // 一级返利比例
		Level2Ratio *int    `json:"level2Ratio"`           // 二级返利比例
		Remark      *string `json:"remark"`                // 备注
	} // 不修改的字段传null
	UpdateAppUserPromotionResponse struct {
		Response
	}
)

type (
	PageListAppUserPromotionRequestParam     struct{}
	PageListAppUserPromotionRequest          = PagerIn[PageListAppUserPromotionRequestParam]
	PageListAppUserPromotionResponseDataItem struct {
		Name            string    `json:"name" bun:"nickname"`                    // 渠道名称
		DisplayName     string    `json:"displayName" bun:"-"`                    // 显示名称
		Phone           string    `json:"phone" bun:"phone"`                      // 渠道手机号
		Password        string    `json:"password" bun:"password"`                // 渠道密码
		PromotionCode   string    `json:"promotionCode" bun:"promotion_code"`     // 推广码（邀请码）
		Level           int       `json:"level" bun:"level"`                      // 渠道级别：1-推广渠道用户
		Level1Ratio     int       `json:"level1Ratio" bun:"level1_ratio"`         // 一级返利比例
		Level2Ratio     int       `json:"level2Ratio" bun:"level2_ratio"`         // 二级返利比例
		RebateWithdrawn int       `json:"rebateWithdrawn" bun:"rebate_withdrawn"` // 已提现返利
		Remark          string    `json:"remark" bun:"remark"`                    // 备注
		CreatedAt       time.Time `json:"createdAt" bun:"created_at"`             // 创建时间
		UpdatedAt       time.Time `json:"updatedAt" bun:"updated_at"`             // 更新时间
	}
	PageListAppUserPromotionResponseData = PagerOut[*PageListAppUserPromotionResponseDataItem]
	PageListAppUserPromotionResponse     struct {
		Response
		Data *PageListAppUserPromotionResponseData `json:"data"`
	}
)

type (
	PersonalPromotionRequest struct {
		User *jwthub.Auth `json:"-"`
	}
	PersonalPromotionResponseData struct {
		Name               string    `json:"name"`               // 渠道名称
		PromotionCode      string    `json:"promotionCode"`      // 推广码（邀请码）
		RebateTotal        int       `json:"rebateTotal"`        // 累计返利金额（总收益）（单位：分）
		RebateWithdrawn    int       `json:"rebateWithdrawn"`    // 已提现返利金额（单位：分）
		RebateAvailable    int       `json:"rebateAvailable"`    // 可提现返利金额（单位：分）
		Level1UserCount    int       `json:"level1UserCount"`    // 1级推广用户数
		Level1UserRecharge int       `json:"level1UserRecharge"` // 1级推广用户充值金额
		Level1Rebate       int       `json:"level1Rebate"`       // 1级推广用户返利金额（累计佣金）
		Level2UserCount    int       `json:"level2UserCount"`    // 2级推广用户数
		Level2UserRecharge int       `json:"level2UserRecharge"` // 2级推广用户充值金额
		Level2Rebate       int       `json:"level2Rebate"`       // 2级推广用户返利金额（累计佣金）
		StatTime           time.Time `json:"statTime"`           // 统计时间（截止时间）
	}
	PersonalPromotionResponse struct {
		Response
		Data *PersonalPromotionResponseData `json:"data"`
	}
)
