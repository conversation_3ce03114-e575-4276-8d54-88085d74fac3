package v1

import (
	"time"
	"zodiacus/pkg/jwthub"
)

type (
	GetInviteRewardUnreadPopupsRequest struct {
		AppClient
	}
	GetInviteRewardUnreadPopupsResponseDataItem struct {
		Type            string    `json:"type" example:"invitee_reward || inviter_reward"` // 弹窗类型：invitee_reward - 被邀请者奖励 / inviter_reward - 邀请人奖励
		InviteCode      string    `json:"inviteCode"`                                      // 邀请码
		InviteTime      time.Time `json:"inviteTime"`                                      // 邀请时间
		Invitee         string    `json:"invitee,omitempty"`                               // 被邀请人，type=inviter_reward 时返回
		Inviter         string    `json:"inviter,omitempty"`                               // 邀请人，type=invitee_reward 时返回
		GiftVipDuration int64     `json:"giftVipDuration"`                                 // 赠送会员时长（单位s）
	}
	GetInviteRewardUnreadPopupsResponseData = []*GetInviteRewardUnreadPopupsResponseDataItem
	GetInviteRewardUnreadPopupsResponse     struct {
		Response
		Data *GetInviteRewardUnreadPopupsResponseData `json:"data"`
	}
)

type (
	InviteBindingIPRequest struct {
		AppClient
	}
	InviteBindingIPResponse struct {
		Response
	}
)

type (
	GetInviteCodeRequest struct {
		User *jwthub.Auth `json:"-"`
	}
	GetInviteCodeResponseData struct {
		InviteCode string `json:"inviteCode"`
	}
	GetInvitationCodeResponse struct {
		Response
		Data *GetInviteCodeResponseData `json:"data"`
	}
)

type (
	AcceptInvitationRequest struct {
		InviteCode string       `json:"inviteCode" binding:"required"` // 邀请码
		User       *jwthub.Auth `json:"-"`
	}
	AcceptInvitationResponseData struct {
		IsNewVip        bool   `json:"isNewVip"`        // true-被邀请前不为会员，false-被邀请前已是会员
		GiftVipDuration int64  `json:"giftVipDuration"` // 赠送会员时长（单位s）
		VipExpiredAt    string `json:"vipExpiredAt"`    // 会员到期时间（yyyy-MM-dd HH:mm:ss）
	}
	AcceptInvitationResponse struct {
		Response
		Data *AcceptInvitationResponseData `json:"data"`
	}
)

type (
	GetInviteReferralsRequestParam struct {
		User *jwthub.Auth `json:"-"`
	}
	GetInviteReferralsRequest          = PagerIn[GetInviteReferralsRequestParam]
	GetInviteReferralsResponseDataItem struct {
		Invitee         string `json:"invitee" bun:"invitee"`                 // 被邀请人手机号加密
		GiftVipDuration int64  `json:"giftVipDuration" bun:"giftVipDuration"` // 当前账号赠送会员时长（单位s）
		InviteAt        string `json:"inviteAt" bun:"inviteAt"`               // 邀请时间（yyyy-MM-dd HH:mm:ss）
	}
	GetInviteReferralsResponseData = PagerOut[*GetInviteReferralsResponseDataItem]
	GetInviteReferralsResponse     struct {
		Response
		Data *GetInviteReferralsResponseData `json:"data"`
	}
)

type (
	GetInviteReferrerRequest struct {
		User *jwthub.Auth `json:"-"`
	}
	GetInviteReferrerResponseData struct {
		Inviter         string `json:"inviter"`         // 邀请人手机号加密
		GiftVipDuration int64  `json:"giftVipDuration"` // 当前账号赠送会员时长（单位s）
		InviteAt        string `json:"inviteAt"`        // 被邀请时间（yyyy-MM-dd HH:mm:ss）
	}
	GetInviteReferrerResponse struct {
		Response
		Data *GetInviteReferrerResponseData `json:"data"`
	}
)
