package v1

import (
	"zodiacus/pkg/jwthub"
	"zodiacus/third_party/corona"
)

type (
	Luncai<PERSON><PERSON><PERSON><PERSON> struct {
		Name           string            `json:"name"`           // 姓名
		Birthtime      string            `json:"birthtime"`      // 出生日期
		BirthtimeLunar string            `json:"birthtimeLunar"` // 农历生日
		Birthplace     []string          `json:"birthplace"`     // 出生地
		Gender         string            `json:"gender"`         // 性别
		Bazi           []string          `json:"bazi"`           // 八字
		Wuxing         []string          `json:"wuxing"`         // 五行：用神,喜神,忌神,仇神,闲神
		WuxingWxxqs    map[string]string `json:"wuxingWxxqs"`    // 五行旺相休囚死
		Riyuan         string            `json:"riyuan"`         // 日元
		Zodiac         string            `json:"zodiac"`         // 生肖
		Tiangan        []string          `json:"tiangan"`        // 天干，索引：0-年柱、1-月柱、2-日柱、3-时柱、4-大运、5-流年、6-流月、7-流日、8-流时
		TianganWuxing  []string          `json:"tianganWuxing"`  // 天干五行
		Dizhi          []string          `json:"dizhi"`          // 地支
		DizhiWuxing    []string          `json:"dizhiWuxing"`    // 地支五行
		DizhiShishen   []string          `json:"dizhiShishen"`   // 地支十神
		Zhuxing        []string          `json:"zhuxing"`        // 主星
		Benqi          []string          `json:"benqi"`          // 本气
		BenqiShishen   []string          `json:"benqiShishen"`   // 本气十神
		Zhongqi        []string          `json:"zhongqi"`        // 中气
		ZhongqiShishen []string          `json:"zhongqiShishen"` // 中气十神
		Yuqi           []string          `json:"yuqi"`           // 余气
		YuqiShishen    []string          `json:"yuqiShishen"`    // 余气十神
		ShenshaJishen  [][]string        `json:"shenshaJishen"`  // 神煞吉神
		Nayin          []string          `json:"nayin"`          // 纳音
		Qiyun          string            `json:"qiyun"`          // 命主起运
		Jiaoyun        string            `json:"jiaoyun"`        // 交运
		WuxingNeed     string            `json:"wuxingNeed"`     // 五行缺
	} // 命主信息
	LuncaiMingli struct {
		TongYi          []int                          `json:"tongYi"`          // 同异
		TongYiPercent   []float64                      `json:"tongYiPercent"`   // 同异百分比
		YingYang        []int                          `json:"yingYang"`        // 阴阳数量
		YingYangPercent []float64                      `json:"yingYangPercent"` // 阴阳百分比
		MapTransfer     *corona.PaipanBaziLiutongGraph `json:"graph"`           // 流通图
		LiuTongNum      int                            `json:"liuTongNum"`      // 流通个数
		ZuaiNum         int                            `json:"zuaiNum"`         // 阻碍个数
		Pingfen         string                         `json:"pingfen"`         // 评分
		Wangshuai       string                         `json:"wangshuai"`       // 旺衰
		Gejucankao      string                         `json:"gejucankao"`      // 格局参考
		Tiaohou         string                         `json:"tiaohou"`         // 调候
		Wuxing          []string                       `json:"wuxing"`          // 五行：用神,喜神,忌神,仇神,闲神
		Shishen         [][]string                     `json:"shishen"`         // 四柱十神
	} // 命理概述
	LuncaiNengliang   = []*corona.PaipanShishenPowerItem // 五行能量
	LuncaiBelongTable struct {
		Gongwei        [][]string `json:"gongwei"`       // 宫位
		Sizhu          []string   `json:"sizhu"`         // 四柱
		Tiangan        []string   `json:"tiangan"`       // 天干
		TianganCaiwei  []string   `json:"tianganCaiwei"` // 天干财位
		Dizhi          []string   `json:"dizhi"`         // 地支
		DizhiCaiwei    []string   `json:"dizhiCaiwei"`   // 地支财位
		LifeStage      []string   `json:"lifeStage"`     // 人生阶段
		Nianling       []string   `json:"nianling"`      // 年龄
		TianganShishen []string   `json:"-"`             // 天干十神
		DizhiShishen   []string   `json:"-"`             // 地支十神
		BenqiShishen   []string   `json:"-"`             // 本气十神
		ZhongqiShishen []string   `json:"-"`             // 中气十神
		YuqiShishen    []string   `json:"-"`             // 余气十神
	}
	LuncaiBelongDetailCangcai struct {
		TianganCaiNum int  `json:"tianganCaiNum"` // 天干财位个数
		DizhiCaiNum   int  `json:"dizhiCaiNum"`   // 地支财位个数
		Deling        bool `json:"deling"`        // 得令
		Shiling       bool `json:"shiling"`       // 失令
		AncangRuku    bool `json:"ancangRuku"`    // 是否暗藏入库
	}
	LuncaiBelongDetail struct {
		Cangcai     *LuncaiBelongDetailCangcai `json:"cangcai"`     // 命局藏财情况
		CaixingXiji string                     `json:"caixingXiji"` // 财星喜忌
		Opportunity string                     `json:"opportunity"` // 位置与时机
	}
	LuncaiBelong struct {
		Table  *LuncaiBelongTable  `json:"table"`  // 表
		Detail *LuncaiBelongDetail `json:"detail"` // 详情
	}
	LuncaiCareerPalaceYueling struct {
		Xiyong string   `json:"xiyong" example:"卯木"` // 喜用（地支与地支五行）
		Dizhi  []string `json:"dizhi"`               // 地支（三合或六合地支）
	}
	LuncaiCareerPalace struct {
		Middle  string                    `json:"middle"`  // 中（月支）
		Left    string                    `json:"left"`    // 左（三合地支1）
		Right   string                    `json:"right"`   // 右（三合地支2）
		Down    string                    `json:"down"`    // 下（六合地支）
		Shishen string                    `json:"shishen"` // 月支十神
		Yueling LuncaiCareerPalaceYueling `json:"yueling"` // 月令
	}
	LuncaiRisk struct {
		Shishen [][]string `json:"shishen"` // 四柱十神
		Detail  []string   `json:"detail"`  // 风险详情
	}
	LuncaiDancai struct {
		Dangyi         []int     `json:"dangyi"`
		DangyiPercent  []float64 `json:"dangyiPercent"`
		Wangshuai      string    `json:"wangshuai"`
		GejuCankao     string    `json:"gejuCankao"`
		Ability        string    `json:"ability"`
		Recommendation string    `json:"recommendation"`
	}
	LuncaiDayunliunian struct {
		StartYear  int      `json:"startYear"`  // 开始年份
		EndYear    int      `json:"endYear"`    // 结束年份
		ScoreList  []int    `json:"scoreList"`  // 评分列表
		DayunList  []string `json:"dayunList"`  // 大运列表
		YearList   []int    `json:"yearList"`   // 年份列表
		GanzhiList []string `json:"ganzhiList"` // 干支列表
	}
	LuncaiRequest struct {
		Birthtime    string   `json:"birthtime" binding:"required" example:"2006-01-02 15:04:05"` // 生日
		Gender       string   `json:"gender" binding:"required,oneof=男 女" example:"男"`
		Name         string   `json:"name" example:"张三"` // 姓名
		Birthplace   []string `json:"birthplace"`        // 出生地
		IgnoreRecord bool     `json:"ignoreRecord"`      // 是否忽略记录
		//VIP          *casdoor.VIP `json:"-"`
		User        *jwthub.Auth `json:"-"`
		UserAgent   string       `json:"-"`
		IP          string       `json:"-"`
		IsReplay    bool         `json:"-"`
		Application string       `json:"-"` // 应用ID
		DeviceID    string       `json:"-"` // 设备ID
		IsExample   bool         `json:"-"` // 是否示例
	}
	LuncaiResponseData struct {
		ID               int64               `json:"id,omitempty"`     // 返回记录ID（用于用户付费）
		Mingzhu          LuncaiMinzhu        `json:"mingzhu"`          // 命主信息
		Mingli           LuncaiMingli        `json:"mingli"`           // 命理
		Nengliang        LuncaiNengliang     `json:"nengliang"`        // 五行能量
		CareerSuggestion []string            `json:"careerSuggestion"` // 职业建议
		Belong           *LuncaiBelong       `json:"belong"`           // 财富所属
		CareerPalace     *LuncaiCareerPalace `json:"careerPalace"`     // 事业宫看财富贵人
		Caiyuan          []string            `json:"caiyuan"`          // 财源与求财意向
		Risk             *LuncaiRisk         `json:"risk"`             // 风险与偏好
		Dancai           *LuncaiDancai       `json:"dancai"`           // 担财能力
		Shensha          []string            `json:"shensha"`          // 神煞看财运
		Dayunliunian     *LuncaiDayunliunian `json:"dayunliunian"`     // 大运流年
		IsShow           bool                `json:"isShow"`           // 是否显示：true-显示，false-不显示
	}
	LuncaiResponse struct {
		Response
		Data LuncaiResponseData `json:"data"`
	}
)

type (
	LuncaiReplayRequest struct {
		ID     int64        `json:"id" binding:"required" example:"1"` // 记���ID
		UserID *jwthub.Auth `json:"-"`                                 // 用户
	}
	LuncaiReplayResponseData = LuncaiResponseData
	LuncaiReplayResponse     struct {
		Response
		Data LuncaiReplayResponseData `json:"data"`
	}
)
