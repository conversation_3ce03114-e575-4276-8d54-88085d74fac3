package v1

import "zodiacus/internal/model"

type (
	CreateAppChannelRequest struct {
		Name   string `json:"name" binding:"required"`
		Remark string `json:"remark" binding:"required"`
	}
	CreateAppChannelResponseData struct {
		ID int64 `json:"id"`
	}
	CreateAppChannelResponse struct {
		Response
		Data *CreateAppChannelResponseData `json:"data"`
	}
)

type (
	UpdateAppChannelRequest struct {
		ID     int64  `json:"id" binding:"required"`
		Name   string `json:"name" binding:"required"`
		Remark string `json:"remark" binding:"required"`
	}
	UpdateAppChannelResponse struct {
		Response
	}
)

type (
	PageListAppChannelRequestParam struct{}
	PageListAppChannelRequest      = PagerIn[PageListAppChannelRequestParam]
	ChannelAppStatistics           struct {
		AppID               int64           `json:"appID" bun:"app_id"`
		AppName             string          `json:"appName" bun:"app_name"`
		SignupNum           int             `json:"signupNum" bun:"signup_num"`
		EnableWxMiniProgram bool            `json:"enableWxMiniProgram" bun:"enable_wx_mp"`
		WxMiniProgramQrCode *model.MpQrcode `json:"wxMiniProgramQrCode" bun:"-"`
	}
	ChannelStatistics struct {
		SignupNum int                     `json:"signupNum" bun:"signup_num"`
		Apps      []*ChannelAppStatistics `json:"apps"`
	}
	PageListAppChannelResponseDataItem struct {
		ID         int64              `json:"id" bun:"id"`
		Name       string             `json:"name" bun:"name"`
		Remark     string             `json:"remark" bun:"remark"`
		Code       string             `json:"code" bun:"code"`
		Statistics *ChannelStatistics `json:"statistics" bun:"-"`
		CreatedAt  string             `json:"createdAt" bun:"created_at"`
	}
	PageListAppChannelResponseData = PagerOut[*PageListAppChannelResponseDataItem]
	PageListAppChannelResponse     struct {
		Response
		Data *PageListAppChannelResponseData `json:"data"`
	}
)
