package v1

type (
	PageListAppUserRequestParam struct {
		Name                 *string `json:"name"`
		Phone                *string `json:"phone"`
		RoleIDs              []int64 `json:"roleIDs"`
		SignupApplicationIDs []int64 `json:"signupApplicationIDs"`
		SignupTimeStart      *string `json:"signupTimeStart"`
		SignupTimeEnd        *string `json:"signupTimeEnd"`
	}
	PageListAppUserRequest = PagerIn[PageListAppUserRequestParam]
	AppUserMemberDuration  struct {
		RoleID       int64    `json:"roleID" bun:"role_id"`
		RoleName     string   `json:"roleName" bun:"role_name"`
		AppsName     []string `json:"appsName" bun:"-"`
		StartTime    string   `json:"startTime" bun:"start_time"`        // 开始时间
		ExpireTime   string   `json:"expireTime" bun:"expire_time"`      // 到期时间
		LastGainTime string   `json:"lastGainTime" bun:"last_gain_time"` // 最后一次获取时间
		LastGainType int      `json:"lastGainType" bun:"last_gain_type"` // 最后一次获取类型：0 购买，1 管理员赠送，2 注册赠送，3 app登录赠送
		IsExpired    bool     `json:"isExpired" bun:"is_expired"`        // 是否过期
	}
	PageListAppUserResponseDataItem struct {
		UserID                string                   `json:"user_id" bun:"user_id"`
		Name                  string                   `json:"name" bun:"username"`
		DisplayName           string                   `json:"displayName" bun:"nickname"`
		Avatar                string                   `json:"avatar" bun:"avatar"`
		Phone                 string                   `json:"phone" bun:"phone"`
		CountryCode           string                   `json:"countryCode" bun:"country_code"`
		IsPaid                bool                     `json:"isPaid" bun:"is_paid"` // 是否付费
		SignupApplication     string                   `json:"signupApplication" bun:"signup_application"`
		SignupApplicationID   int64                    `json:"signupApplicationID" bun:"signup_application_id"`
		SignupApplicationName string                   `json:"signupApplicationName" bun:"signup_application_name"`
		SignupTime            string                   `json:"signupTime" bun:"signup_time"`
		Channel               string                   `json:"channel" bun:"channel"`
		ChannelID             int64                    `json:"channelID" bun:"channel_id"`
		ChannelName           string                   `json:"channelName" bun:"channel_name"`
		IsMember              int                      `json:"isMember" bun:"-"` // 是否会员：0-否，1-是，2-曾是
		MemberDuration        []*AppUserMemberDuration `json:"memberDuration" bun:"-"`
		LastLoginTime         string                   `json:"lastLoginTime" bun:"-"`
		LastLoginApp          string                   `json:"lastLoginApp" bun:"-"`
	}
	PageListAppUserResponseData = PagerOut[*PageListAppUserResponseDataItem]
	PageListAppUserResponse     struct {
		Response
		Data PageListAppUserResponseData `json:"data"`
	}
)

type (
	PageListAppUserVipRequestParam struct {
		Name            *string `json:"name"`
		Phone           *string `json:"phone"`
		AppIDs          []int64 `json:"appIDs"`
		RoleIDs         []int64 `json:"roleIDs"`
		ExpireTimeStart *string `json:"expireTimeStart"`
		ExpireTimeEnd   *string `json:"expireTimeEnd"`
	}
	PageListAppUserVipRequest          = PagerIn[PageListAppUserVipRequestParam]
	PageListAppUserVipResponseDataItem struct {
		UserID                string `json:"userID" bun:"user_id"`
		Name                  string `json:"name" bun:"username"`
		DisplayName           string `json:"displayName" bun:"nickname"`
		Avatar                string `json:"avatar" bun:"avatar"`
		Phone                 string `json:"phone" bun:"phone"`
		CountryCode           string `json:"countryCode" bun:"country_code"`
		SignupApplication     string `json:"signupApplication" bun:"signup_application"`
		SignupApplicationID   int64  `json:"signupApplicationID" bun:"signup_application_id"`
		SignupApplicationName string `json:"signupApplicationName" bun:"signup_application_name"`
		SignupTime            string `json:"signupTime" bun:"signup_time"`
		Channel               string `json:"channel" bun:"channel"`
		ChannelID             int64  `json:"channelID" bun:"channel_id"`
		ChannelName           string `json:"channelName" bun:"channel_name"`
		RoleID                int64  `json:"roleID" bun:"role_id"`
		RoleName              string `json:"roleName" bun:"role_name"`
		ExpireTime            string `json:"expireTime" bun:"expire_time"`
	}
	PageListAppUserVipResponseData = PagerOut[*PageListAppUserVipResponseDataItem]
	PageListAppUserVipResponse     struct {
		Response
		Data PageListAppUserVipResponseData `json:"data"`
	}
)
