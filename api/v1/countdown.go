package v1

import "zodiacus/pkg/jwthub"

type UserCountdownDay struct {
	ID         int64  `json:"id" example:"1"`                           // ID
	Name       string `json:"name" example:"张三"`                        // 名称
	Type       int    `json:"type" example:"1"`                         // 类型：1-纪念日，2-生日，3-日程
	RemindTime string `json:"remindTime" example:"2021-01-01 12:00:00"` // 提醒时间
	RemindType int    `json:"remindType" example:"1"`                   // 提醒类型：0-不提醒，1-整点提醒，2-提前5分钟，3-提前10分钟，4-提前15分钟，5-提前30分钟，6-提前1小时，7-提前1天，8-提前3天，9-提前7天
	RepeatTime int    `json:"repeatTime" example:"1"`                   // 重复时间：0-不重复，1-每年，2-每月，3-每日，4-每时，5-法定节假日重复（智能跳过工作日），6-法定工作日重复（智能跳过节假日）
	IsTop      bool   `json:"isTop" example:"true"`                     // 是否置顶
}

type (
	CreateUserCountdownDayRequest struct {
		User       *jwthub.Auth `json:"-"`
		Name       string       `json:"name" example:"张三"`                        // 名称
		Type       int          `json:"type" example:"1"`                         // 类型：1-纪念日，2-生日，3-日程
		RemindTime string       `json:"remindTime" example:"2021-01-01 12:00:00"` // 提醒时间
		RemindType int          `json:"remindType" example:"1"`                   // 提醒类型：0-不提醒，1-整点提醒，2-提前5分钟，3-提前10分钟，4-提前15分钟，5-提前30分钟，6-提前1小时，7-提前1天，8-提前3天，9-提前7天
		RepeatTime int          `json:"repeatTime" example:"1"`                   // 重复时间：0-不重复，1-每年，2-每月，3-每日，4-每时，5-法定节假日重复（智能跳过工作日），6-法定工作日重复（智能跳过节假日）
		IsTop      bool         `json:"isTop" example:"true"`                     // 是否置顶
	}
	CreateUserCountdownDayResponseData struct {
		ID int64 `json:"id"`
	}
	CreateUserCountdownDayResponse struct {
		Response
		Data *CreateUserCountdownDayResponseData `json:"data"`
	}
)

type (
	UpdateUserCountdownDayRequest struct {
		User       *jwthub.Auth `json:"-"`
		ID         int64        `json:"id" example:"1"`                           // ID
		Name       string       `json:"name" example:"张三"`                        // 名称
		Type       int          `json:"type" example:"1"`                         // 类型：1-纪念日，2-生日，3-日程
		RemindTime string       `json:"remindTime" example:"2021-01-01 12:00:00"` // 提醒时间
		RemindType int          `json:"remindType" example:"1"`                   // 提醒类型：0-不提醒，1-整点提醒，2-提前5分钟，3-提前10分钟，4-提前15分钟，5-提前30分钟，6-提前1小时，7-提前1天，8-提前3天，9-提前7天
		RepeatTime int          `json:"repeatTime" example:"1"`                   // 重复时间：0-不重复，1-每年，2-每月，3-每日，4-每时，5-法定节假日重复（智能跳过工作日），6-法定工作日重复（智能跳过节假日）
		IsTop      bool         `json:"isTop" example:"true"`                     // 是否置顶
	}
	UpdateUserCountdownDayResponse struct {
		Response
	}
)

type (
	DeleteUserCountdownDayRequest struct {
		User *jwthub.Auth `json:"-"`
		ID   int64        `json:"id" binding:"required"`
	}
	DeleteUserCountdownDayResponse struct {
		Response
	}
)

type (
	ListUserCountdownDayRequest struct {
		User *jwthub.Auth `json:"-"`
	}
	ListUserCountdownDayResponseData = []*UserCountdownDay
	ListUserCountdownDayResponse     struct {
		Response
		Data *ListUserCountdownDayResponseData `json:"data"`
	}
)
