package v1

import "zodiacus/third_party/casdoor"

type (
	MingliBaziRequest struct {
		CurrentTime string        `json:"currentTime" binding:"required" example:"2006-01-02 15:04:05"` // 当前时间
		Birthtime   string        `json:"birthtime" binding:"required" example:"2006-01-02 15:04:05"`   // 生日
		Gender      string        `json:"gender" binding:"required,oneof=男 女" example:"男"`
		Name        string        `json:"name" example:"张三"` // 姓名
		Birthplace  []string      `json:"birthplace"`        // 出生地
		Scene       string        `json:"scene" example:"1"` // 场景
		VIP         *casdoor.VIP  `json:"-"`
		User        *casdoor.User `json:"-"`
		UserAgent   string        `json:"-"`
		IP          string        `json:"-"`
		IsReplay    bool          `json:"-"`
		IsPaid      bool          `json:"-"` // 是否已支付
		Application string        `json:"-"` // 应用ID
		DeviceID    string        `json:"-"` // 设备ID
		IsExample   bool          `json:"-"` // 是否示例
	}
	MingliBaziWuxingTiaohe struct {
		Pingheng  []string `json:"pingheng,omitempty"`  // 平衡
		Wangshuai []string `json:"wangshuai,omitempty"` // 旺衰
	}
	MingliBaziCurrentDayunWangshuai struct {
		Tiangan string `json:"tiangan"` // 天干
		Dizhi   string `json:"dizhi"`   // 地支
	}
	MingliBaziCurrentDayun struct {
		InDayun   bool                             `json:"inDayun"`             // 是否在大运中
		Dayun     string                           `json:"dayun"`               // 当前大运
		Wangshuai *MingliBaziCurrentDayunWangshuai `json:"wangshuai"`           // 旺衰
		Jiaoyun   string                           `json:"jiaoyun,omitempty"`   // 交运
		BestYear  [][]any                          `json:"bestYear,omitempty"`  // 最佳年份
		WorstYear [][]any                          `json:"worstYear,omitempty"` // 最差年份
	}
	MingliBaziResponseData struct {
		ID               int64                   `json:"id,omitempty"`     // 返回记录ID（用于用户付费）
		Liunian          string                  `json:"liunian"`          // 流年
		Mingzhu          LuncaiMinzhu            `json:"mingzhu"`          // 命主信息
		Xingge           string                  `json:"xingge"`           // 性格
		Mingli           LuncaiMingli            `json:"mingli"`           // 命理
		WuxingTiaohe     *MingliBaziWuxingTiaohe `json:"wuxingTiaohe"`     // 五行调和
		Nengliang        LuncaiNengliang         `json:"nengliang"`        // 五行能量
		CareerSuggestion []string                `json:"careerSuggestion"` // 职业建议
		Belong           *LuncaiBelong           `json:"belong"`           // 财富所属
		CareerPalace     *LuncaiCareerPalace     `json:"careerPalace"`     // 事业宫看财富贵人
		Caiyuan          []string                `json:"caiyuan"`          // 财源与求财意向
		Risk             *LuncaiRisk             `json:"risk"`             // 风险与偏好
		Dancai           *LuncaiDancai           `json:"dancai"`           // 担财能力
		Shensha          []string                `json:"shensha"`          // 神煞看财运
		Dayunliunian     *LuncaiDayunliunian     `json:"dayunliunian"`     // 大运流年
		CurrentDayun     *MingliBaziCurrentDayun `json:"currentDayun"`     // 当前大运
		Advice           []string                `json:"advice"`           // 建议
		IsShow           bool                    `json:"isShow"`           // 是否显示：true-显示，false-不显示
	}
	MingliBaziResponse struct {
		Response
		Data MingliBaziResponseData `json:"data"`
	}
)

type (
	MingliBaziReplayRequest struct {
		ID          int64         `json:"id" binding:"required" example:"1"`                            // 记录ID
		CurrentTime string        `json:"currentTime" binding:"required" example:"2006-01-02 15:04:05"` // 当前时间
		User        *casdoor.User `json:"-"`                                                            // 用户
		VIP         *casdoor.VIP  `json:"-"`
	}
	MingliBaziReplayResponseData = MingliBaziResponseData
	MingliBaziReplayResponse     struct {
		Response
		Data MingliBaziReplayResponseData `json:"data"`
	}
)

type (
	MingliBaziYearRequest struct {
		ID          int64    `json:"id" binding:"required" example:"1"`             // 记录ID
		CurrentYear int      `json:"currentYear" binding:"required" example:"2021"` // 当前年份
		Birthtime   string   `json:"-"`                                             // 生日
		Gender      string   `json:"-"`                                             // 性别
		Coefficient float32  `json:"-"`                                             // 系数
		Name        string   `json:"-"`                                             // 姓名
		Birthplace  []string `json:"-"`                                             // 出生地
	}
	MingliBaziYearResponseData = YunshiResponseData
	MingliBaziYearResponse     = YunshiResponse
)

type (
	MingliBaziQwLinkRequest struct {
		Button string `json:"btn" binding:"required" example:"00"` // 按钮ID
		ID     int64  `json:"id" binding:"required" example:"1"`   // 记录ID
		Phone  string `json:"phone"`                               // 手机号
		Type   int    `json:"type" binding:"required" example:"1"` // 类型：1-联系我、2-获客链接
	}
	MingliBaziQwLinkResponseData = string
	MingliBaziQwLinkResponse     struct {
		Response
		Data MingliBaziQwLinkResponseData `json:"data"`
	}
)

type (
	MingliBaziSMSRequest struct {
		ID    int64  `json:"id" binding:"required" example:"1"`              // 论财ID（排盘ID）
		Phone string `json:"phone" binding:"required" example:"13800138000"` // 手机号
	}
	MingliBaziSMSResponse struct {
		Response
	}
)
