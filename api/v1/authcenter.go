package v1

import "zodiacus/pkg/jwthub"

type (
	UserProfileInfo struct {
		ID          string `json:"id"`          // 用户唯一ID
		DisplayName string `json:"displayName"` // 昵称
	}
	UserPremiumInfo struct {
		IsPaid     bool  `json:"isPaid"`     // 是否付费
		ExpireTime int64 `json:"expireTime"` // 过期时间（秒级时间戳）
	}
	GetUserInfoRequest struct {
		User        *jwthub.Auth `json:"-"`
		Application string       `json:"-"`
	}
	GetUserInfoResponseData struct {
		UserProfileInfo
		UserPremiumInfo
	}
	GetUserInfoResponse struct {
		Response
		Data *GetUserInfoResponseData `json:"data"`
	}
)

type (
	GenImageCodeRequest struct {
		ApplicationId string `json:"applicationId" example:"admin/application_horoscope"` // 应用ID
	}
	GenImageCodeResponseData struct {
		CaptchaId    string `json:"captchaId"`
		CaptchaImage []byte `json:"captchaImage"`
	}
	GenImageCodeResponse struct {
		Response
		Data *GenImageCodeResponseData `json:"data"`
	}
)

type (
	SMSAuthRequest struct {
		CaptchaToken string `json:"captchaToken" example:"1234"`                 // 图片验证码code
		ClientSecret string `json:"clientSecret" example:"lYGEQkzDN92FX6x3BVXc"` // 图片验证码id
		Dest         string `json:"dest" example:"13069105206"`                  // 手机号
		Method       string `json:"method" example:"login"`                      // 方法：login-登录
		Application  string `json:"-"`                                           // 应用ID
	}
	SMSAuthResponse struct {
		Response
		Data string `json:"data"`
	}
)

type LoginResponseData struct {
	AccessToken  string `json:"accessToken"`  // 访问令牌
	RefreshToken string `json:"refreshToken"` // 刷新令牌（客户端并未使用）
	ExpiresIn    int64  `json:"expiresIn"`    // 过期时间
}

type (
	LoginByPhoneRequest struct {
		Username     string `json:"username" example:"13069105206"`              // 用户名
		Password     string `json:"password" example:"751581"`                   // 密码
		Application  string `json:"application" example:"application_horoscope"` // 应用ID
		Organization string `json:"organization" example:"organization_taisu"`   // 组织ID
		ClientId     string `json:"clientId" example:"2c027e9509c551e0be62"`     // 客户端ID
		AppClient
	}
	LoginByPhoneResponseData = LoginResponseData
	LoginByPhoneResponse     struct {
		Response
		Data *LoginByPhoneResponseData `json:"data"`
	}
)

type (
	LoginByPhoneQuickly4UniRequest struct {
		AccessToken string `json:"accessToken"` // 访问令牌
		OpenId      string `json:"openid"`      // 开放ID
		Application string `json:"application"` // 应用ID
		AppClient
	}
	LoginByPhoneQuickly4UniResponseData = LoginResponseData
	LoginByPhoneQuickly4UniResponse     struct {
		Response
		Data *LoginByPhoneQuickly4UniResponseData `json:"data"`
	}
)

type (
	LoginByWechatMiniProgramRequest struct {
		Code string `json:"code"` // 微信code
		AppClient
	}
	LoginByWechatMiniProgramResponseData = LoginResponseData
	LoginByWechatMiniProgramResponse     struct {
		Response
		Data *LoginByWechatMiniProgramResponseData `json:"data"`
	}
)

type (
	WechatMiniProgramJsCode2SessionRequest struct {
		JsCode      string       `json:"js_code"`     // 微信code
		Application string       `json:"application"` // 应用ID
		UserID      *jwthub.Auth `json:"-"`           // 用户ID
	}
	WechatMiniProgramJsCode2SessionResponse struct {
		Response
	}
)

type (
	WechatJsCode2SessionRequest struct {
		JsCode      string       `json:"js_code"`     // 微信code
		Application string       `json:"application"` // 应用ID
		Type        int          `json:"type"`        // 类型：1-小程序、2-服务号
		User        *jwthub.Auth `json:"-"`           // 用户ID
	}
	WechatJsCode2SessionResponseData struct {
		OpenId     string `json:"openid"`
		SessionKey string `json:"session_key"`
		UnionId    string `json:"unionid"`
	}
	WechatCode2SessionResponse struct {
		Response
		Data *WechatJsCode2SessionResponseData `json:"data"`
	}
)

type (
	DeactivateRequest struct {
		User *jwthub.Auth `json:"-"`
	}
	DeactivateResponseData struct {
		Success bool `json:"success"`
	}
	DeactivateResponse struct {
		Response
		Data *DeactivateResponseData `json:"data"`
	}
)

type (
	AdminLoginRequest struct {
		UserName string `json:"userName" binding:"required" example:"admin"`
		Password string `json:"password" binding:"required" example:"123456"`
	}
	AdminLoginResponseData = LoginResponseData
	AdminLoginResponse     struct {
		Response
		Data *AdminLoginResponseData `json:"data"`
	}
)

type (
	LoginByUMengMobileInfoRequest struct {
		VerifyID string `json:"verifyId" binding:"required"`
		Token    string `json:"token" binding:"required"`
		App      string `json:"app" binding:"required" example:"android || ios"`
		AppClient
	}
	LoginByUMengMobileInfoResponseData = LoginResponseData
	LoginByUMengMobileInfoResponse     struct {
		Response
		Data *LoginByUMengMobileInfoResponseData `json:"data"`
	}
)

type (
	LoginRequest struct {
		CaptchaID   string `json:"captchaId" binding:"required"`                // 图片验证码id
		CaptchaCode string `json:"captchaCode" binding:"required"`              // 图片验证码code
		Username    string `json:"username" binding:"required" example:"hello"` // 用户名
		Password    string `json:"password" binding:"required" example:"kitty"` // 密码
		AppClient
	}
	LoginResponse struct {
		Response
		Data *LoginResponseData `json:"data"`
	}
)

type (
	LogoutRequest struct {
		AppClient
	}
	LogoutResponse struct {
		Response
	}
)
