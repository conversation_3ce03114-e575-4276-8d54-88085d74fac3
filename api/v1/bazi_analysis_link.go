package v1

type (
	CreateBaziAnalysisLinkRequest struct {
		Name       string   `json:"name" binding:"required"`       // 姓名
		Gender     int      `json:"gender" binding:"required"`     // 性别：1-男、2-女
		Birthtime  string   `json:"birthtime" binding:"required"`  // 出生时间：2006-01-02 15:04:05
		Birthplace []string `json:"birthplace" binding:"required"` // 出生地点：省、市、区
		Phone      string   `json:"phone" binding:"required"`      // 手机号
		OrderID    int64    `json:"orderID" binding:"required"`    // 订单ID
		IP         string   `json:"ip"`                            // IP
		UA         string   `json:"ua"`                            // Auth-Agent
	}
	CreateBaziAnalysisLinkResponseData = string
	CreateBaziAnalysisLinkResponse     struct {
		Response
		Data CreateBaziAnalysisLinkResponseData `json:"data"`
	}
)
