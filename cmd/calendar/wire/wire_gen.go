// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/google/wire"
	"github.com/spf13/viper"
	"zodiacus/internal/handler"
	"zodiacus/internal/handler/calendar"
	"zodiacus/internal/repository"
	"zodiacus/internal/server"
	"zodiacus/internal/service"
	"zodiacus/pkg/app"
	"zodiacus/pkg/geoip"
	"zodiacus/pkg/jwthub"
	"zodiacus/pkg/log"
	"zodiacus/pkg/oss/aliyun"
	"zodiacus/pkg/server/http"
	"zodiacus/pkg/sid"
	"zodiacus/third_party/aliyun/sms"
	"zodiacus/third_party/casdoor"
	"zodiacus/third_party/corona"
)

// Injectors from wire.go:

func NewWire(viperViper *viper.Viper, logger *log.Logger) (*app.App, func(), error) {
	client := casdoor.NewClient(viperViper)
	baseHandler := handler.NewHandler(logger)
	db := repository.NewDB(viperViper, logger)
	redisClient := repository.NewRedis(viperViper)
	repositoryRepository := repository.NewRepository(logger, db, redisClient)
	transaction := repository.NewTransaction(repositoryRepository)
	sidSid := sid.NewSid()
	aliyun_smsClient := aliyun_sms.NewClient(viperViper, logger)
	coronaClient := corona.NewClient(viperViper, aliyun_smsClient, redisClient, logger)
	geoipClient := geoip.NewClient(viperViper)
	aliyun_ossClient := aliyun_oss.NewClient(viperViper)
	jwthubJwthub := jwthub.NewJwthub(viperViper, redisClient)
	serviceService := service.NewService(transaction, logger, sidSid, coronaClient, geoipClient, aliyun_ossClient, jwthubJwthub)
	dateRepository := repository.NewDateRepository(repositoryRepository)
	enumsRepository := repository.NewEnumsRepository(repositoryRepository)
	userMingliRepository := repository.NewUserMingliRepository(repositoryRepository)
	userMingliGroupRepository := repository.NewUserMingliGroupRepository(repositoryRepository)
	dateService := service.NewDateService(serviceService, dateRepository, enumsRepository, userMingliRepository, userMingliGroupRepository)
	userPaipanRecordRepository := repository.NewUserPaipanRecordRepository(repositoryRepository)
	userMingliService := service.NewUserMingliService(serviceService, userMingliRepository, dateRepository, userPaipanRecordRepository, userMingliGroupRepository)
	dateHandler := calendar.NewDateHandler(baseHandler, dateService, userMingliService)
	dateSubRepository := repository.NewDateSubRepository(repositoryRepository)
	dateSubService := service.NewDateSubService(serviceService, dateSubRepository)
	dateSubHandler := calendar.NewDateSubHandler(baseHandler, dateSubService)
	datetimeService := service.NewDatetimeService(serviceService)
	dateTimeHandler := calendar.NewDateTimeHandler(baseHandler, datetimeService)
	enumsService := service.NewEnumsService(serviceService, enumsRepository)
	enumsHandler := calendar.NewEnumsHandler(baseHandler, enumsService)
	historyTodayRepository := repository.NewHistoryTodayRepository(repositoryRepository)
	historyTodayService := service.NewHistoryTodayService(serviceService, historyTodayRepository)
	historyHandler := calendar.NewHistoryHandler(baseHandler, historyTodayService)
	jiemengService := service.NewJiemengService(serviceService, enumsRepository)
	jiemengHandler := calendar.NewJiemengHandler(baseHandler, jiemengService)
	userMingliHandler := calendar.NewUserMingliHandler(baseHandler, userMingliService)
	calendarCountdownDayRepository := repository.NewCalendarCountdownDayRepository(repositoryRepository)
	countdownDay := service.NewCountdownDayService(serviceService, calendarCountdownDayRepository)
	countdownDayHandler := calendar.NewCountdownDayHandler(baseHandler, countdownDay)
	appRepository := repository.NewAppRepository(repositoryRepository)
	appVersionRepository := repository.NewAppVersionRepository(repositoryRepository)
	appVersionService := service.NewAppVersionService(serviceService, appRepository, appVersionRepository)
	appVersionHandler := calendar.NewAppVersionHandler(baseHandler, appVersionService)
	httpServer := server.NewCalendarHTTPServer(logger, viperViper, client, dateHandler, dateSubHandler, dateTimeHandler, enumsHandler, historyHandler, jiemengHandler, userMingliHandler, countdownDayHandler, appVersionHandler, jwthubJwthub)
	job := server.NewJob(logger)
	appApp := newApp(httpServer, job)
	return appApp, func() {
	}, nil
}

// wire.go:

var repositorySet = wire.NewSet(repository.NewDB, repository.NewRedis, repository.NewRepository, repository.NewTransaction, repository.NewEnumsRepository, repository.NewDateRepository, repository.NewDateSubRepository, repository.NewUserMingliRepository, repository.NewHistoryTodayRepository, repository.NewCalendarCountdownDayRepository, repository.NewUserPaipanRecordRepository, repository.NewUserMingliGroupRepository, repository.NewAppVersionRepository, repository.NewAppRepository)

var serviceSet = wire.NewSet(service.NewService, service.NewDateService, service.NewDateSubService, service.NewDatetimeService, service.NewUserMingliService, service.NewEnumsService, service.NewHistoryTodayService, service.NewJiemengService, service.NewCountdownDayService, service.NewAppVersionService)

var handlerSet = wire.NewSet(handler.NewHandler, calendar.NewDateHandler, calendar.NewDateSubHandler, calendar.NewDateTimeHandler, calendar.NewEnumsHandler, calendar.NewHistoryHandler, calendar.NewJiemengHandler, calendar.NewUserMingliHandler, calendar.NewCountdownDayHandler, calendar.NewAppVersionHandler)

var serverSet = wire.NewSet(server.NewCalendarHTTPServer, server.NewJob)

// build App
func newApp(
	httpServer *http.Server,
	job *server.Job,

) *app.App {
	return app.NewApp(app.WithServer(httpServer, job), app.WithName("calendar-app-api"))
}
