package main

import (
	"context"
	"flag"
	"time"
	"zodiacus/config"
	"zodiacus/internal/repository"
	"zodiacus/pkg/log"
	"zodiacus/pkg/ocpcb"
	"zodiacus/third_party/baidu/ocpc"
)

func main() {
	var envConf = flag.String("conf", "etc/local.yml", "config path, eg: -conf ./etc/local.yml")
	flag.Parse()
	config.AppName = "local"
	conf := config.NewConfig(*envConf)
	var (
		logger   = log.NewLogger(conf)
		db       = repository.NewDB(conf, logger)
		rdb      = repository.NewRedis(conf)
		ocpcRepo = repository.NewOcpcRepository(repository.NewRepository(logger, db, rdb))
		ocpcli   = ocpcb.NewOcpcb(logger, ocpcRepo)
	)
	doCbActivate(ocpcli)
	doCbRegister(ocpcli)
	doCbOrders(ocpcli)
}

func doCbActivate(ocpcli *ocpcb.Ocpcb) {
	if err := ocpcli.CbActivate(context.TODO(), &ocpcb.CbActivateRequest{
		CbType:    ocpc.ConversionTypeActivate,
		CbValue:   0,
		AppUserID: "",
		Imei:      "",
		Oaid:      "",
		AndroidID: "",
		IP:        "***************",
		OsType:    "2",
		OsVersion: "",
		Idfa:      "",
		Caid:      "",
		Mac:       "",
		CbTime:    time.Now(),
	}); err != nil {
		panic(err)
	}
}

func doCbRegister(ocpcli *ocpcb.Ocpcb) {
	if err := ocpcli.CbActivate(context.TODO(), &ocpcb.CbActivateRequest{
		CbType:    ocpc.ConversionTypeRegister,
		CbValue:   0,
		AppUserID: "",
		Imei:      "",
		Oaid:      "",
		AndroidID: "",
		IP:        "***************",
		OsType:    "2",
		OsVersion: "",
		Idfa:      "",
		Caid:      "",
		Mac:       "",
		CbTime:    time.Now(),
	}); err != nil {
		panic(err)
	}
}

func doCbOrders(ocpcli *ocpcb.Ocpcb) {
	if err := ocpcli.CbActivate(context.TODO(), &ocpcb.CbActivateRequest{
		CbType:    ocpc.ConversionTypeOrders,
		CbValue:   1980,
		AppUserID: "",
		Imei:      "",
		Oaid:      "",
		AndroidID: "",
		IP:        "***************",
		OsType:    "2",
		OsVersion: "",
		Idfa:      "",
		Caid:      "",
		Mac:       "",
		CbTime:    time.Now(),
	}); err != nil {
		panic(err)
	}
}
