// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/google/wire"
	"github.com/spf13/viper"
	"zodiacus/internal/handler"
	"zodiacus/internal/handler/wealth"
	"zodiacus/internal/repository"
	"zodiacus/internal/server"
	"zodiacus/internal/service"
	"zodiacus/pkg/app"
	"zodiacus/pkg/geoip"
	"zodiacus/pkg/jwthub"
	"zodiacus/pkg/log"
	"zodiacus/pkg/oss/aliyun"
	"zodiacus/pkg/server/http"
	"zodiacus/pkg/sid"
	"zodiacus/third_party/aliyun/sms"
	"zodiacus/third_party/casdoor"
	"zodiacus/third_party/corona"
)

// Injectors from wire.go:

func NewWire(viperViper *viper.Viper, logger *log.Logger) (*app.App, func(), error) {
	client := casdoor.NewClient(viperViper)
	baseHandler := handler.NewHandler(logger)
	db := repository.NewDB(viperViper, logger)
	redisClient := repository.NewRedis(viperViper)
	repositoryRepository := repository.NewRepository(logger, db, redisClient)
	transaction := repository.NewTransaction(repositoryRepository)
	sidSid := sid.NewSid()
	aliyun_smsClient := aliyun_sms.NewClient(viperViper, logger)
	coronaClient := corona.NewClient(viperViper, aliyun_smsClient, redisClient, logger)
	geoipClient := geoip.NewClient(viperViper)
	aliyun_ossClient := aliyun_oss.NewClient(viperViper)
	jwthubJwthub := jwthub.NewJwthub(viperViper, redisClient)
	serviceService := service.NewService(transaction, logger, sidSid, coronaClient, geoipClient, aliyun_ossClient, jwthubJwthub)
	datetimeService := service.NewDatetimeService(serviceService)
	dateTimeHandler := wealth.NewDateTimeHandler(baseHandler, datetimeService)
	luncaiRepository := repository.NewLuncaiRepository(repositoryRepository)
	userPaipanRecordRepository := repository.NewUserPaipanRecordRepository(repositoryRepository)
	userOrderRepository := repository.NewUserOrderRepository(repositoryRepository)
	vipRepository := repository.NewVIPRepository(repositoryRepository)
	luncaiService := service.NewLuncaiService(serviceService, luncaiRepository, userPaipanRecordRepository, userOrderRepository, vipRepository)
	luncaiHandler := wealth.NewLuncaiHandler(baseHandler, luncaiService)
	enumsRepository := repository.NewEnumsRepository(repositoryRepository)
	locationService := service.NewLocationService(serviceService, enumsRepository)
	locationHandler := wealth.NewLocationHandler(baseHandler, locationService)
	enumsService := service.NewEnumsService(serviceService, enumsRepository)
	enumsHandler := wealth.NewEnumsHandler(baseHandler, enumsService)
	paipanRecordService := service.NewPaipanRecordService(serviceService, userPaipanRecordRepository)
	paipanRecordHandler := wealth.NewPaipanRecordHandler(baseHandler, paipanRecordService)
	dateRepository := repository.NewDateRepository(repositoryRepository)
	userMingliRepository := repository.NewUserMingliRepository(repositoryRepository)
	userMingliGroupRepository := repository.NewUserMingliGroupRepository(repositoryRepository)
	dateService := service.NewDateService(serviceService, dateRepository, enumsRepository, userMingliRepository, userMingliGroupRepository)
	dateHandler := wealth.NewDateHandler(baseHandler, dateService)
	appRepository := repository.NewAppRepository(repositoryRepository)
	appVersionRepository := repository.NewAppVersionRepository(repositoryRepository)
	appVersionService := service.NewAppVersionService(serviceService, appRepository, appVersionRepository)
	appVersionHandler := wealth.NewAppVersionHandler(baseHandler, appVersionService)
	httpServer := server.NewWealthHTTPServer(logger, viperViper, client, dateTimeHandler, luncaiHandler, locationHandler, enumsHandler, paipanRecordHandler, vipRepository, dateHandler, appVersionHandler, jwthubJwthub)
	job := server.NewJob(logger)
	appApp := newApp(httpServer, job)
	return appApp, func() {
	}, nil
}

// wire.go:

var repositorySet = wire.NewSet(repository.NewDB, repository.NewRedis, repository.NewRepository, repository.NewTransaction, repository.NewLuncaiRepository, repository.NewEnumsRepository, repository.NewUserPaipanRecordRepository, repository.NewVIPRepository, repository.NewUserOrderRepository, repository.NewDateRepository, repository.NewUserMingliRepository, repository.NewUserMingliGroupRepository, repository.NewAppVersionRepository, repository.NewAppRepository)

var serviceSet = wire.NewSet(service.NewService, service.NewLuncaiService, service.NewLocationService, service.NewEnumsService, service.NewDatetimeService, service.NewPaipanRecordService, service.NewDateService, service.NewAppVersionService)

var handlerSet = wire.NewSet(handler.NewHandler, wealth.NewLuncaiHandler, wealth.NewDateTimeHandler, wealth.NewLocationHandler, wealth.NewEnumsHandler, wealth.NewPaipanRecordHandler, wealth.NewDateHandler, wealth.NewAppVersionHandler)

var serverSet = wire.NewSet(server.NewWealthHTTPServer, server.NewJob)

// build App
func newApp(
	httpServer *http.Server,
	job *server.Job,

) *app.App {
	return app.NewApp(app.WithServer(httpServer, job), app.WithName("luncai-app-api"))
}
