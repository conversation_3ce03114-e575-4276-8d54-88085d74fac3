package main

import (
	"context"
	"flag"
	"fmt"
	"github.com/ua-parser/uap-go/uaparser"
	"zodiacus/config"
	"zodiacus/internal/model"
	"zodiacus/internal/repository"
	"zodiacus/pkg/log"
)

type Order struct {
	UserID    string
	Phone     string
	OrderNo   string
	PayAmount int
	OS        string
	Device    string
}

func main() {
	var envConf = flag.String("conf", "etc/local.yml", "config path, eg: -conf ./etc/local.yml")
	flag.Parse()
	config.AppName = "local"
	conf := config.NewConfig(*envConf)
	var (
		logger = log.NewLogger(conf)
		db     = repository.NewDB(conf, logger)
		//rdb    = repository.NewRedis(conf)
	)
	var orders []*model.Order
	if err := db.NewSelect().Model(&model.Order{}).Where("pay_status = 1").Where("created_at > '2025-07-02 00:00:00'").Scan(context.Background(), &orders); err != nil {
		panic(err)
	}
	var userIDs []string
	parser := uaparser.NewFromSaved()
	for _, order := range orders {
		result := parser.Parse(order.UA)
		fmt.Println(order.UserID, order.OrderNo, order.PayAmount, result.Os.ToString(), result.Device.ToString())
	}

}
