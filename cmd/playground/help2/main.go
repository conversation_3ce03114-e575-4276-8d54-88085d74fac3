package main

import (
	"context"
	"flag"
	"go.uber.org/zap"
	"zodiacus/config"
	"zodiacus/internal/repository"
	"zodiacus/pkg/log"
)

func main() {
	var envConf = flag.String("conf", "etc/local.yml", "config path, eg: -conf ./etc/local.yml")
	flag.Parse()
	config.AppName = "local"
	conf := config.NewConfig(*envConf)
	var (
		logger     = log.NewLogger(conf)
		db         = repository.NewDB(conf, logger)
		rdb        = repository.NewRedis(conf)
		inviteRepo = repository.NewAppUserInvitationRepository(repository.NewRepository(logger, db, rdb))
	)
	hasInvited, err := inviteRepo.IsThisPhoneHasInvited(context.TODO(), "13175058107")
	if err != nil {
		logger.Error("err", zap.Any("err", err))
	}
	logger.Info("hasInvited", zap.Any("hasInvited", hasInvited))
}
