package main

import (
	"context"
	"flag"
	"fmt"
	"go.uber.org/zap"
	"zodiacus/cmd/authcenter/wire"
	"zodiacus/config"
	"zodiacus/pkg/log"
)

// @title           AuthCenter API
// @version         1.0.0
// @description     This is a http server template.
// @termsOfService  http://swagger.io/terms/
// @contact.name   API Support
// @contact.url    http://www.swagger.io/support
// @contact.email  <EMAIL>
// @license.name  Apache 2.0
// @license.url   http://www.apache.org/licenses/LICENSE-2.0.html
// @securityDefinitions.apiKey BearerAuth
// @in header
// @name Authorization
// @externalDocs.description  OpenAPI
// @externalDocs.url          https://swagger.io/resources/open-api/
func main() {
	var envConf = flag.String("conf", "etc/authcenter.yaml", "config path, eg: -conf ./etc/local.yml")
	flag.Parse()
	config.AppName = "authcenter"
	conf := config.NewConfig(*envConf)

	logger := log.NewLogger(conf)

	app, cleanup, err := wire.NewWire(conf, logger)
	defer cleanup()
	if err != nil {
		panic(err)
	}
	host, port := conf.GetString("http.authcenter.host"), conf.GetInt("http.authcenter.port")
	logger.Info("server started", zap.String("env", config.ENV),
		zap.String("address", fmt.Sprintf("http://%s:%d", host, port)))
	logger.Info("swagger docs", zap.String("address", fmt.Sprintf("http://%s:%d/swagger/index.html", host, port)))
	if err = app.Run(context.Background()); err != nil {
		panic(err)
	}
}
