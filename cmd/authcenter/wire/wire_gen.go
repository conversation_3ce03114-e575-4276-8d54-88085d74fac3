// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/google/wire"
	"github.com/spf13/viper"
	"zodiacus/internal/handler"
	"zodiacus/internal/handler/authcenter"
	"zodiacus/internal/repository"
	"zodiacus/internal/server"
	"zodiacus/internal/service"
	"zodiacus/pkg/app"
	"zodiacus/pkg/geoip"
	"zodiacus/pkg/jwthub"
	"zodiacus/pkg/log"
	"zodiacus/pkg/ocpcb"
	"zodiacus/pkg/oss/aliyun"
	"zodiacus/pkg/server/http"
	"zodiacus/pkg/sid"
	"zodiacus/third_party/aliyun/sms"
	"zodiacus/third_party/casdoor"
	"zodiacus/third_party/corona"
	"zodiacus/third_party/submail"
	"zodiacus/third_party/umeng"
	"zodiacus/third_party/uniapp"
	"zodiacus/third_party/wechat"
	"zodiacus/third_party/wecom"
)

// Injectors from wire.go:

func NewWire(viperViper *viper.Viper, logger *log.Logger) (*app.App, func(), error) {
	baseHandler := handler.NewHandler(logger)
	db := repository.NewDB(viperViper, logger)
	client := repository.NewRedis(viperViper)
	repositoryRepository := repository.NewRepository(logger, db, client)
	transaction := repository.NewTransaction(repositoryRepository)
	sidSid := sid.NewSid()
	aliyun_smsClient := aliyun_sms.NewClient(viperViper, logger)
	coronaClient := corona.NewClient(viperViper, aliyun_smsClient, client, logger)
	geoipClient := geoip.NewClient(viperViper)
	aliyun_ossClient := aliyun_oss.NewClient(viperViper)
	jwthubJwthub := jwthub.NewJwthub(viperViper, client)
	serviceService := service.NewService(transaction, logger, sidSid, coronaClient, geoipClient, aliyun_ossClient, jwthubJwthub)
	appUserRepository := repository.NewAppUserRepository(repositoryRepository)
	adminUserRepository := repository.NewAdminUserRepository(repositoryRepository)
	authRepository := repository.NewAuthRepository(repositoryRepository)
	userOrderRepository := repository.NewUserOrderRepository(repositoryRepository)
	appUserInvitationRepository := repository.NewAppUserInvitationRepository(repositoryRepository)
	work := wecom.NewDoraemon(viperViper, logger)
	submailClient := submail.NewClient(viperViper)
	uniappClient := uniapp.NewClient(viperViper)
	umengClient := umeng.NewClient(viperViper)
	officialAccount := wechat_app.NewOfficialAccount(viperViper, logger)
	miniprogram := wechat_app.NewMiniProgram(viperViper, logger)
	ocpcRepository := repository.NewOcpcRepository(repositoryRepository)
	ocpcbOcpcb := ocpcb.NewOcpcb(logger, ocpcRepository)
	authService := service.NewAuthService(serviceService, appUserRepository, adminUserRepository, authRepository, userOrderRepository, appUserInvitationRepository, work, aliyun_smsClient, submailClient, uniappClient, umengClient, officialAccount, miniprogram, ocpcbOcpcb)
	authCenterHandler := authcenter.NewAuthHandler(baseHandler, authService)
	casdoorClient := casdoor.NewClient(viperViper)
	httpServer := server.NewAuthCenterServer(logger, viperViper, authCenterHandler, casdoorClient, jwthubJwthub)
	job := server.NewJob(logger)
	task := server.NewTask(logger)
	appApp := newApp(httpServer, job, task)
	return appApp, func() {
	}, nil
}

// wire.go:

var repositorySet = wire.NewSet(repository.NewDB, repository.NewRedis, repository.NewRepository, repository.NewTransaction, repository.NewAppUserRepository, repository.NewAuthRepository, repository.NewUserOrderRepository, repository.NewAdminUserRepository, repository.NewAppUserInvitationRepository, repository.NewOcpcRepository)

var serviceSet = wire.NewSet(service.NewService, service.NewAppUserService, service.NewAuthService)

var handlerSet = wire.NewSet(handler.NewHandler, authcenter.NewAuthHandler)

var serverSet = wire.NewSet(server.NewAuthCenterServer, server.NewJob)

// build App
func newApp(
	httpServer *http.Server,
	job *server.Job,
	task *server.Task,
) *app.App {
	return app.NewApp(app.WithServer(httpServer, job, task), app.WithName("authcenter-server"))
}
