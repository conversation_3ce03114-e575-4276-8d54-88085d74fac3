package main

import (
	"encoding/json"
	"log"
	"os"
	"regexp"
	"strings"

	"github.com/xuri/excelize/v2"
)

type GaokaoShishenStage struct {
	Shishen string `json:"shishen"` // 十神
	Stage   int    `json:"stage"`   // 阶段编号
	Type    string `json:"type"`    // 类型
	Content string `json:"content"` // 内容
}

func parseStageNumber(stage string) int {
	// 提取“第一阶段”中的数字
	stageMap := map[string]int{
		"第一阶段": 1,
		"第二阶段": 2,
		"第三阶段": 3,
		"第四阶段": 4,
	}
	if val, ok := stageMap[stage]; ok {
		return val
	}
	// 兜底：匹配中文数字
	re := regexp.MustCompile(`第(.+)阶段`)
	matches := re.FindStringSubmatch(stage)
	if len(matches) > 1 {
		chineseNums := map[string]int{"一": 1, "二": 2, "三": 3, "四": 4}
		if n, ok := chineseNums[matches[1]]; ok {
			return n
		}
	}
	return 0
}

func readExcelToStructs(filepath string) ([]GaokaoShishenStage, error) {
	f, err := excelize.OpenFile(filepath)
	if err != nil {
		return nil, err
	}
	defer f.Close()
	sheetName := f.GetSheetName(0)
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return nil, err
	}
	var result []GaokaoShishenStage
	for i, row := range rows {
		if i == 0 {
			continue
		}
		if len(row) < 4 {
			continue
		}
		stage := GaokaoShishenStage{
			Shishen: strings.TrimSpace(row[0]),
			Stage:   parseStageNumber(strings.TrimSpace(row[1])),
			Type:    strings.TrimSpace(row[2]),
			Content: strings.TrimSpace(row[3]),
		}
		result = append(result, stage)
	}
	return result, nil
}

func main() {
	stages, err := readExcelToStructs("./cmd/helper/data.xlsx")
	if err != nil {
		log.Fatalf("读取失败: %v", err)
	}

	marshal, err := json.MarshalIndent(stages, "", "  ")
	if err != nil {
		log.Fatalf("序列化失败: %v", err)
	}
	// 写字符到文件
	err = os.WriteFile("./cmd/helper/data.json", marshal, 0644)
	if err != nil {
		log.Fatalf("写入失败: %v", err)
	}
}
