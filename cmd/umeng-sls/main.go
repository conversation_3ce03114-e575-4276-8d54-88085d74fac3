package main

import sls "github.com/aliyun/aliyun-log-go-sdk"

func main() {
	provider := sls.NewStaticCredentialsProvider(config.AccessKeyId, config.AccessKeySecret, config.SecurityToken)
	cli := sls.CreateNormalInterfaceV2(config.Endpoint, provider)
	shards, err := cli.ListShards("paipan-prod", "dwd_ump_log_uapp_event_json_rt")
	if err != nil {
		panic(err)
	}
	m := make(map[int]string)
	for _, shard := range shards {
		cursor, err := cli.GetCursor("paipan-prod", "dwd_ump_log_uapp_event_json_rt", shard.ShardID, "begin")
		if err != nil {
			panic(err)
		}
		m[shard.ShardID] = cursor
	}
	for _, shard := range shards {
		gl, nextCursor, err := cli.PullLogs("paipan-prod", "dwd_ump_log_uapp_event_json_rt", shard.ShardID, m[shard.ShardID], "end", 100)
		if err != nil {
			panic(err)
		}
		m[shard.ShardID] = nextCursor
		for i, i2 := range gl.LogGroups {

		}
	}
}
