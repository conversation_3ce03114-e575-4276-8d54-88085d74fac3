package main

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"flag"
	"go.uber.org/zap"
	"time"
	"zodiacus/config"
	"zodiacus/internal/model"
	"zodiacus/internal/repository"
	"zodiacus/pkg/log"
	aliyun_sls "zodiacus/third_party/aliyun/sls"
)

func main() {
	var envConf = flag.String("conf", "etc/local.yml", "config path, eg: -conf ./etc/local.yml")
	flag.Parse()
	config.AppName = "umeng-sls"
	conf := config.NewConfig(*envConf)
	cli := aliyun_sls.NewClient(conf)
	shards, err := cli.ListShards("paipan-prod", "dwd_ump_log_uapp_event_json_rt")
	if err != nil {
		panic(err)
	}
	var (
		logger    = log.NewLogger(conf)
		db        = repository.NewDB(conf, logger)
		rdb       = repository.NewRedis(conf)
		umengRepo = repository.NewUmengRepository(repository.NewRepository(logger, db, rdb))
		tx        = repository.NewTransaction(repository.NewRepository(logger, db, rdb))
	)
	cursors := make(map[int]string)
	for _, shard := range shards {
		cursor, err := umengRepo.GetNextCursorByShardID(context.Background(), "paipan-prod", "dwd_ump_log_uapp_event_json_rt", shard.ShardID)
		if err != nil {
			panic(err)
		}
		if cursor == "" {
			cursor, err = cli.GetCursor("paipan-prod", "dwd_ump_log_uapp_event_json_rt", shard.ShardID, "begin")
			if err != nil {
				panic(err)
			}
		}
		cursors[shard.ShardID] = cursor
	}
	for {
		for _, shard := range shards {
			curCursor := cursors[shard.ShardID]
			gl, nextCursor, err := cli.PullLogs("paipan-prod", "dwd_ump_log_uapp_event_json_rt", shard.ShardID, curCursor, "", 100)
			if err != nil {
				logger.Error("failed to pull logs", zap.Error(err))
				continue
			}
			cursors[shard.ShardID] = nextCursor
			for _, lg := range gl.LogGroups {
				for _, l := range lg.Logs {
					kv := make(map[string]string)
					for _, c := range l.Contents {
						kv[*c.Key] = *c.Value
					}
					kvj := make(map[string]any)
					if kv["event_kv_json"] != "" {
						_ = json.Unmarshal([]byte(kv["event_kv_json"]), &kvj)
					}
					ue := &model.UmengEvent{
						AndroidID:         kv["android_id"],
						AppChannel:        kv["app_channel"],
						AppKey:            kv["app_key"],
						AppVersion:        kv["app_version"],
						City:              kv["city"],
						CliTimestamp:      kv["cli_timestamp"],
						Country:           kv["country"],
						DeviceBrand:       kv["device_brand"],
						DeviceModel:       kv["device_model"],
						EventKVJson:       kvj,
						EventName:         kv["event_name"],
						EventType:         kv["event_type"],
						Health:            kv["health"],
						IDFA:              kv["idfa"],
						IDFV:              kv["idfv"],
						InstallAppVersion: kv["install_app_version"],
						InstallChannel:    kv["install_channel"],
						InstallDatetime:   kv["install_datetime"],
						IP:                kv["ip"],
						NetworkAccess:     kv["network_access"],
						OAID:              kv["oaid"],
						OID:               kv["oid"],
						OS:                kv["os"],
						OSVersion:         kv["os_version"],
						PreAppVersion:     kv["pre_app_version"],
						Province:          kv["province"],
						ScreenHeight:      kv["screen_height"],
						ScreenWidth:       kv["screen_width"],
						SDKVersion:        kv["sdk_version"],
						SessionID:         kv["session_id"],
						SvrTimestamp:      kv["svr_timestamp"],
						UMID:              kv["umid"],
					}
					marshal, err := json.Marshal(ue)
					if err != nil {
						logger.Error("failed to marshal umeng event", zap.Error(err))
						continue
					}
					hash := md5.Sum(marshal)
					eventMd5 := hex.EncodeToString(hash[:])
					record := &model.UmengEventSls{
						EventMD5:      eventMd5,
						EventTime:     time.Unix(int64(*l.Time), 0),
						EventJson:     ue,
						SlsProject:    "paipan-prod",
						SlsLogstore:   "dwd_ump_log_uapp_event_json_rt",
						SlsSharedID:   shard.ShardID,
						SlsCursor:     curCursor,
						SlsNextCursor: nextCursor,
					}
					if err = tx.Transaction(context.TODO(), func(ctx context.Context) error {
						eve, err := umengRepo.FetchUmengEventSlsByEventMD5(ctx, eventMd5)
						if err != nil {
							return err
						}
						if eve != nil {
							return nil
						}
						if _, err = umengRepo.CreateUmengEventSls(ctx, record); err != nil {
							return err
						}
						return nil
					}); err != nil {
						logger.Error("failed to create umeng event sls", zap.Error(err))
					}
				}
			}
		}
	}
}

/*
crate table `umeng_event_sls` (
	`id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
	event_md5 varchar(255) not null unique comment '事件md5',
	event_time datetime not null comment '事件时间',
	event_json json not null comment '事件json',
	sls_project varchar(255) not null comment 'sls项目',
	sls_logstore varchar(255) not null comment 'sls日志库',
	sls_shared_id int not null comment 'sls shard id',
	sls_cursor varchar(255) not null comment 'sls cursor',
	sls_next_cursor varchar(255) not null comment 'sls next cursor',
	processed_at datetime comment '处理时间',
	created_at datetime default CURRENT_TIMESTAMP not null,
	updated_at datetime default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP not null,
	primary key (id),
	unique key idx_event_md5 (event_md5)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='umeng event logs';
*/

/*
	android_id
	app_channel
	app_key
	app_version
	city
	cli_timestamp
	country
	device_brand
	device_model
	event_kv_json
	event_name
	event_type
	health
	idfa
	idfv
	install_app_version
	install_channel
	install_datetime
	ip
	network_access
	oaid
	oid
	os
	os_version
	pre_app_version
	province
	screen_height
	screen_width
	sdk_version
	session_id
	svr_timestamp
	umid
*/
