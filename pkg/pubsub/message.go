package pubsub

import (
	"encoding/json"
	"github.com/google/uuid"
	"time"
)

type (
	EventType  = string
	ActionType = string
)

type Message struct {
	ID        string         `json:"id"`         // 消息唯一ID
	EventType EventType      `json:"event_type"` // 事件类型
	Action    ActionType     `json:"action"`     // 操作类型
	Data      map[string]any `json:"data"`       // 消息数据
	Timestamp int64          `json:"timestamp"`  // 时间戳
}

func NewMessage(eventType EventType, action ActionType, data map[string]interface{}) *Message {
	return &Message{
		ID:        uuid.New().String(),
		EventType: eventType,
		Action:    action,
		Data:      data,
		Timestamp: time.Now().Unix(),
	}
}

func (slf *Message) ToJSON() (string, error) {
	data, err := json.Marshal(slf)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

func FromJSON(jsonStr string) (*Message, error) {
	var msg Message
	err := json.Unmarshal([]byte(jsonStr), &msg)
	if err != nil {
		return nil, err
	}
	return &msg, nil
}

func (slf *Message) GetChannelName() string {
	return string(slf.EventType)
}
