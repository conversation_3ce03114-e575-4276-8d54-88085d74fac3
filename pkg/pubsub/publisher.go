package pubsub

import (
	"context"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"zodiacus/pkg/log"
)

type Publisher struct {
	rdb *redis.Client
	log *log.Logger
}

func NewPublisher(rdb *redis.Client, log *log.Logger) *Publisher {
	return &Publisher{
		rdb: rdb,
		log: log,
	}
}

func (slf *Publisher) Publish(ctx context.Context, eventType EventType, action ActionType, data map[string]any) error {
	msg := NewMessage(eventType, action, data)
	jsonData, err := msg.ToJSON()
	if err != nil {
		return errors.Wrapf(err, "failed to marshal message")
	}
	channel := msg.GetChannelName()
	if err = slf.rdb.Publish(ctx, channel, jsonData).Err(); err != nil {
		return errors.Wrapf(err, "failed to publish message")
	}
	slf.log.Info("Message published successfully", zap.String("channel", channel))
	return nil
}

func (slf *Publisher) BatchPublish(ctx context.Context, messages []*Message) error {
	if len(messages) == 0 {
		return nil
	}
	pipe := slf.rdb.Pipeline()
	for _, msg := range messages {
		jsonData, err := msg.ToJSON()
		if err != nil {
			return errors.Wrapf(err, "failed to marshal message")
		}
		channel := msg.GetChannelName()
		pipe.Publish(ctx, channel, jsonData)
	}
	if _, err := pipe.Exec(ctx); err != nil {
		return errors.Wrapf(err, "failed to batch publish messages")
	}
	slf.log.Info("Batch messages published successfully", zap.Int("count", len(messages)))
	return nil
}
