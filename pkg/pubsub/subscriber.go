package pubsub

import (
	"context"
	"fmt"
	"github.com/pkg/errors"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"zodiacus/pkg/log"
)

type Subscriber struct {
	rdb *redis.Client
	log *log.Logger
}

func NewSubscriber(rdb *redis.Client, log *log.Logger) *Subscriber {
	return &Subscriber{
		rdb: rdb,
		log: log,
	}
}

func (s *Subscriber) Subscribe(ctx context.Context, handler EventHandler) error {
	channel := handler.GetEventType()
	pubsub := s.rdb.Subscribe(ctx, channel)
	defer pubsub.Close()
	ch := pubsub.Channel()
	for {
		select {
		case <-ctx.Done():
			return errors.Wrapf(ctx.Err(), "context canceled")
		case msg := <-ch:
			if msg == nil {
				continue
			}
			if err := s.handleMessage(ctx, msg.Payload, handler); err != nil {
				s.log.Error("failed to handle message", zap.Error(err), zap.Any("msg", msg))
			}
		}
	}
}

// BatchSubscribe 订阅多个事件类型
func (s *Subscriber) BatchSubscribe(ctx context.Context, handlers map[EventType]EventHandler) error {
	if len(handlers) == 0 {
		return errors.New("no handlers provided")
	}
	channels := make([]string, 0, len(handlers))
	for eventType := range handlers {
		channels = append(channels, eventType)
	}
	pubsub := s.rdb.Subscribe(ctx, channels...)
	defer pubsub.Close()
	ch := pubsub.Channel()
	for {
		select {
		case <-ctx.Done():
			return errors.Wrapf(ctx.Err(), "context canceled")
		case msg := <-ch:
			if msg == nil {
				continue
			}
			eventType := msg.Channel
			handler, exists := handlers[eventType]
			if !exists {
				s.log.Warn("No handler found for event type",
					zap.String("event_type", string(eventType)),
					zap.String("channel", msg.Channel))
				continue
			}
			if err := s.handleMessage(ctx, msg.Payload, handler); err != nil {
				s.log.Error("Failed to handle message",
					zap.Error(err),
					zap.String("channel", msg.Channel),
					zap.String("payload", msg.Payload))
			}
		}
	}
}

// handleMessage 处理单个消息
func (s *Subscriber) handleMessage(ctx context.Context, payload string, handler EventHandler) error {
	msg, err := FromJSON(payload)
	if err != nil {
		return fmt.Errorf("failed to parse message: %w", err)
	}
	s.log.Debug("Processing message",
		zap.Any("message", msg))
	if err = handler.Handle(ctx, msg); err != nil {
		return fmt.Errorf("failed to handle message: %w", err)
	}
	return nil
}

// SubscribeWithRetry 带重试的订阅
func (s *Subscriber) SubscribeWithRetry(ctx context.Context, eventType EventType, handler EventHandler, maxRetries int, retryInterval time.Duration) error {
	var lastErr error
	for i := 0; i <= maxRetries; i++ {
		if i > 0 {
			s.log.Warn("Retrying subscription",
				zap.String("event_type", string(eventType)),
				zap.Int("attempt", i),
				zap.Error(lastErr))

			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(retryInterval):
			}
		}
		err := s.Subscribe(ctx, handler)
		if err == nil || errors.Is(err, context.Canceled) {
			return err
		}
		lastErr = err
		s.log.Error("Subscription failed",
			zap.String("event_type", string(eventType)),
			zap.Int("attempt", i+1),
			zap.Error(err))
	}
	return fmt.Errorf("subscription failed after %d retries: %w", maxRetries, lastErr)
}
