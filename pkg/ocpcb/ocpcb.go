package ocpcb

import (
	"context"
	"github.com/go-resty/resty/v2"
	"github.com/pkg/errors"
	"github.com/ua-parser/uap-go/uaparser"
	"go.uber.org/zap"
	"time"
	"zodiacus/internal/model"
	"zodiacus/internal/repository"
	"zodiacus/pkg/log"
	"zodiacus/third_party/baidu/ocpc"
)

type Ocpcb struct {
	uap      *uaparser.Parser
	logger   *log.Logger
	ocpcRepo repository.OcpcRepository
}

func NewOcpcb(logger *log.Logger, ocpcRepo repository.OcpcRepository) *Ocpcb {
	return &Ocpcb{
		uap:      uaparser.NewFromSaved(),
		logger:   logger,
		ocpcRepo: ocpcRepo,
	}
}

type (
	CbActivateRequest struct {
		CbType           ocpc.ConversionType // 转化类型
		CbValue          int                 // 转化指标
		AppUserID        string              // APP用户ID：仅转化类型为付费时传入
		Imei             string              // 归因方式：回传
		Oaid             string              // 归因方式：回传
		AndroidID        string              // 归因方式：回传
		IP               string              // 归因方式：不回传
		OsType           string              // 归因方式：不回传
		OsVersion        string              // 归因方式：不回传
		Idfa             string              // 归因方式：回传
		Caid             string              // 归因方式：不回传
		Mac              string              // 归因方式：不回传
		CbTime           time.Time           // 回传时间
		BindingAppUserID string              // 绑定APP用户ID
	}
	CbActivateResponse struct {
		ErrCode int    `json:"error_code"`
		ErrMsg  string `json:"error_msg"`
	}
)

func (slf *Ocpcb) CbActivate(ctx context.Context, req *CbActivateRequest) error {
	slf.logger.WithContext(ctx).Info("回传：开始回传", zap.Any("req", req))
	var (
		click    *model.BaiduOcpcClick
		err      error
		aType    = req.CbType
		aValue   = req.CbValue
		extInfo  string
		aTime    = req.CbTime.Unix()
		joinType string
	)
	if req.AppUserID != "" {
		// 已回传激活与注册，归因方式等同于注册
		click, err = slf.ocpcRepo.FetchOcpcClickByAppUserID(ctx, req.AppUserID)
		if err != nil {
			return errors.Wrapf(err, "FetchOcpcClickByAppUserID")
		}
		if click == nil {
			slf.logger.WithContext(ctx).Error("回传：未找到点击记录", zap.Any("param", req))
			return nil
		}
		joinType = click.CbRegisterJoinType
		extInfo = click.ExtInfo
	} else {
		click, err = slf.ocpcRepo.FetchOcpcClickByCbType(ctx, req.CbType, map[string]string{
			"imei":       req.Imei,
			"oaid":       req.Oaid,
			"android_id": req.AndroidID,
			"idfa":       req.Idfa,
			"caid":       req.Caid,
			"mac":        req.Mac,
			"ip":         req.IP,
			"os_type":    req.OsType,
			"os_version": req.OsVersion,
		})
		if err != nil {
			return err
		}
		if click == nil {
			slf.logger.WithContext(ctx).Error("回传：未找到点击记录", zap.Any("param", req))
			return nil
		}
		if req.Imei != "" && req.Imei == click.ImeiMd5 {
			joinType = "imei"
		} else if req.Oaid != "" && req.Oaid == click.Oaid {
			joinType = "oaid"
		} else if req.AndroidID != "" && req.AndroidID == click.AndroidIdMd5 {
			joinType = "android_id"
		} else if req.Idfa != "" && req.Idfa == click.Idfa {
			joinType = "idfa"
		} else if req.Caid != "" && req.Caid == click.Caid {
			joinType = "caid"
		} else if req.Mac != "" && req.Mac == click.MacMd5 {
			joinType = "mac"
		} else {
			joinType = "ip"
		}
		slf.logger.WithContext(ctx).Info("回传：已匹配归因方式", zap.String("join_type", joinType))
		extInfo = click.ExtInfo
	}
	var fields = []*ocpc.ConversionField{
		{"a_type", aType},
		{"a_value", aValue},
		{"ext_info", extInfo},
		{"s", 1234},
		{"a_time", aTime},
		{"join_type", joinType},
	}
	switch joinType {
	case "imei":
		fields = append(fields, &ocpc.ConversionField{Key: "imei", Value: req.Imei})
	case "oaid":
		fields = append(fields, &ocpc.ConversionField{Key: "oaid", Value: req.Oaid})
	case "android_id":
		fields = append(fields, &ocpc.ConversionField{Key: "android_id", Value: req.AndroidID})
	case "idfa":
		fields = append(fields, &ocpc.ConversionField{Key: "idfa", Value: req.Idfa})
	default:
	}
	var res CbActivateResponse
	fullUrl := ocpc.BuildConversionUrl(fields, click.Akey)
	post, err := resty.New().R().SetContext(ctx).SetResult(&res).Post(fullUrl)
	if err != nil {
		return errors.Wrapf(err, "failed to post to %s", fullUrl)
	}
	if post.StatusCode() != 200 {
		return nil
	}
	if res.ErrCode != 0 {
		return errors.Errorf("failed to callback, err_code=%s, err_msg=%s", res.ErrCode, res.ErrMsg)
	}
	switch req.CbType {
	case ocpc.ConversionTypeActivate:
		click.CbActivate = true
		click.CbActivateJoinType = joinType
		click.CbActivateTime = req.CbTime
	case ocpc.ConversionTypeRegister:
		click.CbRegister = true
		click.CbRegisterJoinType = joinType
		click.CbRegisterTime = req.CbTime
	case ocpc.ConversionTypeOrders:
		click.CbOrders = true
		click.CbOrdersJoinType = joinType
		click.CbOrdersTime = req.CbTime
		click.CbOrdersValue = req.CbValue
	}
	if req.BindingAppUserID != "" {
		click.AppUserID = req.BindingAppUserID
	}
	if err = slf.ocpcRepo.UpdateOcpcClick(ctx, click); err != nil {
		return errors.Wrapf(err, "UpdateOcpcClick")
	}
	return nil
}
