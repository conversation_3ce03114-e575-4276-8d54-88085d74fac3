package algo

func Combinations(n, k int) [][]int {
	if k == 0 || k > n {
		return [][]int{}
	}
	var result [][]int
	var comb []int
	var combine func(start, depth int)
	combine = func(start, depth int) {
		if depth == k {
			result = append(result, append([]int(nil), comb...))
			return
		}
		for i := start; i < n; i++ {
			comb = append(comb, i)
			combine(i+1, depth+1)
			comb = comb[:len(comb)-1]
		}
	}
	combine(0, 0)
	return result
}

func CombinationsFromArray[T any](elements []T, k int) [][]T {
	if k == 0 || k > len(elements) {
		return [][]T{}
	}
	var result [][]T
	var comb []T
	var combine func(start, depth int)
	combine = func(start, depth int) {
		if depth == k {
			// Make a copy of the combination and add it to the result.
			result = append(result, append([]T(nil), comb...))
			return
		}
		for i := start; i < len(elements); i++ {
			comb = append(comb, elements[i])
			combine(i+1, depth+1)
			comb = comb[:len(comb)-1]
		}
	}
	combine(0, 0)
	return result
}
