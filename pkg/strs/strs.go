package strs

func Str2Array(str string) []string {
	runes := []rune(str)
	var list []string
	for _, r := range runes {
		list = append(list, string(r))
	}
	return list
}

func AllCharsExists(target, chars string) (map[rune]int, bool) {
	targetMap := make(map[rune]int)
	for _, ch := range target {
		targetMap[ch]++
	}
	resultMap := make(map[rune]int)
	for _, ch := range chars {
		if targetMap[ch] <= 0 {
			return nil, false
		}
		resultMap[ch]++
	}
	return resultMap, true
}
