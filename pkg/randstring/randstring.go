package randstring

import (
	"math/rand"
	"sync"
	"time"
	"unsafe"
)

const (
	charset = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
)

const (
	CharsetNumeric      = "0123456789"
	CharsetUpperAlpha   = "abcdefghijklmnopqrstuvwxyz"
	CharsetLowerAlpha   = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
	CharsetAlpha        = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
	CharsetAlphaNumeric = "123456789ABCDEFGHJKLMNPQRSTUVWXYZ"
)

var (
	r   *rand.Rand
	mux sync.Mutex
)

func init() {
	mux = sync.Mutex{}
	r = rand.New(rand.NewSource(time.Now().UnixNano()))
}

func RandomStr(length int, charset string) string {

	charsetLen := len(charset)
	bytes := make([]byte, length)

	mux.Lock()
	defer mux.Unlock()

	for i := range bytes {
		bytes[i] = charset[r.Intn(charsetLen)]
	}

	return *(*string)(unsafe.Pointer(&bytes))
}
