package aliyun_sls

import (
	"github.com/spf13/viper"
	"time"

	sls "github.com/aliyun/aliyun-log-go-sdk"
	"github.com/pkg/errors"
	"google.golang.org/protobuf/proto"
)

type Config struct {
	ProjectName     string
	LogStoreName    string
	LogTopic        string
	LogSource       string
	LogContentKey   string
	Endpoint        string
	AccessKeyId     string
	AccessKeySecret string
	SecurityToken   string
}

type Logger struct {
	Config
	client sls.ClientInterface
}

func NewLogger(config Config) *Logger {
	provider := sls.NewStaticCredentialsProvider(config.AccessKeyId, config.AccessKeySecret, config.SecurityToken)
	return &Logger{
		Config: config,
		client: sls.CreateNormalInterfaceV2(config.Endpoint, provider),
	}
}

func (slf *Logger) Write(data []byte) (int, error) {
	contents := []*sls.LogContent{{
		Key:   proto.String("__log__"),
		Value: proto.String(string(data)),
	}}
	logs := []*sls.Log{{
		Time:     proto.Uint32(uint32(time.Now().Unix())),
		Contents: contents,
	}}
	logGroup := &sls.LogGroup{
		Logs:   logs,
		Topic:  proto.String(slf.LogTopic),
		Source: proto.String(slf.LogSource),
	}
	if err := slf.client.PutLogs(slf.ProjectName, slf.LogStoreName, logGroup); err != nil {
		return 0, errors.Wrap(err, "failed to put logs to aliyun sls")
	}
	return len(data), nil
}

func NewClient(conf *viper.Viper) sls.ClientInterface {
	provider := sls.NewStaticCredentialsProvider(conf.GetString("sls.access_key_id"), conf.GetString("sls.access_key_secret"), conf.GetString("sls.security_token"))
	return sls.CreateNormalInterfaceV2(conf.GetString("sls.endpoint"), provider)
}
