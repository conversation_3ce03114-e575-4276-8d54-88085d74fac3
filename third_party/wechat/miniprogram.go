package wechat_app

import (
	"context"
	"github.com/silenceper/wechat/v2"
	"github.com/silenceper/wechat/v2/cache"
	"github.com/silenceper/wechat/v2/miniprogram/config"
	"github.com/spf13/viper"
	"zodiacus/pkg/log"
)

func NewMiniProgram(conf *viper.Viper, _ *log.Logger) *Miniprogram {
	var (
		redisCfg = conf.Sub("data.redis")
		appCfg   = conf.Sub("wechat.miniprogram")
	)
	wc := wechat.NewWechat()
	app := wc.GetMiniProgram(&config.Config{
		AppID:     appCfg.GetString("app_id"),
		AppSecret: appCfg.GetString("secret"),
		Cache: cache.NewRedis(context.TODO(), &cache.RedisOpts{
			Host:     redisCfg.GetString("addr"),
			Password: redisCfg.GetString("password"),
			Database: redisCfg.GetInt("db"),
		}),
	})
	return &Miniprogram{app}
}
