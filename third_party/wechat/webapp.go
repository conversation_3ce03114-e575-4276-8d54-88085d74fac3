package wechat_app

import (
	"context"
	"github.com/silenceper/wechat/v2"
	"github.com/silenceper/wechat/v2/cache"
	"github.com/silenceper/wechat/v2/officialaccount/config"
	"github.com/spf13/viper"
	"zodiacus/pkg/log"
)

func NewWebApp(conf *viper.Viper, _ *log.Logger) *WebApp {
	var (
		redisCfg = conf.Sub("data.redis")
		appCfg   = conf.Sub("wechat.webapp")
	)
	wc := wechat.NewWechat()
	app := wc.GetOfficialAccount(&config.Config{
		AppID:          appCfg.GetString("app_id"),
		AppSecret:      appCfg.GetString("secret"),
		Token:          appCfg.GetString("token"),
		EncodingAESKey: appCfg.GetString("aes_key"),
		Cache: cache.NewRedis(context.TODO(), &cache.RedisOpts{
			Host:     redisCfg.GetString("addr"),
			Password: redisCfg.GetString("password"),
			Database: redisCfg.GetInt("db"),
		}),
	})
	return &WebApp{OfficialAccount: app}
}
