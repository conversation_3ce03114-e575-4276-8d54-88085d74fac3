package wechat_app

import (
	"github.com/silenceper/wechat/v2/miniprogram"
	"github.com/silenceper/wechat/v2/officialaccount"
	"github.com/silenceper/wechat/v2/openplatform"
)

type (
	WebApp struct {
		*officialaccount.OfficialAccount
	}
	OfficialAccount struct {
		*officialaccount.OfficialAccount
	}
	OpenPlatform struct {
		*openplatform.OpenPlatform
	}
	Miniprogram struct {
		*miniprogram.MiniProgram
	}
)
