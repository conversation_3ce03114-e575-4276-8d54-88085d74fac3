package corona

import (
	"fmt"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"time"
	"zodiacus/pkg/array"
	"zodiacus/pkg/maths"
)

type (
	GetDatetimeBySizhuRequest struct {
		Sizhu     [4]string `json:"sizhu" example:"癸亥,戊午,壬寅,己酉"`        // 四柱
		StartYear int       `json:"startYear,omitempty" example:"1801"` // 默认1801
		EndYear   int       `json:"endYear,omitempty" example:"2099"`   // 默认2099
	}
	GetDatetimeBySizhuResponse []string
)

type (
	GetAllRequest struct {
		Birthtime string `json:"birthtime"` // 出生时间
		Gender    string `json:"gender"`    // 性别
	}
	GetAllResponse struct {
		Yangli                          string   `json:"yangli"`                              // 公历
		Nongli                          string   `json:"nongli"`                              // 农历
		Sizhu                           []string `json:"sizhu"`                               // 四柱
		Zishi                           int      `json:"zishi"`                               // 时辰 0没开启,1开启了早晚子时
		Gender                          string   `json:"gender"`                              // 性别
		Shengxiao                       string   `json:"shengxiao"`                           // 生肖
		Taiyuan                         string   `json:"taiyuan"`                             // 胎元
		Minggong                        string   `json:"minggong"`                            // 命宫
		Shengong                        string   `json:"shengong"`                            // 生宫
		Kongwang                        string   `json:"kongwang"`                            // 空亡
		Minggua                         string   `json:"minggua"`                             // 命卦
		Taixi                           string   `json:"taixi"`                               // 胎息
		Xingxiu                         string   `json:"xingxiu"`                             // 星宿
		JqTwoName                       string   `json:"jqTwoName"`                           // 节气
		JqBegin                         string   `json:"jqBegin"`                             // 节气开始
		JqEnd                           string   `json:"jqEnd"`                               // 节气结束
		GetFenye                        string   `json:"getFenye"`                            // 0《子平真诠》分日诀,1《三命通会》分日诀 ,2《渊海子平》分日诀 ,3《神峰通考》分日诀 ,4《星平会海》分日诀 ,5万育吾之法诀
		RetQyStr                        string   `json:"retQyStr"`                            // 日柱
		RetJyStr                        string   `json:"retJyStr"`                            // 月柱
		RetJyDate                       string   `json:"retJyDate"`                           // 月柱日期
		GetShierDayun                   []string `json:"getShierDayun"`                       // 十二长生
		TianganGetShierDayun            []string `json:"tiangan_getShierDayun"`               // 天干十二长生
		DizhiGetShierDayun              []string `json:"dizhi_getShierDayun"`                 // 地支十二长生
		GetXingyunList                  []string `json:"getXingyunList"`                      // 星宿列表
		GetZizuoList                    []string `json:"getZizuoList"`                        // 紫薇列表
		GetKongwangList                 []string `json:"getKongwangList"`                     // 空亡列表
		GetNayinList                    []string `json:"getNayinList"`                        // 纳音列表
		GetXiaoyunList                  []string `json:"getXiaoyunList"`                      // 小运列表
		TianganGetXiaoyunList           []string `json:"tiangan_getXiaoyunList"`              // 天干小运列表
		DizhiGetXiaoyunList             []string `json:"dizhi_getXiaoyunList"`                // 地支小运列表
		GetXingyunX                     []string `json:"getXingyunX"`                         // 星宿列表
		GetZizuoX                       []string `json:"getZizuoX"`                           // 紫薇列表
		GetKongwangX                    []string `json:"getKongwangX"`                        // 空亡列表
		GetNayinX                       []string `json:"getNayinX"`                           // 纳音列表
		AgeS                            string   `json:"ageS"`                                // 年龄上
		AgeX                            string   `json:"ageX"`                                // 年龄下
		SubYearXiaoyun                  int      `json:"subYearXiaoyun"`                      // 小运年数
		StrXiaoyunLiuYear               string   `json:"strXiaoyunLiuYear"`                   // 小运流年
		XiaoyunQishi                    int      `json:"xiaoyunQishi"`                        // 小运起始（农历）
		XiaoyunJiezhi                   int      `json:"xiaoyunJiezhi"`                       // 小运结束（农历）
		GetShishenXiaoyunTgList         []string `json:"getShishen_xiaoyun_Tg_list"`          // 十神小运天干列表
		GetShishenXiaoyunJxTgList       []string `json:"getShishen_xiaoyun_jx_Tg_list"`       // 十神小运进星天干列表
		SubYearDayun                    int      `json:"subYearDayun"`                        // 大运年数
		StrDayunLiuYear                 string   `json:"strDayunLiuYear"`                     // 大运流年
		DayunQishi                      int      `json:"dayunQishi"`                          // 大运起始（农历）
		DayunJiezhi                     int      `json:"dayunJiezhi"`                         // 大运结束（农历）
		GetShishenDayunTgList           []string `json:"getShishen_dayun_Tg_list"`            // 十神大运天干列表
		GetShishenDayunJxTgList         []string `json:"getShishen_dayun_jx_Tg_list"`         // 十神大运进星天干列表
		DizhiListIsFind12Dy             []string `json:"dizhiList_IsFind_12dy"`               // 地支列表
		TianganBenqiTgList              []string `json:"tiangan_benqi_tg_List"`               // 本气天干列表
		TianganBenqiSsList              []string `json:"tiangan_benqi_ss_List"`               // 本气十神列表
		TianganBenqiSsjxList            []string `json:"tiangan_benqi_ssjx_List"`             // 本气十神���表
		TianganZhongqiTgList            []string `json:"tiangan_zhongqi_tg_List"`             // 中气天干列表
		TianganZhongqiSsList            []string `json:"tiangan_zhongqi_ss_List"`             // 中气十神列表
		TianganZhongqiSsjxList          []string `json:"tiangan_zhongqi_ssjx_List"`           // 中气十神列表
		TianganYuqiTgList               []string `json:"tiangan_yuqi_tg_List"`                // 余气天干列表
		TianganYuqiSsList               []string `json:"tiangan_yuqi_ss_List"`                // 余气十神列表
		TianganYuqiSsjxList             []string `json:"tiangan_yuqi_ssjx_List"`              // 余气十神列表
		TypenameList12Dy                []string `json:"typename_List_12dy"`                  // 地支列表
		DizhiListIsFindXy               []string `json:"dizhiList_IsFind_xy"`                 // 地支列表
		TianganBenqiTgListXy            []string `json:"tiangan_benqi_tg_List_xy"`            // 本气天干列表
		TianganBenqiSsListXy            []string `json:"tiangan_benqi_ss_List_xy"`            // 本气十神列表
		TianganBenqiSsjxListXy          []string `json:"tiangan_benqi_ssjx_List_xy"`          // 本气十神列表
		TianganZhongqiTgListXy          []string `json:"tiangan_zhongqi_tg_List_xy"`          // 中气天干列表
		TianganZhongqiSsListXy          []string `json:"tiangan_zhongqi_ss_List_xy"`          // 中气十神列表
		TianganZhongqiSsjxListXy        []string `json:"tiangan_zhongqi_ssjx_List_xy"`        // 中气十神列表
		TianganYuqiTgListXy             []string `json:"tiangan_yuqi_tg_List_xy"`             // 余气天干列表
		TianganYuqiSsListXy             []string `json:"tiangan_yuqi_ss_List_xy"`             // 余气十神列表
		TianganYuqiSsjxListXy           []string `json:"tiangan_yuqi_ssjx_List_xy"`           // 余气十神列表
		TypenameListXy                  []string `json:"typename_List_xy"`                    // 地支列表
		BaziTupleNew                    []string `json:"baziTupleNew"`                        // 八字列表
		IsDayunOrXiaoyun                int      `json:"isDayunOrXiaoyun"`                    // 是否大运或小运
		NowDayunOrXiaoyun               string   `json:"nowDayunOrXiaoyun"`                   // 当前大运或小运
		NowOneLiunian                   string   `json:"nowOneLiunian"`                       // 当前流年
		BaziTupleNewYear                int      `json:"baziTupleNewYear"`                    // 八字列表
		PaiYueJq                        []string `json:"paiYueJq"`                            // 排月诀
		BaziTupleNewTg                  []string `json:"baziTupleNew_tg"`                     // 八字列表
		BaziTupleNewDz                  []string `json:"baziTupleNew_dz"`                     // 八字列表
		GetShishenBaziTupleNewTgList    []string `json:"getShishen_baziTupleNew_Tg_list"`     // 十神八字列表
		GetShishenBaziTupleNewJxTgList  []string `json:"getShishen_baziTupleNew_jx_Tg_list"`  // 十神八字列表
		DizhiListIsFindLiuAll           []string `json:"dizhiList_IsFind_LiuAll"`             // 地支列表
		TianganBenqiTgListLiuAll        []string `json:"tiangan_benqi_tg_List_LiuAll"`        // 本气天干列表
		TianganBenqiSsListLiuAll        []string `json:"tiangan_benqi_ss_List_LiuAll"`        // 本气十神列表
		TianganBenqiSsjxListLiuAll      []string `json:"tiangan_benqi_ssjx_List_LiuAll"`      // 本气十神列表
		TianganZhongqiTgListLiuAll      []string `json:"tiangan_zhongqi_tg_List_LiuAll"`      // 中气天干列表
		TianganZhongqiSsListLiuAll      []string `json:"tiangan_zhongqi_ss_List_LiuAll"`      // 中气十神列表
		TianganZhongqiSsjxListLiuAll    []string `json:"tiangan_zhongqi_ssjx_List_LiuAll"`    // 中气十神列表
		TianganYuqiTgListLiuAll         []string `json:"tiangan_yuqi_tg_List_LiuAll"`         // 余气天干列表
		TianganYuqiSsListLiuAll         []string `json:"tiangan_yuqi_ss_List_LiuAll"`         // 余气十神列表
		TianganYuqiSsjxListLiuAll       []string `json:"tiangan_yuqi_ssjx_List_LiuAll"`       // 余气十神列表
		TypenameListLiuAll              []string `json:"typename_List_LiuAll"`                // 地支列表
		TianganLiuYearXiaoyun           []string `json:"tiangan_LiuYearXiaoyun"`              // 天干流年
		DizhiLiuYear1Xiaoyun            []string `json:"dizhi_LiuYear1Xiaoyun"`               // 地支流年
		DizhiListIsFindXyliunian        []string `json:"dizhiList_IsFind_xyliunian"`          // 地支列表
		TianganBenqiTgListXyliunian     []string `json:"tiangan_benqi_tg_List_xyliunian"`     // 本气天干列表
		TianganBenqiSsListXyliunian     []string `json:"tiangan_benqi_ss_List_xyliunian"`     // 本气十神列表
		TianganBenqiSsjxListXyliunian   []string `json:"tiangan_benqi_ssjx_List_xyliunian"`   // 本气十神列表
		TianganZhongqiTgListXyliunian   []string `json:"tiangan_zhongqi_tg_List_xyliunian"`   // 中气天干列表
		TianganZhongqiSsListXyliunian   []string `json:"tiangan_zhongqi_ss_List_xyliunian"`   // 中气十神列表
		TianganZhongqiSsjxListXyliunian []string `json:"tiangan_zhongqi_ssjx_List_xyliunian"` // 中气十神列表
		TianganYuqiTgListXyliunian      []string `json:"tiangan_yuqi_tg_List_xyliunian"`      // 余气天干列表
		TianganYuqiSsListXyliunian      []string `json:"tiangan_yuqi_ss_List_xyliunian"`      // 余气十神列表
		TianganYuqiSsjxListXyliunian    []string `json:"tiangan_yuqi_ssjx_List_xyliunian"`    // 余气十神列表
		TypenameListXyliunian           []string `json:"typename_List_xyliunian"`             // 地支列表
		Javamsg                         string   `json:"javamsg"`                             // 消息
		Javastatus                      string   `json:"javastatus"`                          // 状态
		Bazhinum                        string   `json:"bazhinum"`                            // 八字数
		SaveWxxqsGan                    string   `json:"saveWxxqsGan"`                        // 干支
		SaveWxxqs                       string   `json:"saveWxxqs"`                           // 干支
		SaveLiliangWuXingMingzi         string   `json:"saveLiliangWuXingMingzi"`             // 五行顺序
		SaveLiliangNum                  string   `json:"saveLiliangNum"`                      // 数字
		SaveLiliangBfb                  string   `json:"saveLiliangBfb"`                      // 百分比
		Jishengzhukexiehao              string   `json:"jishengzhukexiehao"`                  // 吉神
		JishengzhukexiehaoBfb           string   `json:"jishengzhukexiehaoBfb"`               // 吉神百分比
		Yinqiyangqi                     string   `json:"yinqiyangqi"`                         // 阴阳气
		YinqiyangqiBfb                  string   `json:"yinqiyangqiBfb"`                      // 阴阳气百分比
		Rizhuzonghedefen                string   `json:"rizhuzonghedefen"`                    // 日柱综合得分
		Xiyongjichou                    string   `json:"xiyongjichou"`                        // 喜用忌仇
		Xiyongjichoushishen             string   `json:"xiyongjichoushishen"`                 // 喜用忌仇神
		Riyuan                          string   `json:"riyuan"`                              // 日元
		RiyuanBiange                    string   `json:"riyuan_biange"`                       // 日元变格
		SaveWxxqsMingzi                 string   `json:"saveWxxqsMingzi"`                     // 五行
		Tiaohouyongshen                 string   `json:"tiaohouyongshen"`                     // 调后用神
		Geju                            string   `json:"geju"`                                // 格局
		GetDizhiAnhe                    [][]int  `json:"getDizhi_anhe"`                       // 地支暗合
		GetDizhiLiuhe                   [][]any  `json:"getDizhi_liuhe"`                      // 地支六合
		GetDizhiSanhe                   [][]any  `json:"getDizhi_sanhe"`                      // 地支三合
		GetDizhiSanheBanhe              [][]any  `json:"getDizhi_sanhe_banhe"`                // 地支三合半合
		GetDizhiSanheAnhe               [][]any  `json:"getDizhi_sanhe_anhe"`                 // 地支三合暗合
		GetDizhiSanheGonghe             [][]any  `json:"getDizhi_sanhe_gonghe"`               // 地支三合拱合
		GetDizhiSanhui                  []any    `json:"getDizhi_sanhui"`                     // 地支三会
		GetDizhiSanhuiAn                []any    `json:"getDizhi_sanhui_an"`                  // 地支三会暗合
		GetDizhiSanhuiGong              []any    `json:"getDizhi_sanhui_gong"`                // 地支三会拱合
		GetDizhiSanxing                 []any    `json:"getDizhi_sanxing"`                    // 地支三刑
		GetDizhiXiangxing               [][]int  `json:"getDizhi_xiangxing"`                  // 地支相刑
		GetDizhiXiangchong              [][]int  `json:"getDizhi_xiangchong"`                 // 地支相冲
		GetDizhiXianghai                [][]int  `json:"getDizhi_xianghai"`                   // 地支相害
		GetDizhiXiangpo                 [][]int  `json:"getDizhi_xiangpo"`                    // 地支相破
		GetTianganWuhe                  [][]any  `json:"getTiangan_wuhe"`                     // 天干五合
		GetTianganXiangchong            [][]int  `json:"getTiangan_xiangchong"`               // 天干相冲
		GetTianganXiangke               [][]int  `json:"getTiangan_xiangke"`                  // 天干相克
		GetShensha                      [][]any  `json:"getShensha"`                          // 神煞
		Constellation                   string   `json:"constellation"`                       // 星座
		GetLiuT                         struct {
			NianGanYueGan  string `json:"年干月干"`       // 年干月干
			YueGanRiGan    string `json:"月干日干"`       // 月干日干
			RiGanShiGan    string `json:"日干时干"`       // 日干时干
			NianZhiYueZhi  string `json:"年支月支"`       // 年支月支
			YueZhiRiZhi    string `json:"月支日支"`       // 月支日支
			RiZhiShiZhi    string `json:"日支时支"`       // 日支时支
			NianGanNianZhi string `json:"年干年支"`       // 年干年支
			YueGanYueZhi   string `json:"月干月支"`       // 月干月支
			RiGanRiZhi     string `json:"日干日支"`       // 日干日支
			ShiGanShiZhi   string `json:"时干时支"`       // 时干时支
			LiutongNum     int    `json:"liutongNum"` // 流通数
			ZuaiNum        int    `json:"zuaiNum"`    // 罪数
			Pingfen        string `json:"pingfen"`    // 评分
		} `json:"getLiuT"` // 流通罪数评分
		GetXingyunListLiuAll  []string                  `json:"getXingyunListLiuAll"`   // 星运
		GetZizuoListLiuAll    []string                  `json:"getZizuoListLiuAll"`     // 自坐
		GetKongwangListLiuAll []string                  `json:"getKongwangListLiuAll"`  // 空亡
		GetNayinListLiuAll    []string                  `json:"getNayinListLiuAll"`     // 纳音
		StrshishengNum        string                    `json:"strshishengNum"`         // 十神天干
		ShishengNum           string                    `json:"shishengNum"`            // 十神天干能量
		ShishenArr            []string                  `json:"strshishengNumShiSheng"` // 十神
		ShishenBfbArr         []float64                 `json:"shishengNumBfb"`         // 十神能量
		ShishenPower          []*PaipanShishenPowerItem `json:"-"`
		ShishenPowerMap       map[string]float64        `json:"-"`
		ShishenNumMap         map[string]int            `json:"-"`
		WuxingPowerMap        map[string]int            `json:"-"`
		ShishenWuxingMap      map[string]string         `json:"-"`
		WuxingShishenMap      map[string]string         `json:"-"`
		Tiangan               []string                  `json:"-"`
		Dizhi                 []string                  `json:"-"`
		Ganzhi                []string                  `json:"-"`
		Zhuxing               []string                  `json:"-"`
		Benqi                 []string                  `json:"-"`
		BenqiShishen          []string                  `json:"-"`
		Zhongqi               []string                  `json:"-"`
		ZhongqiShishen        []string                  `json:"-"`
		Yuqi                  []string                  `json:"-"`
		YuqiShishen           []string                  `json:"-"`
		Nayin                 []string                  `json:"-"`
		NayinWuxing           []string                  `json:"-"`
		Shensha4              [][]string                `json:"-"`
		Shensha6              [][]string                `json:"-"`
		ShenshaDL             [][]string                `json:"-"`
		DayunGanzhi           []string                  `json:"-"`
		DayunLiunianGanzhi    []string                  `json:"-"`
		XiaoyunGanzhi         []string                  `json:"-"`
		XiaoyunLiunianGanzhi  []string                  `json:"-"`
		Yinqi                 int                       `json:"-"`
		Yangqi                int                       `json:"-"`
		YXCJX                 []string                  `json:"-"`
		QiYunTime             time.Time                 `json:"-"`
	}
)

func (slf *GetAllResponse) Process() {
	slf.calculateShishenPower()
	slf.ShishenPowerMap = make(map[string]float64)
	slf.ShishenWuxingMap = make(map[string]string)
	slf.WuxingShishenMap = make(map[string]string)
	slf.ShishenNumMap = make(map[string]int)
	for _, item := range slf.ShishenPower {
		var (
			ss1, ss2   = item.ShiShenArr[0], item.ShiShenArr[1]
			ss1P, ss2P = item.PowerArr[0], item.PowerArr[1]
		)
		slf.ShishenPowerMap[ss1] = float64(ss1P)
		slf.ShishenPowerMap[ss2] = float64(ss2P)
		slf.ShishenPowerMap[item.ShiShen] = float64(ss1P + ss2P)
		slf.ShishenWuxingMap[item.ShiShen] = item.Wuxing
		slf.ShishenWuxingMap[ss1] = item.Wuxing
		slf.ShishenWuxingMap[ss2] = item.Wuxing
		slf.WuxingShishenMap[item.Wuxing] = item.ShiShen
	}
	slf.WuxingPowerMap = func() map[string]int {
		m := make(map[string]int)
		splitN1 := strings.SplitN(slf.SaveLiliangWuXingMingzi, ",", 5)
		splitN2 := strings.SplitN(slf.SaveLiliangNum, ",", 5)
		for i, s := range splitN1 {
			m[s], _ = strconv.Atoi(splitN2[i])
		}
		return m
	}()
	ssNumTgArr := strings.Split(slf.StrshishengNum, ",")
	ssNumArr := strings.Split(slf.ShishengNum, ",")
	for i := 0; i < len(ssNumTgArr); i++ {
		num, _ := strconv.Atoi(ssNumArr[i])
		slf.ShishenNumMap[ssNumTgArr[i]] = num
	}
	slf.Tiangan = slf.BaziTupleNewTg
	slf.Dizhi = slf.BaziTupleNewDz
	slf.Ganzhi = slf.BaziTupleNew
	slf.Zhuxing = slf.GetShishenBaziTupleNewTgList
	slf.Benqi = slf.TianganBenqiTgListLiuAll
	slf.BenqiShishen = slf.TianganBenqiSsListLiuAll
	slf.Zhongqi = slf.TianganZhongqiTgListLiuAll
	slf.ZhongqiShishen = slf.TianganZhongqiSsListLiuAll
	slf.Yuqi = slf.TianganYuqiTgListLiuAll
	slf.YuqiShishen = slf.TianganYuqiSsListLiuAll
	slf.Nayin = slf.GetNayinListLiuAll
	slf.NayinWuxing = func() []string {
		var result []string
		for _, v := range slf.Nayin {
			result = append(result, string([]rune(v)[2]))
		}
		return result
	}()
	slf.DayunGanzhi = slf.GetShierDayun
	slf.DayunLiunianGanzhi = strings.Split(slf.StrDayunLiuYear, ",")
	slf.XiaoyunGanzhi = slf.GetXiaoyunList
	slf.XiaoyunLiunianGanzhi = strings.Split(slf.StrXiaoyunLiuYear, ",")

	val := reflect.ValueOf(slf)
	if val.Kind() != reflect.Ptr || val.Elem().Kind() != reflect.Struct {
		panic("never reach here")
	}
	val = val.Elem()
	for i := 0; i < val.NumField(); i++ {
		field := val.Field(i)
		if field.Kind() == reflect.Slice && field.Type().Elem().Kind() == reflect.String {
			for j := 0; j < field.Len(); j++ {
				elem := field.Index(j)
				if elem.String() == "无" {
					elem.SetString("")
				}
			}
		}
	}

	var shenshaTmp GetShenshaResponse
	for _, v := range slf.GetShensha {
		var tmp []string
		for _, item := range v {
			tmp = append(tmp, fmt.Sprintf("%v", item))
		}
		shenshaTmp.Data = append(shenshaTmp.Data, strings.Join(tmp, ","))
	}
	for i := 0; i < 4; i++ {
		slf.Shensha4 = append(slf.Shensha4, shenshaTmp.GetByIndex(i))
	}
	for i := 0; i < 6; i++ {
		slf.Shensha6 = append(slf.Shensha6, shenshaTmp.GetByIndex(i))
	}
	slf.ShenshaDL = slf.Shensha6[4:6]

	splitN := strings.SplitN(slf.Yinqiyangqi, ",", 2)
	if len(splitN) == 2 {
		slf.Yinqi, _ = strconv.Atoi(splitN[0])
		slf.Yangqi, _ = strconv.Atoi(splitN[1])
	}
	slf.YXCJX = strings.SplitN(slf.Xiyongjichou, ",", 5)
	slf.QiYunTime, _ = time.Parse(slf.RetJyDate, "2006-01-02 15:04:05")
}

func (slf *GetAllResponse) calculateShishenPower() {
	var (
		result        []*PaipanShishenPowerItem
		rigan         = slf.BaziTupleNewTg[2]
		shishenBfbMap = make(map[string]float64)
		tianganNumMap = make(map[string]int)
		wuxingArr     = strings.Split(slf.SaveLiliangWuXingMingzi, ",")
		tianganArr    = strings.Split(slf.StrshishengNum, "")
		tianganNumArr = strings.Split(slf.ShishengNum, ", ")
	)
	for i, ss := range slf.ShishenArr {
		shishenBfbMap[ss] = slf.ShishenBfbArr[i]
	}
	for i, tg := range tianganArr {
		num, _ := strconv.Atoi(tianganNumArr[i])
		tianganNumMap[tg] = num
	}
	for i := 0; i < len(wuxingArr); i++ {
		shishen1 := BaziRelationMap[rigan][tianganArr[i*2]]
		shishen2 := BaziRelationMap[rigan][tianganArr[i*2+1]]
		var name string
		switch shishen1 {
		case "正印", "偏印":
			name = "印枭"
		case "正官", "七杀":
			name = "官杀"
		case "正财", "偏财":
			name = "财才"
		case "食神", "伤官":
			name = "食伤"
		case "比肩", "劫财":
			name = "比劫"
		}
		result = append(result, &PaipanShishenPowerItem{
			ShiShen:     name,
			Wuxing:      wuxingArr[i],
			TianganArr:  []string{tianganArr[i*2], tianganArr[i*2+1]},
			ShiShenArr:  []string{shishen1, shishen2},
			PowerBfbArr: []float64{shishenBfbMap[shishen1], shishenBfbMap[shishen2]},
			PowerArr:    []int{tianganNumMap[tianganArr[i*2]], tianganNumMap[tianganArr[i*2+1]]},
			TotalBfb:    maths.RoundTo(shishenBfbMap[shishen1]+shishenBfbMap[shishen2], 1),
			TotalPower:  tianganNumMap[tianganArr[i*2]] + tianganNumMap[tianganArr[i*2+1]],
		})
	}
	slf.ShishenPower = result
}

type (
	GetAgeRequest struct {
		Birthtime string `json:"birthtime" binding:"required" example:"2021-01-01 12:00:00"` // 出生时间（公历）
		Gender    string `json:"gender" binding:"required" example:"男"`                      // 性别
		Dayun     string `json:"dayun"`                                                      // 大运干支（可选）
		Liunian   string `json:"liunian"`                                                    // 大运/小运流年干支（可选）
	}
	GetAgeResponse struct {
		AgeS     string `json:"ageS"`         // 年龄实岁
		AgeX     string `json:"ageX"`         // 年龄虚岁
		BaziYear string `json:"current_year"` // 当前年份
	}
)

func (slf *GetAgeResponse) GetAgeS() int {
	ageS, _ := strconv.Atoi(slf.AgeS)
	return ageS
}

func (slf *GetAgeResponse) GetAgeX() int {
	ageX, _ := strconv.Atoi(slf.AgeX)
	return ageX
}

func (slf *GetAgeResponse) GetBaziYear() int {
	baziYear, _ := strconv.Atoi(slf.BaziYear)
	return baziYear
}

type (
	GetDayunLiunianScoreRequest struct {
		Birthtime string `json:"birthtime" binding:"required" example:"2021-01-01 12:00:00"` // 出生时间（公历）
		Gender    string `json:"gender" binding:"required" example:"男"`                      // 性别
	}
	GetDayunLiunianScoreResponse struct {
		Dyun   []int `json:"Dyun"`   // 大运分（移除末尾0值，每两个一组，共二十四组）
		Lnian  []int `json:"Lnian"`  // 流年分（每五个一组，共二十四组，每两组与大运分的一组对应）
		Xxian  []int `json:"Xxian"`  // 小运分（每一个小运年对应一个分数）
		Zscore []int `json:"Zscore"` // 综合分（移除末尾0值）
	}
)

func (slf *GetDayunLiunianScoreResponse) process() {
	var trimZero = func(arr []int) []int {
		end := len(arr)
		for end > 0 && arr[end-1] == 0 {
			end--
		}
		return arr[:end]
	}
	slf.Dyun = trimZero(slf.Dyun)
	slf.Lnian = trimZero(slf.Lnian)
	slf.Xxian = trimZero(slf.Xxian)
	slf.Zscore = trimZero(slf.Zscore)
	array.RangeWithIndex(slf.Dyun, func(i int, _ int) {
		slf.Dyun[i] = int(float32(slf.Dyun[i])*0.7) + 30
	})
	array.RangeWithIndex(slf.Lnian, func(i int, _ int) {
		slf.Lnian[i] = int(float32(slf.Lnian[i])*0.7) + 30
	})
	array.RangeWithIndex(slf.Xxian, func(i int, _ int) {
		slf.Xxian[i] = int(float32(slf.Xxian[i])*0.7) + 30
	})
	array.RangeWithIndex(slf.Zscore, func(i int, _ int) {
		slf.Zscore[i] = int(float32(slf.Zscore[i])*0.7) + 30
	})
}

type (
	GetDayunRequest struct {
		Birthtime string `json:"birthtime"` // 出生时间
		Gender    string `json:"gender"`    // 性别：男/女
	}
	GetDayunResponse struct {
		GetShierDayun []string `json:"getShierDayun"`
		DayunQishi    int      `json:"dayunQishi"`
		DayunJiezhi   int      `json:"dayunJiezhi"`
	}
)

type (
	GetXiaoyunRequest struct {
		Birthtime string `json:"birthtime"` // 出生时间
		Gender    string `json:"gender"`    // 性别：男/女
	}
	GetXiaoyunResponse struct {
		SubYearXiaoyun    int      `json:"subYearXiaoyun"`
		XiaoyunQishi      int      `json:"xiaoyunQishi"`
		XiaoyunJiezhi     int      `json:"xiaoyunJiezhi"`
		GetXiaoyunList    []string `json:"getXiaoyunList"`
		StrXiaoyunLiuYear []string `json:"strXiaoyunLiuYear"`
	}
)

type (
	GetJieqiScoreRequest struct {
		Birthtime   string `json:"birthtime" binding:"required" example:"2021-01-01 12:00:00"`   // 出生时间（公历）
		Gender      string `json:"gender" binding:"required" example:"男"`                        // 性别
		CurrentTime string `json:"currentTime" binding:"required" example:"2021-01-01 12:00:00"` // 当前时间（公历）
	}
	GetJieqiScoreResponse struct {
		DayGanzhiList   []string  `json:"DayGanzhiList"` // 日干支列表
		DayFen          []int     `json:"dayFen"`        // 日分
		DayNum          int       `json:"dayNum"`        // 日数（两个节气交替日+中间的天数）
		JqBegin         string    `json:"jqBegin"`       // 节气开始时间（节气交替时的半天记作一天）
		JqBeginTime     time.Time `json:"-"`             // 节气开始时间
		jqBeginTimeZero time.Time // 节气开始时间（零点）
		JqEnd           string    `json:"jqEnd"`           // 节气结束时间（节气交替时的半天记作一天）
		JqEndTime       time.Time `json:"-"`               // 节气结束时间
		MonthGanzhiList []string  `json:"monthGanzhiList"` // 月干支列表
		NowMonthFen     []int     `json:"nowMonthFen"`     // 当前月分
		PaiyueJq        []string  `json:"paiYueJq"`        // 排月节气
	}
)

func (slf *GetJieqiScoreResponse) Process() {
	jqBeginTimeStr := regexp.MustCompile(`: (.*)`).FindStringSubmatch(slf.JqBegin)[1]
	slf.JqBeginTime, _ = time.Parse(time.DateTime, jqBeginTimeStr)
	jqEndTimeStr := regexp.MustCompile(`: (.*)`).FindStringSubmatch(slf.JqEnd)[1]
	slf.JqEndTime, _ = time.Parse(time.DateTime, jqEndTimeStr)
	slf.jqBeginTimeZero = time.Date(slf.JqBeginTime.Year(), slf.JqBeginTime.Month(), slf.JqBeginTime.Day(), 0, 0, 0, 0, slf.JqBeginTime.Location())
}

func (slf *GetJieqiScoreResponse) GetDayIndex(t time.Time) int {
	return int(t.Sub(slf.jqBeginTimeZero).Hours() / 24)
}

type (
	GetSolarMonthScoreRequest struct {
		Birthtime   string `json:"birthtime" binding:"required" example:"2021-01-01 12:00:00"`   // 出生时间（公历）
		Gender      string `json:"gender" binding:"required" example:"男"`                        // 性别
		CurrentTime string `json:"currentTime" binding:"required" example:"2021-01-01 12:00:00"` // 当前时间（公历）
	}
	GetSolarMonthScoreResponse struct {
		DayGanzhiList          []string  `json:"DayGanzhiList"`
		DayGanzhiListSolar     []string  `json:"DayGanzhiList_solar"`
		DayFen                 []int     `json:"dayFen"`
		DayFenSolar            []int     `json:"dayFen_solar"`
		DayNum                 int       `json:"dayNum"`
		DayNumSolar            int       `json:"dayNum_solar"`
		JqBegin                string    `json:"jqBegin"`
		JqEnd                  string    `json:"jqEnd"`
		MonthGanzhiList        []string  `json:"monthGanzhiList"`
		NowMonthFen            []int     `json:"nowMonthFen"`
		PaiYueJq               []string  `json:"paiYueJq"`
		ShishengNumBfb         []float64 `json:"shishengNumBfb"`
		StrshishengNumShiSheng []string  `json:"strshishengNumShiSheng"`
	}
)

type (
	GetSizhuWuxingXiyongRequest struct {
		Birthtime string `json:"birthtime" binding:"required" example:"2021-01-01 12:00:00"` // 出生时间（公历）
		Gender    string `json:"gender" binding:"required" example:"男"`                      // 性别
	}
	GetSizhuWuxingXiyongResponse struct {
		SaveLiliangNum          string   `json:"saveLiliangNum"`
		SaveLiliangWuXingMingzi string   `json:"saveLiliangWuXingMingzi"`
		ShishengNum             string   `json:"shishengNum"`
		StrshishengNum          string   `json:"strshishengNum"`
		Xiyongjichou            string   `json:"xiyongjichou"`
		XiyongArr               []string `json:"-"`
		Tiangan                 []string `json:"baziTupleNew_tg"`
		Dizhi                   []string `json:"baziTupleNew_dz"`
		Zhuxing                 []string `json:"getShishen_baziTupleNew_Tg_list"`
		Benqi                   []string `json:"tiangan_benqi_tg_List_LiuAll"`
		BenqiShishen            []string `json:"tiangan_benqi_ss_List_LiuAll"`
		Zhongqi                 []string `json:"tiangan_zhongqi_tg_List_LiuAll"`
		ZhongqiShishen          []string `json:"tiangan_zhongqi_ss_List_LiuAll"`
		Yuqi                    []string `json:"tiangan_yuqi_tg_List_LiuAll"`
		YuqiShishen             []string `json:"tiangan_yuqi_ss_List_LiuAll"`
		Riyuan                  string   `json:"riyuan"`
	}
)

func (slf *GetSizhuWuxingXiyongResponse) process() {
	slf.XiyongArr = strings.Split(slf.Xiyongjichou, ",")
	val := reflect.ValueOf(slf)
	if val.Kind() != reflect.Ptr || val.Elem().Kind() != reflect.Struct {
		// never be here
		return
	}
	val = val.Elem()
	for i := 0; i < val.NumField(); i++ {
		field := val.Field(i)
		if field.Kind() == reflect.Slice && field.Type().Elem().Kind() == reflect.String {
			for j := 0; j < field.Len(); j++ {
				elem := field.Index(j)
				if elem.String() == "无" {
					elem.SetString("")
				}
			}
		}
	}
}

type (
	GetRenyuanFenyeRequest struct {
		Datetime string `json:"datetime" binding:"required" example:"2021-01-01 12:00:00"` // 出生时间（公历）
	}
	GetRenyuanFenyeResponse struct {
		Fenye string `json:"fenye"`
	}
)

type (
	GetShizhuRequest struct {
		Birthtime string `json:"birthtime"` // 出生时间
	}
	GetShizhuResponse struct {
		Nianzhu string `json:"nianzhu"` // 年柱
		Yuezhu  string `json:"yuezhu"`  // 月柱
		Rizhu   string `json:"rizhu"`   // 日柱
		Shizhu  string `json:"shizhu"`  // 时柱
		Zodiac  string `json:"zodiac"`  // 生肖
	}
)

type (
	GetShenshaRequest struct {
		Bazi   []string `json:"bazi"`   // 八字
		Gender string   `json:"gender"` // 性别
	}
	GetShenshaResponse = ShenshaResult
)

type (
	ShenshaResult struct {
		Data []string
	}
)

func (slf *ShenshaResult) GetAll() [][]string {
	var res [][]string
	for i := 0; i < 8; i++ {
		res = append(res, slf.GetByIndex(i))
	}
	return res
}

func (slf *ShenshaResult) GetByIndex(index int) []string {
	set := make(map[string]struct{})
	for _, item := range slf.Data {
		parts := strings.Split(item, ",")
		if len(parts) < 4 {
			continue
		}
		indices := strings.Trim(parts[3], "[]")
		indexList := strings.Split(indices, " ")

		for _, idx := range indexList {
			if idx == fmt.Sprint(index) {
				set[parts[0]] = struct{}{}
				break
			}
		}
	}
	result := make([]string, 0, len(set))
	for shenSha := range set {
		result = append(result, shenSha)
	}
	return result
}

type (
	GetPaipanBaseInfoRequest struct {
		Birthday   string   `json:"birthday"`   // 生日
		Gender     int      `json:"gender"`     // 	性别
		IsLunar    bool     `json:"isLunar"`    // 是否农历
		Location   []string `json:"location"`   // 地址
		Name       string   `json:"name"`       // 姓名
		IsInternal bool     `json:"isInternal"` // 是否内部调用
	}
	GetPaipanBaseInfoResponseData struct {
		Name          string   `json:"name"`
		Gender        string   `json:"gender"`
		Birthday      string   `json:"birthday"`
		LunarBirthday string   `json:"lunarBirthday"`
		Location      string   `json:"location"`
		Zodiac        string   `json:"zodiac"`
		RealTime      string   `json:"realTime"`
		JqBegin       string   `json:"jqBegin"`
		JqEnd         string   `json:"jqEnd"`
		RetQyStr      string   `json:"retQyStr"`
		RetJyStr      string   `json:"retJyStr"`
		Wanshuai      string   `json:"wanshuai"`
		Xiyongjichou  []string `json:"xiyongjichou"`
	}
	GetPaipanBaseInfoResponse struct {
		Code    int                            `json:"code"`
		Message string                         `json:"message"`
		Data    *GetPaipanBaseInfoResponseData `json:"data"`
	}
)

type (
	GetPaipanJiuizhuRequest struct {
		Birthday   string `json:"birthday"`    // 出生时间
		CurrentDay string `json:"currentDate"` // 当前时间
		Gender     int    `json:"gender"`      // 性别
	}
	GetPaipanJiuzhuResponseData struct {
		Tiangan        []string   `json:"tiangan"`        // 天干
		Dizhi          []string   `json:"dizhi"`          // 地支
		Zhuxing        []string   `json:"zhuxing"`        // 主星
		Benqi          []string   `json:"benqi"`          // 本气
		BenqiShishen   []string   `json:"benqiShishen"`   // 本气十神
		Zhongqi        []string   `json:"zhongqi"`        // 中气
		ZhongqiShishen []string   `json:"zhongqiShishen"` // 中气十神
		Yuqi           []string   `json:"yuqi"`           // 余气
		YuqiShishen    []string   `json:"yuqiShishen"`    // 余气十神
		ShenShaJiShen  [][]string `json:"shenShaJiShen"`  // 神煞
		Nayin          []string   `json:"nayin"`          // 纳音
		Kongwang       []string   `json:"kongwang"`       // 空亡
		Xingyun        []string   `json:"xingyun"`        // 星运
		Zizuo          []string   `json:"zizuo"`          // 自坐
	} // 索引：0-年柱、1-月柱、2-日柱、3-时柱、4-大运、5-流年、6-流月、7-流日、8-流时
	GetPaipanJiuzhuResponse struct {
		Code    int                          `json:"code"`
		Message string                       `json:"message"`
		Data    *GetPaipanJiuzhuResponseData `json:"data"`
	}
)

type (
	LifeCyclesMonthRequest struct {
		Birthday     string   `json:"birthday" binding:"required" example:"2000-06-05 10:00:00"` // 生日 2000-01-01 12:00:00
		Location     []string `json:"location" binding:"required" example:"云南省,昆明市,西山区"`         // 位置
		Gender       int      `json:"gender" example:"1"`                                        // 性别 1:男 2:女
		DaYun        string   `json:"daYun" example:"癸未"`                                        // 大运
		LiuNian      string   `json:"liuNian" example:"癸未"`                                      // 流年
		LiuYue       string   `json:"liuYue" example:"癸未"`                                       // 流月
		LiuRi        string   `json:"liuRi" example:"癸未"`                                        // 流日
		LiuShi       string   `json:"liuShi" example:"癸未"`                                       // 流时
		CurrentYear  int      `json:"currentYear" example:"2024"`                                // 当前选择的流年,返回流月
		CurrentJieQi string   `json:"currentJieQi" example:"2024-02-04 16:26:53"`                // 节气，用于计算流日
		CurrentDay   int      `json:"currentDay" example:"30"`                                   // 当前选择的流日，返回流时
		IsTai        bool     `json:"isTai" example:"false"`                                     // 是否胎生命
		Now          string   `json:"now" example:"2000-06-05 10:00:00"`
	}
	LifeCyclesMonthResponseData struct {
		LiuYue       []LiuYueItem `json:"liuYue"`
		LiuRi        []LiuRiItem  `json:"liuRi"`
		LiuShi       []LiuShiItem `json:"liuShi"`
		Tiangan      []string     `json:"tiangan" example:"天干"`        // 天干 baziTupleNew_tg
		Dizhi        []string     `json:"dizhi" example:"地支"`          // 地支 baziTupleNew_dz
		ZhuXing      []string     `json:"zhuXing" example:"主星"`        // 主星 getShishen_baziTupleNew_Tg_list
		TianGanLiuYi string       `json:"tianGanLiuYi" example:"天干留意"` // 天干留意 tiangan_liuyi_tg_List_LiuAll
		DiZhiLiuYi   string       `json:"diZhiLiuYi" example:"地支留意"`   // 地支留意 dizhi_liuyi_dz_List_LiuAll
		// hehua
		Benqi               []string         `json:"benqi" example:"本气"`            // 本气(藏干第一行) tiangan_benqi_tg_List_LiuAll
		BenqiShiShen        []string         `json:"benqiShiShen" example:"本气十神"`   // 本气十神(藏干第一行) tiangan_benqi_ss_List_LiuAll
		Zhongqi             []string         `json:"zhongqi" example:"中气"`          // 中气(藏干第二行) tiangan_zhongqi_tg_List_LiuAll
		ZhongqiShiShen      []string         `json:"zhongqiShiShen" example:"中气十神"` // 中气十神(藏干第二行) tiangan_zhongqi_ss_List_LiuAll
		Yuqi                []string         `json:"yuqi" example:"余气"`             // 余气(藏干第三行) tiangan_yuqi_tg_List_LiuAll
		YuqiShiShen         []string         `json:"yuqiShiShen" example:"余气十神"`    // 余气十神(藏干第三行) tiangan_yuqi_ss_List_LiuAll
		TianGanWuHe         [][]interface{}  `json:"tianGanWuHe"`                   // 天干五合
		TianGanXiangChong   [][]int          `json:"tianGanXiangChong"`             // 天干相冲
		TianGanXiangKe      [][]int          `json:"tianGanXiangKe"`                // 天干相克
		DiZhiAnHe           [][]int          `json:"diZhiAnHe"`                     //	暗合
		DiZhiLiuHe          [][]interface{}  `json:"diZhiLiuHe"`                    // 六合
		DiZhiSanHe          [][]interface{}  `json:"diZhiSanHe"`                    // 三合
		DiZhiSanHeBanHe     [][]interface{}  `json:"diZhiSanHeBanHe"`               // 三合 半合
		DiZhiSanHeAnHe      [][]interface{}  `json:"diZhiSanHeAnHe"`                // 三合 暗合
		DiZhiSanHeGongHe    [][]interface{}  `json:"diZhiSanHeGongHe"`              // 三合 拱合
		DiZhiSanHui         [][]interface{}  `json:"diZhiSanHui"`                   // 三会
		DiZhiSanHuiAn       [][]interface{}  `json:"diZhiSanHuiAn"`                 // 三会 暗合
		DiZhiSanHuiGong     [][]interface{}  `json:"diZhiSanHuiGong"`               // 三会 拱合
		DiZhiSanXing        [][]int          `json:"diZhiSanXing"`                  // 三刑
		DiZhiXiangXing      [][]int          `json:"diZhiXiangXing"`                // 相刑
		DiZhiXiangChong     [][]int          `json:"diZhiXiangChong"`               // 相冲
		DiZhiXiangHai       [][]int          `json:"diZhiXiangHai"`                 // 相害
		DiZhiXiangPo        [][]int          `json:"diZhiXiangPo"`                  // 相破
		SiLing              string           `json:"siLing"`                        // 司令
		ShenShaJiShen       [][]string       `json:"shenShaJiShen"`                 // 神煞吉神（坑比数据，需要手动按柱组合）
		ShenShaExplainYear  []ShenShaExplain `json:"shenShaExplainYear"`            // 神煞解释
		ShenShaExplainMonth []ShenShaExplain `json:"shenShaExplainMonth"`           // 神煞解释
		ShenShaExplainDay   []ShenShaExplain `json:"shenShaExplainDay"`             // 神煞解释
		ShenShaExplainHour  []ShenShaExplain `json:"shenShaExplainHour"`            // 神煞解释
		NaYin               []string         `json:"naYin"`                         // 纳音
		ZiZuo               []string         `json:"ziZuo"`                         // 自坐
		KongWang            []string         `json:"kongWang"`                      // 空亡
		XingYun             []string         `json:"xingYun"`                       // 星运
	}
	LifeCyclesMonthResponse struct {
		Code    int                         `json:"code"`
		Message string                      `json:"message"`
		Data    LifeCyclesMonthResponseData `json:"data"`
	}
	LiuYueItem struct {
		JieQi    string `json:"jieQi"`    // 节气
		Riqi     string `json:"riqi"`     // 日期
		GanZhi   string `json:"ganZhi"`   // 干支
		ShiShen1 string `json:"shiShen1"` // 十神
		ShiShen2 string `json:"shiShen2"` // 十神
	}
	LiuRiItem struct {
		PublicRiqi string `json:"publicRiqi"` // 公历日期
		Riqi       string `json:"riqi"`       // 农历日期
		RiqiZH     string `json:"riqiZH"`     // 日期中文
		GanZhi     string `json:"ganZhi"`     // 干支
		ShiShen1   string `json:"shiShen1"`   // 十神
		ShiShen2   string `json:"shiShen2"`   // 十神
	}
	LiuShiItem struct {
		Shijian   string `json:"shijian"`   // 时间
		ShiGanZhi string `json:"shiGanZhi"` // 时干支
		ShiShen1  string `json:"shiShen1"`  // 十神
		ShiShen2  string `json:"shiShen2"`  // 十神
	}
	ShenShaExplain struct {
		Name    string `json:"name"`    // 神煞名称
		Explain string `json:"explain"` // 神煞解释
	}
)

type (
	GetPaipanDetailRequest struct {
		Birthday string   `json:"birthday"` // 生日
		Gender   int      `json:"gender"`   // 	性别
		IsLunar  bool     `json:"isLunar"`  // 是否农历
		Location []string `json:"location"` // 地址
		Name     string   `json:"name"`     // 姓名
	}
	GetPaipanDetailResponseData struct {
		Xingyun         []string `json:"xingyun"`         // 星运
		Zizuo           []string `json:"zizuo"`           // 自坐
		KongwangList    []string `json:"kongwangList"`    // 空亡
		Naying          []string `json:"naying"`          // 纳音
		Renyuan         string   `json:"renyuan"`         // 日元
		Tianganliuyi    string   `json:"tianganliuyi"`    // 天干流移
		Dizhiliuyi      string   `json:"dizhiliuyi"`      // 地支流移
		Xingxiu         string   `json:"xingxiu"`         // 星宿
		Xingzuo         string   `json:"xingzuo"`         // 星座
		Taiyuan         string   `json:"taiyuan"`         // 胎元
		Kongwang        string   `json:"kongwang"`        // 空亡
		Minggong        string   `json:"minggong"`        // 命宫
		Taixi           string   `json:"taixi"`           // 胎息
		Shengong        string   `json:"shengong"`        // 生宫
		Minggua         string   `json:"minggua"`         // 命卦
		GejuCankao      string   `json:"gejuCankao"`      // 格局参考
		Sanhe           string   `json:"sanhe"`           // 三合
		Liuhe           string   `json:"liuhe"`           // 六合
		Xiangchong      string   `json:"xiangchong"`      // 相冲
		Xianghai        string   `json:"xianghai"`        // 相害
		Piyu            string   `json:"piyu"`            // 破害
		LiuTongNum      int      `json:"liuTongNum"`      // 流通数
		ZuaiNum         int      `json:"zuaiNum"`         // 罪数
		Pingfen         string   `json:"pingfen"`         // 评分
		TiaoHouYongShen string   `json:"tiaoHouYongShen"` // 调后用神
		WenChang        string   `json:"wenChang"`        // 文昌
		TianYi          string   `json:"tianYi"`          // 天乙
		Overview        string   `json:"overview"`        // 概述
		Song            string   `json:"song"`            // 送
		Comment         string   `json:"comment"`         // 评价
		YueWeight       float64  `json:"yueWeight"`       // 月权重
		ShiWeight       float64  `json:"shiWeight"`       // 时权重
		RiWeight        float64  `json:"riWeight"`        // 日权重
		GanzhiWeight    float64  `json:"ganzhiWeight"`    // 干支权重
		TotalWeight     float64  `json:"totalWeight"`     // 总权重
	}
	GetPaipanDetailResponse struct {
		Code    int                          `json:"code"`
		Message string                       `json:"message"`
		Data    *GetPaipanDetailResponseData `json:"data"`
	}
)

type (
	PaipanBaziLiutongGraph struct {
		Nodes []string `json:"nodes"`
		Edges []struct {
			From      string `json:"from"`
			To        string `json:"to"`
			Relation  string `json:"relation"`
			IsSingle  bool   `json:"isSingle"`
			IsReverse bool   `json:"isReverse"`
		} `json:"edges"`
		Weight map[string]any `json:"weight"`
	}
	PaipanShishenPowerItem struct {
		Wuxing      string    `json:"attr"`                  // 五行
		TianganArr  []string  `json:"tianganArr"`            // 天干数组
		ShiShenArr  []string  `json:"shiShen"`               // 十神数组
		ShiShen     string    `json:"shiShenName,omitempty"` // 十神合称
		PowerBfbArr []float64 `json:"power"`                 // 能量占比数组
		TotalBfb    float64   `json:"totalBfb"`              // 总能量占比
		PowerArr    []int     `json:"powerArr"`              // 能量数组
		TotalPower  int       `json:"totalPower"`            // 总能量
		Num         int       `json:"num,omitempty"`         // 五行数量
		CangNum     int       `json:"cangNum,omitempty"`     // 藏干数量
		Liliang     int       `json:"liliang,omitempty"`     // 力量
	}
	GetPaipanBaziMapRequest struct {
		Birthday string   `json:"birthday"` // 生日
		Gender   int      `json:"gender"`   // 	性别
		IsLunar  bool     `json:"isLunar"`  // 是否农历
		Location []string `json:"location"` // 地址
		Name     string   `json:"name"`     // 姓名
	}
	GetPaipanBaziMapResponseData struct {
		TongYi           []int                     `json:"tongYi"`           // 同异
		TongYiPercent    []float64                 `json:"tongYiPercent"`    // 同异百分比
		TongYiShiShen    []string                  `json:"tongYiShiShen"`    // 同异 十神
		WuxingPower      []float64                 `json:"wuxingPower"`      // 五行力量
		WuxingNum        []int                     `json:"wuxingNum"`        // 五行个数顺序水,木,火,土,金
		WuxingPercent    []float64                 `json:"wuxingPercent"`    // 五行百分比顺序水,木,火,土,金
		WuxingCanNum     []int                     `json:"wuxingCanNum"`     // 五行藏干个数顺序水,木,火,土,金
		WuxingCanPercent []float64                 `json:"wuxingCanPercent"` // 五行藏干百分比顺序水,木,火,土,金
		YingYang         []int                     `json:"yingYang"`         // 阴阳数量
		YingYangPercent  []float64                 `json:"yingYangPercent"`  // 阴阳百分比
		ShishenMapPower  []*PaipanShishenPowerItem `json:"shishenMapPower"`  // 十神能量
		TianGan          []string                  `json:"tiangan"`          // 天干
		Dizhi            []string                  `json:"dizhi"`            // dizhi
		TianGanShiShen   []string                  `json:"tianganShishen"`   // 天干
		DizhiShiShen     []string                  `json:"dizhiShishen"`     // dizhi
		MapTransfer      PaipanBaziLiutongGraph    `json:"graph"`            // 流通图
		LiuTongNum       int                       `json:"liuTongNum"`       // 流通个数
		ZuaiNum          int                       `json:"zuaiNum"`          // 阻碍个数
		Pingfen          string                    `json:"pingfen"`          // 评分
		NianGanQingshu   []string                  `json:"-"`                // 年干亲属
		YueGanQingshu    []string                  `json:"-"`                // 月干亲属
		RiGanQingshu     []string                  `json:"-"`                // 日干亲属
		ShiGanQingshu    []string                  `json:"-"`                // 时干亲属
		NianZhiQingShu   []string                  `json:"-"`                // 年支亲属
		YueZhiQingShu    []string                  `json:"-"`                // 月支亲属
		RiZhiQingShu     []string                  `json:"-"`                // 日支亲属
		ShiZhiQingShu    []string                  `json:"-"`                // 时支亲属
		TianQingshu      [][]string                `json:"tianQingShu"`      // 天干亲属
		DiZhiQingshu     [][]string                `json:"diZhiQingShu"`     // 地支亲属
		SaveWxxqsMingzi  []string                  `json:"qsMingzi"`         // 火旺,土相,木休,水囚,金死
		NianGanSocial    []string                  `json:"-"`                // 年干亲属
		YueGanSocial     []string                  `json:"-"`                // 月干亲属
		RiGanSocial      []string                  `json:"-"`                // 日干亲属
		ShiGanSocial     []string                  `json:"-"`                // 时干亲属
		NianZhiSocial    []string                  `json:"-"`                // 年支亲属
		YueZhiSocial     []string                  `json:"-"`                // 月支亲属
		RiZhiSocial      []string                  `json:"-"`                // 日支亲属
		ShiZhiSocial     []string                  `json:"-"`                // 时支亲属
		TianSocial       [][]string                `json:"tianSocial"`       // 天干亲属
		DiZhiSocial      [][]string                `json:"diZhiSocial"`      // 地支亲属
	}
	GetPaipanBaziMapResponse struct {
		Code    int                           `json:"code"`
		Message string                        `json:"message"`
		Data    *GetPaipanBaziMapResponseData `json:"data"`
	}
)

type (
	Shishen1zhu struct {
		Tg string
		Dz string
		Bq string
		Zq string
		Yq string
	}
	ShishenGetter struct {
		Tg            []string
		Dz            []string
		Bq            []string
		Zq            []string
		Yq            []string
		all           []string
		cangan        []string
		ganzhi        []string
		NumMap        map[string]int
		NumMapList    []map[string]int
		NumMap4Tg     map[string]int
		NumMap4TgList []map[string]int
		NumMap4Dz     map[string]int
		NumMap4DzList []map[string]int
		NumMap4Cg     map[string]int
		NumMap4CgList []map[string]int
	}
)

func NewShishenGetter(tgSs, bqSs, zqSs, yqSs []string) *ShishenGetter {
	obj := &ShishenGetter{
		Tg: tgSs,
		Dz: bqSs,
		Bq: bqSs,
		Zq: zqSs,
		Yq: yqSs,
	}
	obj.NumMap4Tg, obj.NumMap4TgList = func() (map[string]int, []map[string]int) {
		var all = make(map[string]int)
		var list = make([]map[string]int, 0)
		for i, s := range obj.Tg {
			list = append(list, make(map[string]int))
			switch s {
			case "比肩":
				all["比肩"]++
				list[i]["比肩"]++
			case "劫财":
				all["劫财"]++
				list[i]["劫财"]++
			case "食神":
				all["食神"]++
				list[i]["食神"]++
			case "伤官":
				all["伤官"]++
				list[i]["伤官"]++
			case "正财":
				all["正财"]++
				list[i]["正财"]++
			case "偏财":
				all["偏财"]++
				list[i]["偏财"]++
			case "正官":
				all["正官"]++
				list[i]["正官"]++
			case "七杀":
				all["七杀"]++
				list[i]["七杀"]++
			case "正印":
				all["正印"]++
				list[i]["正印"]++
			case "偏印":
				all["偏印"]++
				list[i]["偏印"]++
			}
			list[i]["比劫"] = list[i]["比肩"] + list[i]["劫财"]
			list[i]["食伤"] = list[i]["食神"] + list[i]["伤官"]
			list[i]["财才"] = list[i]["正财"] + list[i]["偏财"]
			list[i]["官杀"] = list[i]["正官"] + list[i]["七杀"]
			list[i]["印枭"] = list[i]["正印"] + list[i]["偏印"]
		}
		all["比劫"] = all["比肩"] + all["劫财"]
		all["食伤"] = all["食神"] + all["伤官"]
		all["财才"] = all["正财"] + all["偏财"]
		all["官杀"] = all["正官"] + all["七杀"]
		all["印枭"] = all["正印"] + all["偏印"]
		return all, list
	}()
	obj.NumMap4Dz, obj.NumMap4DzList = func() (map[string]int, []map[string]int) {
		var all = make(map[string]int)
		var list = make([]map[string]int, 0)
		for i, s := range obj.Dz {
			list = append(list, make(map[string]int))
			switch s {
			case "比肩":
				all["比肩"]++
				list[i]["比肩"]++
			case "劫财":
				all["劫财"]++
				list[i]["劫财"]++
			case "食神":
				all["食神"]++
				list[i]["食神"]++
			case "伤官":
				all["伤官"]++
				list[i]["伤官"]++
			case "正财":
				all["正财"]++
				list[i]["正财"]++
			case "偏财":
				all["偏财"]++
				list[i]["偏财"]++
			case "正官":
				all["正官"]++
				list[i]["正官"]++
			case "七杀":
				all["七杀"]++
				list[i]["七杀"]++
			case "正印":
				all["正印"]++
				list[i]["正印"]++
			case "偏印":
				all["偏印"]++
				list[i]["偏印"]++
			}
			list[i]["比劫"] = list[i]["比肩"] + list[i]["劫财"]
			list[i]["食伤"] = list[i]["食神"] + list[i]["伤官"]
			list[i]["财才"] = list[i]["正财"] + list[i]["偏财"]
			list[i]["官杀"] = list[i]["正官"] + list[i]["七杀"]
			list[i]["印枭"] = list[i]["正印"] + list[i]["偏印"]
		}
		all["比劫"] = all["比肩"] + all["劫财"]
		all["食伤"] = all["食神"] + all["伤官"]
		all["财才"] = all["正财"] + all["偏财"]
		all["官杀"] = all["正官"] + all["七杀"]
		all["印枭"] = all["正印"] + all["偏印"]
		return all, list
	}()
	obj.NumMap4Cg, obj.NumMap4CgList = func() (map[string]int, []map[string]int) {
		var all = make(map[string]int)
		var list = make([]map[string]int, 0)
		cg := [][]string{
			{obj.Bq[0], obj.Zq[0], obj.Yq[0]},
			{obj.Bq[1], obj.Zq[1], obj.Yq[1]},
			{obj.Bq[2], obj.Zq[2], obj.Yq[2]},
			{obj.Bq[3], obj.Zq[3], obj.Yq[3]},
		}
		for i, arr := range cg {
			list = append(list, make(map[string]int))
			for _, s := range arr {
				switch s {
				case "比肩":
					all["比肩"]++
					list[i]["比肩"]++
				case "劫财":
					all["劫财"]++
					list[i]["劫财"]++
				case "食神":
					all["食神"]++
					list[i]["食神"]++
				case "伤官":
					all["伤官"]++
					list[i]["伤官"]++
				case "正财":
					all["正财"]++
					list[i]["正财"]++
				case "偏财":
					all["偏财"]++
					list[i]["偏财"]++
				case "正官":
					all["正官"]++
					list[i]["正官"]++
				case "七杀":
					all["七杀"]++
					list[i]["七杀"]++
				case "正印":
					all["正印"]++
					list[i]["正印"]++
				case "偏印":
					all["偏印"]++
					list[i]["偏印"]++
				}
			}
			list[i]["比劫"] = list[i]["比肩"] + list[i]["劫财"]
			list[i]["食伤"] = list[i]["食神"] + list[i]["伤官"]
			list[i]["财才"] = list[i]["正财"] + list[i]["偏财"]
			list[i]["官杀"] = list[i]["正官"] + list[i]["七杀"]
			list[i]["印枭"] = list[i]["正印"] + list[i]["偏印"]
		}
		all["比劫"] = all["比肩"] + all["劫财"]
		all["食伤"] = all["食神"] + all["伤官"]
		all["财才"] = all["正财"] + all["偏财"]
		all["官杀"] = all["正官"] + all["七杀"]
		all["印枭"] = all["正印"] + all["偏印"]
		return all, list
	}()
	obj.NumMap = map[string]int{
		"比肩": obj.NumMap4Tg["比肩"] + obj.NumMap4Cg["比肩"],
		"劫财": obj.NumMap4Tg["劫财"] + obj.NumMap4Cg["劫财"],
		"比劫": obj.NumMap4Tg["比劫"] + obj.NumMap4Cg["比劫"],
		"食神": obj.NumMap4Tg["食神"] + obj.NumMap4Cg["食神"],
		"伤官": obj.NumMap4Tg["伤官"] + obj.NumMap4Cg["伤官"],
		"食伤": obj.NumMap4Tg["食伤"] + obj.NumMap4Cg["食伤"],
		"正财": obj.NumMap4Tg["正财"] + obj.NumMap4Cg["正财"],
		"偏财": obj.NumMap4Tg["偏财"] + obj.NumMap4Cg["偏财"],
		"财才": obj.NumMap4Tg["财才"] + obj.NumMap4Cg["财才"],
		"正官": obj.NumMap4Tg["正官"] + obj.NumMap4Cg["正官"],
		"七杀": obj.NumMap4Tg["七杀"] + obj.NumMap4Cg["七杀"],
		"官杀": obj.NumMap4Tg["官杀"] + obj.NumMap4Cg["官杀"],
		"正印": obj.NumMap4Tg["正印"] + obj.NumMap4Cg["正印"],
		"偏印": obj.NumMap4Tg["偏印"] + obj.NumMap4Cg["偏印"],
		"印枭": obj.NumMap4Tg["印枭"] + obj.NumMap4Cg["印枭"],
	}
	obj.NumMapList = make([]map[string]int, 0)
	for i := 0; i < 4; i++ {
		obj.NumMapList = append(obj.NumMapList, map[string]int{
			"比肩": obj.NumMap4TgList[i]["比肩"] + obj.NumMap4CgList[i]["比肩"],
			"劫财": obj.NumMap4TgList[i]["劫财"] + obj.NumMap4CgList[i]["劫财"],
			"比劫": obj.NumMap4TgList[i]["比劫"] + obj.NumMap4CgList[i]["比劫"],
			"食神": obj.NumMap4TgList[i]["食神"] + obj.NumMap4CgList[i]["食神"],
			"伤官": obj.NumMap4TgList[i]["伤官"] + obj.NumMap4CgList[i]["伤官"],
			"食伤": obj.NumMap4TgList[i]["食伤"] + obj.NumMap4CgList[i]["食伤"],
			"正财": obj.NumMap4TgList[i]["正财"] + obj.NumMap4CgList[i]["正财"],
			"偏财": obj.NumMap4TgList[i]["偏财"] + obj.NumMap4CgList[i]["偏财"],
			"财才": obj.NumMap4TgList[i]["财才"] + obj.NumMap4CgList[i]["财才"],
			"正官": obj.NumMap4TgList[i]["正官"] + obj.NumMap4CgList[i]["正官"],
			"七杀": obj.NumMap4TgList[i]["七杀"] + obj.NumMap4CgList[i]["七杀"],
			"官杀": obj.NumMap4TgList[i]["官杀"] + obj.NumMap4CgList[i]["官杀"],
			"正印": obj.NumMap4TgList[i]["正印"] + obj.NumMap4CgList[i]["正印"],
			"偏印": obj.NumMap4TgList[i]["偏印"] + obj.NumMap4CgList[i]["偏印"],
			"印枭": obj.NumMap4TgList[i]["印枭"] + obj.NumMap4CgList[i]["印枭"],
		})
	}
	merged := array.Merge(obj.Tg, obj.Bq, obj.Zq, obj.Yq)
	obj.all = array.Unique(merged)
	obj.cangan = array.Merge(obj.Bq, obj.Zq, obj.Yq)
	obj.ganzhi = array.Merge(obj.Tg, obj.Dz)
	return obj
}

func (slf *ShishenGetter) All() []string {
	return slf.all
}

func (slf *ShishenGetter) GanZhi() []string {
	return slf.ganzhi
}

func (slf *ShishenGetter) CangGan() []string {
	return slf.cangan
}

func (slf *ShishenGetter) TgListByIdx(idx ...int) []string {
	var ret []string
	for _, i := range idx {
		ret = append(ret, slf.Tg[i])
	}
	return ret
}

func (slf *ShishenGetter) TgSearch(target string, idx ...int) int {
	return array.Index(slf.Tg, target, idx...)
}

func (slf *ShishenGetter) DzSearch(target string, idx ...int) int {
	return array.Index(slf.Dz, target, idx...)
}

func (slf *ShishenGetter) CgSearch(target string, idx ...int) int {
	if len(idx) == 0 {
		idx = []int{0, 1, 2, 3}
	}
	for _, i := range idx {
		if array.Has(slf.CgByIdx(i), target) {
			return i
		}
	}
	return -1
}

func (slf *ShishenGetter) DzListByIdx(idx ...int) []string {
	var ret []string
	for _, i := range idx {
		ret = append(ret, slf.Dz[i])
	}
	return ret
}

func (slf *ShishenGetter) TgDzByIdx(idx ...int) [][]string {
	var ret [][]string
	for _, i := range idx {
		ret = append(ret, []string{slf.Tg[i], slf.Dz[i]})
	}
	return ret
}

func (slf *ShishenGetter) TgDzListByIdx(idx ...int) []string {
	var tmp [][]string
	for _, i := range idx {
		tmp = append(tmp, []string{slf.Tg[i], slf.Dz[i]})
	}
	var ret []string
	for _, item := range tmp {
		ret = array.Merge(ret, item)
	}
	return ret
}

func (slf *ShishenGetter) CgByIdx(idx int) []string {
	return []string{slf.Bq[idx], slf.Zq[idx], slf.Yq[idx]}
}

func (slf *ShishenGetter) CgListByIdx(idx ...int) []string {
	var tmp [][]string
	for _, i := range idx {
		tmp = append(tmp, []string{slf.Bq[i], slf.Zq[i], slf.Yq[i]})
	}
	var ret []string
	for _, item := range tmp {
		ret = array.Merge(ret, item)
	}
	return ret
}

func (slf *ShishenGetter) ByIdx(idx ...int) []string {
	var tmp []*Shishen1zhu
	for _, i := range idx {
		tmp = append(tmp, &Shishen1zhu{
			Tg: slf.Tg[i],
			Dz: slf.Dz[i],
			Bq: slf.Bq[i],
			Zq: slf.Zq[i],
			Yq: slf.Yq[i],
		})
	}
	var arr []string
	for _, item := range tmp {
		arr = array.Merge(arr, item.Array())
	}
	arr = array.Unique(arr)
	return arr
}

func (slf *Shishen1zhu) Array() []string {
	return []string{
		slf.Tg,
		slf.Dz,
		slf.Bq,
		slf.Zq,
		slf.Yq,
	}
}

func (slf *Shishen1zhu) GetTgDzSs() []string {
	return []string{slf.Tg, slf.Dz}
}

func (slf *Shishen1zhu) GetCgSs() []string {
	return []string{
		slf.Bq,
		slf.Zq,
		slf.Yq,
	}
}

func (slf *ShishenGetter) Contains(ss string) bool {
	return slf.NumMap[ss] > 0
}

func (slf *ShishenGetter) TgContains(ss string) bool {
	return slf.NumMap4Tg[ss] > 0
}

func (slf *ShishenGetter) DzContains(ss string) bool {
	return slf.NumMap4Dz[ss] > 0
}
func (slf *ShishenGetter) CgContains(ss string) bool {
	return slf.NumMap4Cg[ss] > 0
}
