package wecom

import (
	"github.com/ArtisanCloud/PowerWeChat/v3/src/kernel"
	"github.com/ArtisanCloud/PowerWeChat/v3/src/work"
	"github.com/spf13/viper"
	"go.uber.org/zap"
	"zodiacus/pkg/log"
)

type Application = work.Work

// NewDoraemon 初始化企业微信助手应用-哆啦A梦
func NewDoraemon(conf *viper.Viper, l *log.Logger) *Application {
	cache := kernel.NewRedisClient(&kernel.UniversalOptions{
		Addrs:    []string{conf.GetString("data.redis.addr")},
		DB:       conf.GetInt("data.redis.db"),
		Password: conf.GetString("data.redis.password"),
	})
	cli, err := work.NewWork(&work.UserConfig{
		CorpID:      conf.GetString("wecom.corp_id"),               // 企业微信的CorpID
		AgentID:     conf.GetInt("wecom.doraemon.agent_id"),        // 企业微信应用的AgentID
		Secret:      conf.GetString("wecom.doraemon.secret"),       // 企业微信应用的Secret
		Token:       conf.GetString("wecom.doraemon.token"),        // 企业微信应用的Token
		AESKey:      conf.GetString("wecom.doraemon.aes_key"),      // 企业微信应用的AESKey
		CallbackURL: conf.GetString("wecom.doraemon.callback_url"), // 企业微信应用的回调URL
		OAuth: work.OAuth{
			Callback: conf.GetString("wecom.doraemon.oauth.callback"),
			Scopes:   []string{"snsapi_base"},
		},
		Cache:     cache,
		HttpDebug: false,
		Debug:     false,
	})
	if err != nil {
		l.Panic("failed to init wecom doraemon", zap.Error(err))
	}
	return cli
}
