package submail

import (
	"context"
	"github.com/go-resty/resty/v2"
	"github.com/pkg/errors"
	"github.com/spf13/viper"
)

type (
	Client struct {
		AppID  string
		AppKey string
		sms    *smsClient
	}
	BaseRequestFields struct {
		AppID     string `json:"appid"`
		Signature string `json:"signature"`
	}
	BaseResponseFields struct {
		Status string `json:"status"`
		Code   string `json:"code"`
		Msg    string `json:"msg"`
	}
)

// NewClient returns a new submail client
func NewClient(conf *viper.Viper) *Client {
	cli := &Client{
		AppID:  conf.GetString("submail.app_id"),
		AppKey: conf.GetString("submail.app_key"),
	}
	cli.sms = &smsClient{owner: cli}
	return cli
}

func (slf *Client) SMS() *smsClient {
	return slf.sms
}

func (slf *Client) BaseRequestFields() BaseRequestFields {
	return BaseRequestFields{
		AppID:     slf.AppID,
		Signature: slf.App<PERSON>ey,
	}
}

type smsClient struct {
	owner *Client
}

type (
	SMSXSendRequest struct {
		BaseRequestFields
		To      string         `json:"to"`
		Project string         `json:"project"`
		Vars    map[string]any `json:"vars"`
	}
	SMSXSendResponse struct {
		BaseResponseFields
		SendID string `json:"send_id"`
		Fee    int    `json:"fee"`
	}
)

func (slf *smsClient) XSend(ctx context.Context, req *SMSXSendRequest) (*SMSXSendResponse, error) {
	req.BaseRequestFields = slf.owner.BaseRequestFields()
	var (
		err error
		res SMSXSendResponse
	)
	response, err := resty.New().R().SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetBody(req).
		SetResult(&res).
		Post("https://api-v4.mysubmail.com/sms/xsend")
	if err != nil {
		return nil, err
	}
	if response.IsError() {
		return nil, response.Error().(error)
	}
	if res.Status != "success" {
		return nil, errors.Errorf("failed to send sms, code=%s, msg=%s", res.Code, res.Msg)
	}
	return &res, nil
}
