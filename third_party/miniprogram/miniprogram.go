package miniprogram

import (
	"github.com/ArtisanCloud/PowerWeChat/v3/src/kernel"
	"github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram"
	"github.com/spf13/viper"
	"go.uber.org/zap"
	"zodiacus/pkg/log"
)

type Application = miniProgram.MiniProgram

type Manager struct {
	apps map[string]*Application
}

func (slf *Manager) Get(id string) *Application {
	if app, ok := slf.apps[id]; ok {
		return app
	}
	return nil
}

func NewManager(conf *viper.Viper, l *log.Logger) *Manager {
	cache := kernel.NewRedisClient(&kernel.UniversalOptions{
		Addrs:    []string{conf.GetString("data.redis.addr")},
		DB:       conf.GetInt("data.redis.db"),
		Password: conf.GetString("data.redis.password"),
	})
	apps := make(map[string]*Application)
	miniprograms := conf.Get("miniprogram").([]any)
	for _, item := range miniprograms {
		program := item.(map[string]interface{})
		cli, err := miniProgram.NewMiniProgram(&miniProgram.UserConfig{
			AppID:  program["app_id"].(string),
			Secret: program["secret"].(string),
			Cache:  cache,
		})
		if err != nil {
			l.Panic("failed to init mini program", zap.Error(err))
		}
		apps[program["app"].(string)] = cli
	}
	return &Manager{apps: apps}
}
