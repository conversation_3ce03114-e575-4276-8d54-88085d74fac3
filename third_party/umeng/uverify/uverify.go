package uverify

import (
	"context"
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"github.com/go-resty/resty/v2"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/spf13/viper"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"
)

type (
	UVerify struct {
		appKey     string
		appCode    string
		appSecret  string
		appAndroid *AppConfig
		appIOS     *AppConfig
	}
	AppConfig struct {
		key    string
		secret string
	}
	uverifyBaseResponse struct {
		Success   bool   `json:"success"`
		Code      int    `json:"code"`
		Message   string `json:"message"`
		RequestID string `json:"requestId"`
	}
	getMobileInfoRequest struct {
		Token string `json:"token"`
	}
	getMobileInfoResponseData struct {
		Mobile      string  `json:"mobile"`
		Score       float64 `json:"score"`
		ActiveScore float64 `json:"activeScore"`
	}
	getMobileInfoResponse struct {
		uverifyBaseResponse
		Data *getMobileInfoResponseData `json:"data"`
	}
)

func NewUVerify(conf *viper.Viper) *UVerify {
	return &UVerify{
		appKey:    conf.GetString("app_key"),
		appCode:   conf.GetString("app_code"),
		appSecret: conf.GetString("app_secret"),
		appAndroid: &AppConfig{
			key:    conf.GetString("app.android.key"),
			secret: conf.GetString("app.android.secret"),
		},
		appIOS: &AppConfig{
			key:    conf.GetString("app.ios.key"),
			secret: conf.GetString("app.ios.secret"),
		},
	}
}

func (slf *UVerify) MobileInfo(ctx context.Context, app string, token, verifyId string) (string, error) {
	var (
		aliAppKey = slf.appKey
		appKey    string
	)
	switch app {
	case "android":
		appKey = slf.appAndroid.key
	case "ios":
		appKey = slf.appIOS.key
	default:
		return "", errors.New("unsupported app")
	}
	baseURL := "https://verify5.market.alicloudapi.com/api/v1/mobile/info"
	params := url.Values{}
	params.Set("appkey", appKey)
	params.Set("verifyId", verifyId)
	fullURL := baseURL + "?" + params.Encode()
	timestamp := strconv.FormatInt(time.Now().UnixMilli(), 10)
	nonce := uuid.New().String()
	requestBody := getMobileInfoRequest{Token: token}
	bodyBytes, err := json.Marshal(requestBody)
	if err != nil {
		return "", errors.Wrap(err, "failed to marshal request body")
	}
	contentMD5 := func() string {
		b := md5.Sum(bodyBytes)
		return base64.StdEncoding.EncodeToString(b[:])
	}()
	headers := map[string]string{
		"Content-Type":           "application/json; charset=UTF-8",
		"Accept":                 "application/json",
		"X-Ca-Version":           "1",
		"X-Ca-Stage":             "RELEASE",
		"X-Ca-Key":               aliAppKey,
		"X-Ca-Timestamp":         timestamp,
		"X-Ca-Nonce":             nonce,
		"Content-MD5":            contentMD5,
		"X-Ca-Signature-Headers": "X-Ca-Version,X-Ca-Stage,X-Ca-Key,X-Ca-Timestamp,X-Ca-Nonce",
	}
	signature, err := slf.signature(ctx, "POST", "/api/v1/mobile/info", params, headers)
	if err != nil {
		return "", errors.Wrap(err, "failed to generate signature")
	}
	headers["X-Ca-Signature"] = signature
	var response getMobileInfoResponse
	resp, err := resty.New().R().
		SetContext(ctx).
		SetHeaders(headers).
		SetBody(requestBody).
		SetResult(&response).
		Post(fullURL)
	if err != nil {
		return "", errors.Wrap(err, "failed to make HTTP request")
	}
	if resp.StatusCode() != 200 {
		res := resp.String()
		fmt.Println(res)
		return "", errors.Errorf("HTTP request failed with status %d: %s", resp.StatusCode(), resp.String())
	}
	if !response.Success {
		return "", errors.Errorf("API request failed: code=%d, message=%s", response.Code, response.Message)
	}
	if response.Data == nil {
		return "", errors.New("response data is nil")
	}
	return response.Data.Mobile, nil
}

func (slf *UVerify) signature(ctx context.Context, method, path string, queryParams url.Values, headers map[string]string) (string, error) {
	var builder strings.Builder
	builder.WriteString(method)
	builder.WriteString("\n")
	if accept, ok := headers["Accept"]; ok {
		builder.WriteString(accept)
	}
	builder.WriteString("\n")
	if contentMD5, ok := headers["Content-MD5"]; ok {
		builder.WriteString(contentMD5)
	}
	builder.WriteString("\n")
	if contentType, ok := headers["Content-Type"]; ok {
		builder.WriteString(contentType)
	}
	builder.WriteString("\n")
	builder.WriteString("\n")
	var customHeaders []string
	for key := range headers {
		if strings.HasPrefix(key, "X-Ca-") {
			customHeaders = append(customHeaders, key)
		}
	}
	sort.Strings(customHeaders)
	for _, key := range customHeaders {
		switch key {
		case "X-Ca-Signature", "X-Ca-Signature-Headers", "Accept", "Content-MD5", "Content-Type", "Date":
		default:
			builder.WriteString(key)
			builder.WriteString(":")
			builder.WriteString(headers[key])
			builder.WriteString("\n")
		}
	}
	builder.WriteString(path)
	if len(queryParams) > 0 {
		var keys []string
		for key := range queryParams {
			keys = append(keys, key)
		}
		sort.Strings(keys)
		builder.WriteString("?")
		for i, key := range keys {
			if i > 0 {
				builder.WriteString("&")
			}
			builder.WriteString(key)
			builder.WriteString("=")
			builder.WriteString(queryParams.Get(key))
		}
	}
	h := hmac.New(sha256.New, []byte(slf.appSecret))
	h.Write([]byte(builder.String()))
	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))
	return signature, nil
}
