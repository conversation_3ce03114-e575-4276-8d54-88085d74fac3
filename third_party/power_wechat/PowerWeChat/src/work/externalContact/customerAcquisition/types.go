package customerAcquisition

import "github.com/ArtisanCloud/PowerWeChat/v3/src/kernel/response"

type (
	Link struct {
		LinkID     string `json:"link_id,omitempty"`
		LinkName   string `json:"link_name,omitempty"`
		Url        string `json:"url,omitempty"`
		CreateTime int64  `json:"create_time,omitempty"`
		SkipVerify bool   `json:"skip_verify,omitempty"`
	}
	Range struct {
		UserList       []string `json:"user_list,omitempty"`
		DepartmentList []int    `json:"department_list,omitempty"`
	}
	PriorityOption struct {
		PriorityType       int      `json:"priority_type,omitempty"`
		PriorityUseridList []string `json:"priority_userid_list,omitempty"`
	}
	Customer struct {
		ExternalUserID string `json:"external_userid"`
		UserID         string `json:"userid"`
		ChatStatus     int    `json:"chat_status"`
		State          string `json:"state"`
	}
)

type (
	ListCustomerAcquisitionLinkResp struct {
		response.ResponseWork

		NextCursor string    `json:"next_cursor"`
		LinkIDList []*string `json:"link_id_list"`
	}
)

type (
	GetCustomerAcquisitionLinkResp struct {
		response.ResponseWork

		Link           *Link           `json:"link"`
		Range          *Range          `json:"range"`
		PriorityOption *PriorityOption `json:"priority_option"`
	}
)

type (
	CreateCustomerAcquisitionLinkReq struct {
		LinkName       string          `json:"link_name"`
		SkipVerify     bool            `json:"skip_verify,omitempty"`
		Range          *Range          `json:"range"`
		PriorityOption *PriorityOption `json:"priority_option,omitempty"`
	}
	ResponseCreateCustomerAcquisitionLink struct {
		response.ResponseWork

		Link *Link `json:"link"`
	}
)

type (
	UpdateCustomerAcquisitionLinkReq struct {
		LinkID         string          `json:"link_id"`
		LinkName       string          `json:"link_name,omitempty"`
		SkipVerify     bool            `json:"skip_verify,omitempty"`
		Range          *Range          `json:"range,omitempty"`
		PriorityOption *PriorityOption `json:"priority_option,omitempty"`
	}
	UpdateCustomerAcquisitionLinkResp struct {
		response.ResponseWork
	}
)

type (
	DeleteCustomerAcquisitionLinkResp struct {
		response.ResponseWork
	}
)

type (
	GetCustomerListResp struct {
		response.ResponseWork

		NextCursor   string      `json:"next_cursor"`
		CustomerList []*Customer `json:"customer_list"`
	}
)

type (
	GetQuotaResp struct {
		response.ResponseWork

		Total     int `json:"total"`
		Balance   int `json:"balance"`
		QuotaList []*struct {
			ExpireDate int64 `json:"expire_date"`
			Balance    int   `json:"balance"`
		}
	}
)

type (
	GetCustomerAcquisitionStatisticResp struct {
		response.ResponseWork

		ClickLinkCustomerCnt int `json:"click_link_customer_cnt"`
		NewCustomerCnt       int `json:"new_customer_cnt"`
	}
)
