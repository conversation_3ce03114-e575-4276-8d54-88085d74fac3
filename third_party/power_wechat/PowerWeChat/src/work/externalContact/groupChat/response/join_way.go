package response

import "github.com/ArtisanCloud/PowerWeChat/v3/src/kernel/response"

type AddJoinWayResponse struct {
	response.ResponseWork
	ConfigID string `json:"config_id"`
}

type JoinWay struct {
	ConfigID       string   `json:"config_id"`
	Scene          int      `json:"scene"`
	Remark         string   `json:"remark"`
	AutoCreateRoom int      `json:"auto_create_room"`
	RoomBaseName   string   `json:"room_base_name"`
	RoomBaseID     int      `json:"room_base_id"`
	ChatIDList     []string `json:"chat_id_list"`
	QRCode         string   `json:"qr_code"`
	State          string   `json:"state"`
}

/*
{
	"errcode": 0,
	"errmsg": "ok",
	"join_way": {
		"config_id": "9ad7fa5cdaa6511298498f979c472aaa",
		"scene": 2,
		"remark": "aa_remark",
		"auto_create_room": 1,
		"room_base_name" : "销售客服群",
		"room_base_id" : 10,
		"chat_id_list": ["wrOgQhDgAAH2Yy-CTZ6POca8mlBEdaaa", "wrOgQhDgAALPUthpRAKvl7mgiQRw_aaa"],
		"qr_code": "http://p.qpic.cn/wwhead/nMl9ssowtibVGyrmvBiaibzDtp703nXuzpibnKtbSDBRJTLwS3ic4ECrf3ibLVtIFb0N6wWwy5LVuyvMQ22/0",
		"state" : "klsdup3kj3s1"
	}
}
*/

type GetJoinWayResponse struct {
	response.ResponseWork
	JoinWay *JoinWay `json:"join_way"`
}

type UpdateJoinWayResponse struct {
	response.ResponseWork
}

type DelJoinWayResponse struct {
	response.ResponseWork
}
