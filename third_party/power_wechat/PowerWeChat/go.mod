module github.com/ArtisanCloud/PowerWeChat/v3

go 1.17

//replace github.com/ArtisanCloud/PowerLibs/v3 => ../PowerLibs

//replace github.com/ArtisanCloud/PowerSocialite/v3 => ../PowerSocialite

require (
	github.com/ArtisanCloud/PowerLibs/v3 v3.2.2
	github.com/ArtisanCloud/PowerSocialite/v3 v3.0.7
	github.com/go-playground/assert/v2 v2.0.1
	github.com/redis/go-redis/v9 v9.0.3
	github.com/stretchr/testify v1.8.0
)

require (
	github.com/cespare/xxhash/v2 v2.2.0 // indirect
	github.com/clbanning/mxj/v2 v2.7.0 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/patrickmn/go-cache v2.1.0+incompatible // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	go.uber.org/atomic v1.7.0 // indirect
	go.uber.org/multierr v1.6.0 // indirect
	go.uber.org/zap v1.21.0 // indirect
	gopkg.in/natefinch/lumberjack.v2 v2.2.1 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)
