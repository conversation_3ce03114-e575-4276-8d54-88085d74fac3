package mail

import (
	"github.com/ArtisanCloud/PowerLibs/v3/object"
	"net/mail"
	"os"
	"testing"
	"time"
)

func Test_SendFile(t *testing.T) {
	sender := NewSender(nil)
	m := NewMessage("Test", "test for PowerLib Mail Sender."+time.Now().String())
	//m.To = []string{"<EMAIL>"}
	m.To = []mail.Address{mail.Address{"", "<EMAIL>"}}
	m.CC = []mail.Address{mail.Address{"", "<EMAIL>"}}
	m.BCC = []mail.Address{mail.Address{"", "<EMAIL>"}}
	m.AttachFile(os.Getenv("test_attach_file"))
	err := sender.Send(m)
	if err != nil {
		t.Error(err)
	}
}

func Test_SendData(t *testing.T) {
	sender := NewSender(nil)
	m := NewMessage("Test", "test for PowerLib Mail Sender.")
	m.To = []mail.Address{mail.Address{"", "<EMAIL>"}}
	m.CC = []mail.Address{mail.Address{"", "<EMAIL>"}}
	m.BCC = []mail.Address{mail.Address{"", "<EMAIL>"}}

	data := &object.HashMap{
		"test":  1,
		"test2": "hello",
	}

	strData, err := object.JsonEncode(data)
	if err != nil {
		t.Error(err)
	}

	err = m.AttachData("dataFile.xlsx", []byte(strData))
	if err != nil {
		t.Error(err)
	}

	err = sender.Send(m)
	if err != nil {
		t.Error(err)
	}
}
