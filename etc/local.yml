env: local
http:
  cms:
    host: 0.0.0.0
    port: 60110
  paipan:
    host: 0.0.0.0
    port: 60111
  calendar:
    host: 0.0.0.0
    port: 60112
  fortune:
    host: 0.0.0.0
    port: 60113
  wealth:
    host: 0.0.0.0
    port: 60114
  master:
    host: 0.0.0.0
    port: 60115
  adflow:
    host: 0.0.0.0
    port: 60116
  authcenter:
    host: 0.0.0.0
    port: 60000
security:
  api_sign:
    app_key: 123456
    app_security: 123456
  jwt:
    key: qgSfZOpwcuTCtZxFbK3ob58Ebr2i3Med
    expire: 168h
  jwthub:
    jwtKey: qgSfZOpwcuTCtZxFbK3ob58Ebr2i3Med
    appPrefix: zodiacus:app
    adminPrefix: zodiacus:admin
data:
  db:
    user:
      driver: mysql
      dsn: root_ts:gx_iuOi6uVwgt78ioPlM_we66BnJ@tcp(silkroad.mysql.polardb.rds.aliyuncs.com:3306)/paipan?charset=utf8mb4&parseTime=True&loc=Local
  redis:
    addr: shulingji.redis.rds.aliyuncs.com:6379
    password: "slmt#Sl202204"
    db: 0
    read_timeout: 2s
    write_timeout: 2s
log:
  log_level: debug
  encoding: json
  log_file_name: "./storage/logs/server.log"
  max_backups: 30
  max_age: 7
  max_size: 1024
  compress: true
oss:
  endpoint: oss-cn-hangzhou.aliyuncs.com
  bucket: imp-static
  prefix: dev
  access_key_id: LTAI5tNzPKfJQaBTsHYWyawh
  access_key_secret: ******************************
  base_url: https://imp-static-oss.yaoyoumeng.com
identity:
  endpoint: http://127.0.0.1:9527
corona:
    endpoint:
      core: http://**************:5000
      bazi: https://horoscope.laibuyi.com
wecom:
  corp_id: wwe6d350c03a123ca3
  doraemon:
    agent_id: 1000002
    secret: "c7uuQPaD40wGCsKs2ATARY7ulPCT2DGx4jFo_7sMK3A"
    token: "prpbUIVmFbyHn4MLbEUV1yv9qDVBOVK"
    aes_key: "B5DyvBxXdFCWecxbcxOAUsX3LXSPE7Hr9CsxEc1uvIn"
    callback_url: "https://paipan-cms-api.yaoyoumeng.com/v1/wecom/doraemon"
    oauth:
      callback: "https://paipan-cms-api.yaoyoumeng.com/v1/wecom/doraemon"
offiaccount:
  app_id: wxb842ab09c30e81a9
  secret: 4ebeeae3a9eb17a5829f28e5df474c5e
  token: JLTbmdPnDxFhuQXAYCmFJPIjluAHTwhL
  aes_key: rQsBSPB4f1e79AMKJfsLpGUkWKeCuOUfhNqliRxDtbu
  notify_url: https://paipan-cms-api.yaoyoumeng.com/v1/offiaccount
miniprogram:
  - app: application_horoscope
    app_id: wx4e699f1071dcc922
    secret: 31f1cdb3edb358d5a3f824af2501441b
ip2region:
  path: ./assets/ip2region.xdb
submail:
  app_id: 108520
  app_key: f15a8e687cf015e875e13b736950bca5
aliyun:
  sms:
    access_key_id: LTAI5tSYvBHcr2SnJFH9HChy
    access_key_secret: ******************************
sls:
  enabled: false
  project_name: paipan-prod
  log_store_name: ""
  log_topic: ""
  log_source: ""
  log_content_key: __log__
  endpoint: cn-hangzhou.log.aliyuncs.com
  access_key_id: LTAI5tGm7Lfqd5qWPrxwHkkk
  access_key_secret: ******************************
  security_token: ""
geoip:
  path: ./assets/GeoLite2-City.mmdb
wechat:
  officialaccount:
    app_id: wxb842ab09c30e81a9
    secret: 4ebeeae3a9eb17a5829f28e5df474c5e
    token: JLTbmdPnDxFhuQXAYCmFJPIjluAHTwhL
    aes_key: rQsBSPB4f1e79AMKJfsLpGUkWKeCuOUfhNqliRxDtbu
    notify_url: https://paipan-cms-api.yaoyoumeng.com/v1/offiaccount
  miniprogram:
    app_id: wx4e699f1071dcc922
    secret: 31f1cdb3edb358d5a3f824af2501441b