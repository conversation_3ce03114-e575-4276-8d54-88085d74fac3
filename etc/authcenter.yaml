env: local
http:
  authcenter:
    host: 0.0.0.0
    port: 60000
security:
  jwthub:
    jwtKey: qgSfZOpwcuTCtZxFbK3ob58Ebr2i3Med
    appPrefix: zodiacus:app
    adminPrefix: zodiacus:admin
data:
  db:
    user:
      driver: mysql
      dsn: root_ts:gx_iuOi6uVwgt78ioPlM_we66BnJ@tcp(silkroad.mysql.polardb.rds.aliyuncs.com:3306)/paipan?charset=utf8mb4&parseTime=True&loc=Local
  redis:
    addr: shulingji.redis.rds.aliyuncs.com:6379
    password: "slmt#Sl202204"
    db: 0
    read_timeout: 2s
    write_timeout: 2s
log:
  log_level: debug
  encoding: json
  log_file_name: "./storage/logs/server.log"
  max_backups: 30
  max_age: 7
  max_size: 1024
  compress: true
submail:
  app_id: 108520
  app_key: f15a8e687cf015e875e13b736950bca5
aliyun:
  sms:
    access_key_id: LTAI5tSYvBHcr2SnJFH9HChy
    access_key_secret: ******************************
oss:
  endpoint: oss-cn-hangzhou.aliyuncs.com
  bucket: imp-static
  prefix: dev
  access_key_id: LTAI5tNzPKfJQaBTsHYWyawh
  access_key_secret: ******************************
  base_url: https://imp-static-oss.yaoyoumeng.com
sls:
  enabled: false
  project_name: paipan-prod
  log_store_name: ""
  log_topic: ""
  log_source: ""
  log_content_key: __log__
  endpoint: cn-hangzhou.log.aliyuncs.com
  access_key_id: LTAI5tGm7Lfqd5qWPrxwHkkk
  access_key_secret: ******************************
  security_token: ""
geoip:
  path: ./assets/GeoLite2-City.mmdb
wechat:
  officialaccount:
    app_id: wxb842ab09c30e81a9
    secret: 4ebeeae3a9eb17a5829f28e5df474c5e
    token: JLTbmdPnDxFhuQXAYCmFJPIjluAHTwhL
    aes_key: rQsBSPB4f1e79AMKJfsLpGUkWKeCuOUfhNqliRxDtbu
    notify_url: https://paipan-cms-api.yaoyoumeng.com/v1/offiaccount
  miniprogram:
    app_id: wx4e699f1071dcc922
    secret: 31f1cdb3edb358d5a3f824af2501441b
wecom:
  corp_id: wwe6d350c03a123ca3
  doraemon:
    agent_id: 1000002
    secret: "c7uuQPaD40wGCsKs2ATARY7ulPCT2DGx4jFo_7sMK3A"
    token: "prpbUIVmFbyHn4MLbEUV1yv9qDVBOVK"
    aes_key: "B5DyvBxXdFCWecxbcxOAUsX3LXSPE7Hr9CsxEc1uvIn"
    callback_url: "https://paipan-cms-api.yaoyoumeng.com/v1/wecom/doraemon"
    oauth:
      callback: "https://paipan-cms-api.yaoyoumeng.com/v1/wecom/doraemon"
corona:
  endpoint:
    core: http://**************:5000
    bazi: https://horoscope.laibuyi.com
umeng:
  uverify:
    app_key: *********
    app_code: c4b95842f31d481e93d086779379f3dd
    app_secret: q4VsN15zSFSo2APufO07fwSFLEM6TOPF
    app:
      ios:
        key: 68383d87bc47b67d83767c32
        secret: ZxAPGnaY4OAgwADYfMzbh2hxy2kfyut3fB3H5RTKLtrExMF3LpNVlqK8ikrvRnwhXXY7snEQnBJ97Vbo9CqU+q/RU+qrILmrPOiHCCgpOB1pRbeAgAH7v0v0kNPaXkiB45BX+BjndVxLZ5qY7DzX5CTrKtZQyb8kghCXDfVXZaB6U3mj/3mzYlWk4uoucraveF4/zNPNxHPoTDXULQYvRVwWY+cd+QFSzYb1dTWOhziHtK3ghuFQS5SQwLcodJV/
      android:
        key: 68383d3679267e0210743c8c
        secret: VhJEefsMevIt3+gmW+PAnluFaBoJrsxv/ZleSJTcO2Hw5f5RqbYb1KOwzL/lyrd6vC4IevJPxb1aithQipE9ULVZHQ3Pmg6YON8qBfzzsheKtUGyrrAHq/HSd3R3onYIQZBftxNrIRBT7ZurVEPPb/uDNLui/gauVj3ZaZ9LnWVnM1jwGODNs2LvqpC6MsnYmkezaejUXt2ciRnmRbxvgxtgNSQ6OAn/dMGUzdSUhi0x4UkvINY0+8AkEkEpUmsCWe+tO+Sq7bno+uS5RNsPkHJ9OJupFx0dtW24Mb8yA2w=
